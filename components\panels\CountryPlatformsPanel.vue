<template>
  <section id="zhiwuwuyan" class="bg-white rounded-lg shadow-lg py-3 px-6 mt-4">
    <div class="mb-3">
      <!-- 国家/地区标题导航 -->
      <div class="flex items-center justify-between mb-3 border-b border-gray-200 pb-2">
        <div class="flex items-center space-x-6">
          <h3 
            @click="panelsStore.setCountryTab('other')"
            :class="[
              'text-base font-medium cursor-pointer pb-2 transition-all duration-200 relative',
              panelsStore.countryTab === 'other' 
                ? 'text-gray-800 border-b-3 border-orange-500' 
                : 'text-gray-500 hover:text-gray-700'
            ]"
          >
            其他
            <div v-if="panelsStore.countryTab === 'other'" class="absolute bottom-0 left-0 w-full h-0.5 bg-orange-500 rounded-full"></div>
          </h3>
          <h3 
            @click="panelsStore.setCountryTab('indonesia')"
            :class="[
              'text-base font-medium cursor-pointer pb-2 transition-all duration-200 relative',
              panelsStore.countryTab === 'indonesia' 
                ? 'text-gray-800 border-b-3 border-orange-500' 
                : 'text-gray-500 hover:text-gray-700'
            ]"
          >
            印尼
            <div v-if="panelsStore.countryTab === 'indonesia'" class="absolute bottom-0 left-0 w-full h-0.5 bg-orange-500 rounded-full"></div>
          </h3>
          <h3 
            @click="panelsStore.setCountryTab('malaysia')"
            :class="[
              'text-base font-medium cursor-pointer pb-2 transition-all duration-200 relative',
              panelsStore.countryTab === 'malaysia' 
                ? 'text-gray-800 border-b-3 border-orange-500' 
                : 'text-gray-500 hover:text-gray-700'
            ]"
          >
            马来西亚
            <div v-if="panelsStore.countryTab === 'malaysia'" class="absolute bottom-0 left-0 w-full h-0.5 bg-orange-500 rounded-full"></div>
          </h3>
          <h3 
            @click="panelsStore.setCountryTab('singapore')"
            :class="[
              'text-base font-medium cursor-pointer pb-2 transition-all duration-200 relative',
              panelsStore.countryTab === 'singapore' 
                ? 'text-gray-800 border-b-3 border-orange-500' 
                : 'text-gray-500 hover:text-gray-700'
            ]"
          >
            新加坡
            <div v-if="panelsStore.countryTab === 'singapore'" class="absolute bottom-0 left-0 w-full h-0.5 bg-orange-500 rounded-full"></div>
          </h3>
          <h3 
            @click="panelsStore.setCountryTab('vietnam')"
            :class="[
              'text-base font-medium cursor-pointer pb-2 transition-all duration-200 relative',
              panelsStore.countryTab === 'vietnam' 
                ? 'text-gray-800 border-b-3 border-orange-500' 
                : 'text-gray-500 hover:text-gray-700'
            ]"
          >
            越南
            <div v-if="panelsStore.countryTab === 'vietnam'" class="absolute bottom-0 left-0 w-full h-0.5 bg-orange-500 rounded-full"></div>
          </h3>
          <h3 
            @click="panelsStore.setCountryTab('philippines')"
            :class="[
              'text-base font-medium cursor-pointer pb-2 transition-all duration-200 relative',
              panelsStore.countryTab === 'philippines' 
                ? 'text-gray-800 border-b-3 border-orange-500' 
                : 'text-gray-500 hover:text-gray-700'
            ]"
          >
            菲律宾
            <div v-if="panelsStore.countryTab === 'philippines'" class="absolute bottom-0 left-0 w-full h-0.5 bg-orange-500 rounded-full"></div>
          </h3>
          <h3 
            @click="panelsStore.setCountryTab('thailand')"
            :class="[
              'text-base font-medium cursor-pointer pb-2 transition-all duration-200 relative',
              panelsStore.countryTab === 'thailand' 
                ? 'text-gray-800 border-b-3 border-orange-500' 
                : 'text-gray-500 hover:text-gray-700'
            ]"
          >
            泰国
            <div v-if="panelsStore.countryTab === 'thailand'" class="absolute bottom-0 left-0 w-full h-0.5 bg-orange-500 rounded-full"></div>
          </h3>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="min-h-[120px]">
        <div v-if="panelsStore.countryTab === 'other'" class="grid grid-cols-5 gap-3">
          <!-- Tokopedia -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-green-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">T</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Tokopedia</span>
          </div>

          <!-- L192 -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-red-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">L</span>
            </div>
            <span class="text-xs text-gray-700 text-center">L192</span>
          </div>

          <!-- Thisshop -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-orange-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">T</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Thisshop</span>
          </div>

          <!-- Akulaku -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-red-600 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">A</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Akulaku</span>
          </div>

          <!-- Zilingo -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-gray-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">Z</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Zilingo</span>
          </div>

          <!-- Tiki -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-blue-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">T</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Tiki</span>
          </div>
        </div>

        <div v-else-if="panelsStore.countryTab === 'indonesia'" class="grid grid-cols-5 gap-3">
          <!-- Tokopedia -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-green-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">T</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Tokopedia</span>
          </div>

          <!-- Bukalapak -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-red-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">B</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Bukalapak</span>
          </div>

          <!-- Blibli -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-blue-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">B</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Blibli</span>
          </div>

          <!-- Shopee Indonesia -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-orange-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">S</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Shopee</span>
          </div>

          <!-- Lazada Indonesia -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-purple-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">L</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Lazada</span>
          </div>
        </div>

        <div v-else-if="panelsStore.countryTab === 'malaysia'" class="grid grid-cols-5 gap-3">
          <!-- Lazada Malaysia -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-purple-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">L</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Lazada</span>
          </div>

          <!-- Shopee Malaysia -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-orange-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">S</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Shopee</span>
          </div>

          <!-- 11street -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-blue-600 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">11</span>
            </div>
            <span class="text-xs text-gray-700 text-center">11street</span>
          </div>

          <!-- PGMall -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-green-600 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">P</span>
            </div>
            <span class="text-xs text-gray-700 text-center">PGMall</span>
          </div>

          <!-- Mudah.my -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-yellow-600 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">M</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Mudah.my</span>
          </div>
        </div>

        <div v-else-if="panelsStore.countryTab === 'singapore'" class="grid grid-cols-5 gap-3">
          <!-- Shopee Singapore -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-orange-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">S</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Shopee</span>
          </div>

          <!-- Lazada Singapore -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-purple-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">L</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Lazada</span>
          </div>

          <!-- Qoo10 -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-blue-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">Q</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Qoo10</span>
          </div>

          <!-- Carousell -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-red-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">C</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Carousell</span>
          </div>

          <!-- RedMart -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-green-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">R</span>
            </div>
            <span class="text-xs text-gray-700 text-center">RedMart</span>
          </div>
        </div>

        <div v-else-if="panelsStore.countryTab === 'vietnam'" class="grid grid-cols-5 gap-3">
          <!-- Tiki -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-blue-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">T</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Tiki</span>
          </div>

          <!-- Sendo -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-red-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">S</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Sendo</span>
          </div>

          <!-- Shopee Vietnam -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-orange-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">S</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Shopee</span>
          </div>

          <!-- Lazada Vietnam -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-purple-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">L</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Lazada</span>
          </div>

          <!-- Adayroi -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-green-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">A</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Adayroi</span>
          </div>
        </div>

        <div v-else-if="panelsStore.countryTab === 'philippines'" class="grid grid-cols-5 gap-3">
          <!-- Shopee Philippines -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-orange-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">S</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Shopee</span>
          </div>

          <!-- Lazada Philippines -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-purple-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">L</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Lazada</span>
          </div>

          <!-- Zalora Philippines -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-black rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">Z</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Zalora</span>
          </div>

          <!-- OLX Philippines -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-blue-600 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">O</span>
            </div>
            <span class="text-xs text-gray-700 text-center">OLX</span>
          </div>

          <!-- Galleon -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-green-600 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">G</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Galleon</span>
          </div>
        </div>

        <div v-else-if="panelsStore.countryTab === 'thailand'" class="grid grid-cols-5 gap-3">
          <!-- Shopee Thailand -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-orange-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">S</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Shopee</span>
          </div>

          <!-- Lazada Thailand -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-purple-500 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">L</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Lazada</span>
          </div>

          <!-- JD Central -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-red-600 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">JD</span>
            </div>
            <span class="text-xs text-gray-700 text-center">JD Central</span>
          </div>

          <!-- Powerbuy -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-blue-600 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">P</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Powerbuy</span>
          </div>

          <!-- Advice -->
          <div class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-8 h-8 bg-green-600 rounded flex items-center justify-center mb-2">
              <span class="text-white text-xs font-bold">A</span>
            </div>
            <span class="text-xs text-gray-700 text-center">Advice</span>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { usePanelsStore } from '~/stores/panels'

const panelsStore = usePanelsStore()
</script>

<style scoped>
.transition-shadow {
  transition: box-shadow 0.3s ease;
}

.transition-all {
  transition: all 0.2s ease;
}
</style> 