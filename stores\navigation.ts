import { defineStore } from 'pinia'

export const useNavigationStore = defineStore('navigation', {
  state: () => ({
    activeSection: 'shopee-section',
    isNavSticky: false,
    navBarStyle: {
      top: '20px',
      left: '0px',
      width: '256px'
    },
    scrollTimeout: null as NodeJS.Timeout | null,
    
    // 导航项配置
    navigationItems: [
      { id: 'shopee-section', name: 'Shopee站点', href: '#shopee-section' },
      { id: 'tiktok-section', name: 'TikTok后台', href: '#tiktok-section' },
      { id: 'lazada-section', name: 'Lazada面板', href: '#lazada-section' },
      { id: 'tools-section', name: '常用工具', href: '#tools-section' },
      { id: 'custom-urls-section', name: '自定义网址', href: '#custom-urls-section' },
      { id: 'news-section', name: '跨境资讯', href: '#news-section' },
      { id: 'local-backend-section', name: '本土后台', href: '#local-backend-section' },
      { id: 'software-section', name: '综合软件', href: '#software-section' },
      { id: 'payment-section', name: '收款方式', href: '#payment-section' },
      { id: 'logistics-section', name: '东南亚物流', href: '#logistics-section' },
      { id: 'warehouse-section', name: '东南亚海外仓', href: '#warehouse-section' },
      { id: 'localization-section', name: '本地化服务', href: '#localization-section' },
      { id: 'registration-section', name: '本地商标&公司注册', href: '#registration-section' },
      { id: 'product-analysis-section', name: '选品分析', href: '#product-analysis-section' },
      { id: 'analysis-section', name: 'TikTok常用', href: '#analysis-section' },
      { id: 'market-analysis-section', name: '打包发货', href: '#market-analysis-section' },
      { id: 'marketing-section', name: '货源网站', href: '#marketing-section' },
      { id: 'report-section', name: '数据报告', href: '#report-section' },
      { id: 'other-section', name: '月度报告', href: '#other-section' },
      { id: 'zhiwuwuyan', name: '其他', href: '#zhiwuwuyan' },
      { id: 'market-data-section', name: '市场分析', href: '#market-data-section' }
    ]
  }),

  actions: {
    setActiveSection(sectionId: string) {
      this.activeSection = sectionId
    },

    // 动态计算偏移量
    getOffsetForSection() {
      const searchSection = document.querySelector('.search-section') as HTMLElement
      const timeSection = document.querySelector('.mb-3.p-2.bg-gray-100.rounded-lg.border.border-gray-200') as HTMLElement
      
      let offset = 100 // 基础偏移量，稍微减小
      
      if (searchSection) {
        offset += searchSection.offsetHeight
      }
      
      if (timeSection) {
        offset += timeSection.offsetHeight
      }
      
      // 如果导航栏固定，需要考虑固定导航栏的高度
      if (this.isNavSticky) {
        const navElement = document.querySelector('.nav-container') as HTMLElement
        if (navElement) {
          offset += navElement.offsetHeight
        }
      }
      
      return offset
    },

    initializeNavPosition() {
         this.updateNavBarPosition(window.scrollY)
    },

    updateNavBarPosition(scrollY: number) {
      // 获取关键元素
      const searchSection = document.querySelector('.search-section') as HTMLElement
      const mainContainer = document.getElementById('main-container') as HTMLElement
      const footer = document.querySelector('footer') as HTMLElement
      const navElement = document.querySelector('.nav-container') as HTMLElement
      
      if (!searchSection || !mainContainer || !footer) return
      
      // 关键参数
      const triggerPoint = searchSection.offsetHeight + 50 // 开始固定的滚动位置
      const navHeight = navElement ? navElement.offsetHeight : 500 // 导航高度
      const topMargin = 20 // 与浏览器顶部的间距
      const bottomMargin = 20 // 与footer的间距
      
      // 获取容器位置
      const containerRect = mainContainer.getBoundingClientRect()
      const containerLeft = Math.max(containerRect.left + 16, 16)
      
      if (scrollY < triggerPoint) {
        // 状态1：相对定位 - 导航跟随页面滚动
        this.isNavSticky = false
        this.navBarStyle = {
          top: '0px',
          left: '0px',
          width: '256px'
        }
      } else {
        // 状态2：固定定位 - 智能计算位置，允许底部时部分消失
        this.isNavSticky = true
        
        // 获取footer在视口中的位置
        const footerRect = footer.getBoundingClientRect()
        const footerTopInViewport = footerRect.top
        
        // 计算导航应该的位置
        let finalTop: number
        
        if (footerTopInViewport > navHeight + bottomMargin + topMargin) {
          // 情况1：footer距离足够远 - 导航固定在浏览器顶部
          finalTop = topMargin
        } else {
          // 情况2：footer接近或已进入视口 - 导航底部对齐footer顶部
          // 允许导航顶部超出视口上方，实现部分消失效果
          finalTop = footerTopInViewport - navHeight - bottomMargin
          
          // 注意：这里不设置最小值限制，允许导航向上消失
          // 这样可以确保footer区域完全可见，不被导航遮挡
        }
        
        this.navBarStyle = {
          top: `${finalTop}px`,
          left: `${containerLeft}px`,
          width: '256px'
        }
      }
    },

    scrollToSection(sectionId: string) {
      const element = document.getElementById(sectionId)
      if (element) {
        const elementTop = element.offsetTop
        
        // 调试信息
        console.log('Navigation Debug:', {
          sectionId,
          elementTop,
          targetScrollPosition: elementTop,
          elementHeight: element.offsetHeight
        })
        
        // 立即设置选中状态
        this.setActiveSection(sectionId)
        
        // 直接滚动到面板顶部，不使用偏移量
        window.scrollTo({
          top: elementTop,
          behavior: 'smooth'
        })
      } else {
        console.warn('Section not found:', sectionId)
      }
    },

    handleScroll() {
      if (this.scrollTimeout) {
        clearTimeout(this.scrollTimeout)
      }
      
      // 立即更新导航位置
      this.updateNavBarPosition(window.scrollY)
      
      // 添加防抖机制进行section检测
      this.scrollTimeout = setTimeout(() => {
        const sections = this.navigationItems.map(item => item.id)
        const scrollY = window.scrollY
        const viewportHeight = window.innerHeight
        
        // 找到最接近浏览器顶部的section
        let currentSection = sections[0]
        let minDistance = Infinity
        
        for (let i = 0; i < sections.length; i++) {
          const element = document.getElementById(sections[i])
          if (element) {
            const elementTop = element.offsetTop
            
            // 计算section顶部到浏览器顶部的距离
            const distanceFromTop = elementTop - scrollY
            
            // 如果section在视口中或刚刚离开视口顶部，且距离最近
            if (distanceFromTop <= 200) { // 允许200px的容差
              const absDistance = Math.abs(distanceFromTop)
              if (absDistance < minDistance) {
                minDistance = absDistance
                currentSection = sections[i]
              }
            }
          }
        }
        
        // 如果没有找到合适的section，选择第一个
        if (minDistance === Infinity) {
          currentSection = sections[0]
        }
        
        // 调试信息
        console.log('Scroll Debug:', {
          scrollY,
          currentSection,
          activeSection: this.activeSection,
          minDistance,
          sections: sections.map(id => {
            const el = document.getElementById(id)
            const distance = el ? el.offsetTop - scrollY : null
            return {
              id,
              offsetTop: el?.offsetTop,
              distance,
              absDistance: distance ? Math.abs(distance) : null,
              inRange: distance ? (distance <= 200) : false
            }
          }).filter(section => section.inRange) // 只显示在范围内的section
        })
        
        // 只有当找到的section与当前activeSection不同时才更新
        if (currentSection !== this.activeSection) {
          this.setActiveSection(currentSection)
        }
      }, 50) // 50ms防抖
    }
  }
}) 