<template>
  <section id="tiktok-section" class="bg-white rounded-lg shadow-lg py-3 px-6 mt-4">
    <div class="mb-3">
      <h3 class="text-base font-medium text-gray-800 mb-2 pb-2 border-b border-gray-200">TikTok常用</h3>
      <!-- TikTok链接网格 -->
      <div class="space-y-1">
        <!-- 第一行 -->
        <div class="grid grid-cols-5 gap-1.5">
          <!-- 印尼后台 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-black rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">抖</span>
            </div>
            <span class="text-sm text-gray-700">印尼后台</span>
          </div>

          <!-- 菲律宾后台 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-black rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">抖</span>
            </div>
            <span class="text-sm text-gray-700">菲律宾后台</span>
          </div>

          <!-- 马来后台 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-black rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">抖</span>
            </div>
            <span class="text-sm text-gray-700">马来后台</span>
          </div>

          <!-- 泰国后台 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-black rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">抖</span>
            </div>
            <span class="text-sm text-gray-700">泰国后台</span>
          </div>
          
          <!-- 越南后台 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-black rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">抖</span>
            </div>
            <span class="text-sm text-gray-700">越南后台</span>
          </div>
        </div>

        <!-- 第二行 -->
        <div class="grid grid-cols-5 gap-1.5">
          <!-- 新加坡后台 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-black rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">抖</span>
            </div>
            <span class="text-sm text-gray-700">新加坡后台</span>
          </div>

          <!-- 跨境后台 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-black rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">跨</span>
            </div>
            <span class="text-sm text-gray-700">跨境后台</span>
          </div>

          <!-- PC前台 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-blue-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">PC</span>
            </div>
            <span class="text-sm text-gray-700">PC前台</span>
          </div>
          
          <!-- 广告管理平台 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-red-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">广</span>
            </div>
            <span class="text-sm text-gray-700">广告管理平台</span>
          </div>

          <!-- TikTok知识大纲 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-black rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">T</span>
            </div>
            <span class="text-sm text-gray-700">TikTok知识大纲</span>
          </div>
        </div>

        <!-- 第三行 -->
        <div class="grid grid-cols-5 gap-1.5">
          <!-- TikTok入驻 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-red-600 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">TT</span>
            </div>
            <span class="text-sm text-gray-700">TikTok入驻</span>
          </div>

          <!-- 达人广场 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-purple-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">达</span>
            </div>
            <span class="text-sm text-gray-700">达人广场</span>
          </div>
          
          <!-- 更多功能占位 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-gray-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">+</span>
            </div>
            <span class="text-sm text-gray-700">更多功能</span>
          </div>
          
          <!-- 空占位 -->
          <div class="invisible flex items-center px-1.5 py-1">
            <div class="w-4 h-4 rounded flex items-center justify-center mr-1.5 flex-shrink-0"></div>
            <span class="text-sm text-gray-700"></span>
          </div>
          
          <!-- 空占位 -->
          <div class="invisible flex items-center px-1.5 py-1">
            <div class="w-4 h-4 rounded flex items-center justify-center mr-1.5 flex-shrink-0"></div>
            <span class="text-sm text-gray-700"></span>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// TikTok面板组件
// 包含12个TikTok相关链接：后台入口、广告平台、知识大纲等
</script>

<style scoped>
.transition-colors {
  transition: background-color 0.2s ease;
}
</style> 