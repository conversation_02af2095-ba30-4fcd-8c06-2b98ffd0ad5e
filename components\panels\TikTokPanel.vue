<template>
  <section id="tiktok-section" class="bg-white rounded-lg shadow-lg py-3 px-6 mt-4">
    <div class="mb-3">
      <h3 class="text-base font-medium text-gray-800 mb-2 pb-2 border-b border-gray-200">TikTok常用</h3>
      <!-- TikTok链接网格 -->
      <div class="space-y-1">
        <!-- 第一行 -->
        <div class="grid grid-cols-5 gap-1.5">
          <!-- TK专用网络节点 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-red-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">TK</span>
            </div>
            <span class="text-sm text-gray-700">TK专用网络节点</span>
          </div>

          <!-- OBS -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-gray-800 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">O</span>
            </div>
            <span class="text-sm text-gray-700">OBS</span>
          </div>

          <!-- TK验约达人神器 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-pink-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">验</span>
            </div>
            <span class="text-sm text-gray-700">TK验约达人神器</span>
          </div>

          <!-- TK直播伴侣【外】 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-red-600 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">直</span>
            </div>
            <span class="text-sm text-gray-700">TK直播伴侣【外】</span>
          </div>

          <!-- 伪装度检测 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-cyan-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">伪</span>
            </div>
            <span class="text-sm text-gray-700">伪装度检测</span>
          </div>
        </div>

        <!-- 第二行 -->
        <div class="grid grid-cols-5 gap-1.5">
          <!-- TK短视频标签查询 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-amber-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">标</span>
            </div>
            <span class="text-sm text-gray-700">TK短视频标签查询</span>
          </div>

          <!-- 入驻与注册指南 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-black rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">入</span>
            </div>
            <span class="text-sm text-gray-700">入驻与注册指南</span>
          </div>

          <!-- TK数据分析工具 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-blue-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">数</span>
            </div>
            <span class="text-sm text-gray-700">TK数据分析工具</span>
          </div>

          <!-- TK音乐库 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-green-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">音</span>
            </div>
            <span class="text-sm text-gray-700">TK音乐库</span>
          </div>

          <!-- 视频编辑工具 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-purple-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">视</span>
            </div>
            <span class="text-sm text-gray-700">视频编辑工具</span>
          </div>
        </div>

        <!-- 第三行 -->
        <div class="grid grid-cols-5 gap-1.5">
          <!-- TK移动端工具 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-orange-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">移</span>
            </div>
            <span class="text-sm text-gray-700">TK移动端工具</span>
          </div>

          <!-- 更多TK工具 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-teal-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">+</span>
            </div>
            <span class="text-sm text-gray-700">更多TK工具</span>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// TikTok面板组件
// 包含TikTok相关工具和链接
</script>

<style scoped>
.transition-colors {
  transition: background-color 0.2s ease;
}
</style> 