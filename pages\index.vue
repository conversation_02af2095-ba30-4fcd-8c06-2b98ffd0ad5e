<template>
  <div>
    <!-- 搜索区域 -->
    <SearchSection />

    <!-- 主要内容区域 -->
    <div class="py-4">
      <!-- 整体居中容器 -->
      <div id="main-container" class="max-w-[1600px] mx-auto px-4">
        <!-- 左侧导航 + 主内容的 Flex 容器 -->
        <div class="flex gap-0 relative">
          <!-- 左侧导航区域 -->
          <div class="w-40 flex-shrink-0 relative">
            <!-- 导航占位元素，防止布局跳跃 -->
            <div 
              v-if="navigationStore?.isNavSticky" 
              class="w-full bg-transparent"
              style="height: 20px;"
            ></div>
            <SideNavigation />
          </div>
          
          <!-- 主内容区域 -->
          <div class="flex-1 min-w-0">
            <MainContent>
              <!-- Shopee面板 -->
              <ShopeePanel v-if="panelsStore.isVisible('shopee-section')" />
    
              <!-- TikTok面板 -->
              <TikTokPanel v-if="panelsStore.isVisible('tiktok-section')" />
              
              <!-- Lazada面板 -->
              <LazadaPanel v-if="panelsStore.isVisible('lazada-section')" />
              
              <!-- 节假日倒计时面板 -->
              <FestivalCountdown />
              
              <!-- 常用工具面板 -->
              <ToolsPanel v-if="panelsStore.isVisible('tools-section')" />
              
              <!-- 其他面板组件 -->
              <div>
                <CustomUrlsPanel v-if="panelsStore.isVisible('custom-urls-section')" />
                <ExchangeRatePanel v-if="panelsStore.isVisible('exchange-rate-section')" />
              </div>
              <NewsPanel v-if="panelsStore.isVisible('news-section')" />
              <LocalBackendPanel v-if="panelsStore.isVisible('local-backend-section')" />
              <SoftwarePanel v-if="panelsStore.isVisible('software-section')" />
              <PaymentPanel v-if="panelsStore.isVisible('payment-section')" />
              <LogisticsPanel v-if="panelsStore.isVisible('logistics-section')" />
              <WarehousePanel v-if="panelsStore.isVisible('warehouse-section')" />
              <LocalizationPanel v-if="panelsStore.isVisible('localization-section')" />
              <RegistrationPanel v-if="panelsStore.isVisible('registration-section')" />
              <ProductAnalysisPanel v-if="panelsStore.isVisible('product-analysis-section')" />
              <AnalysisPanel v-if="panelsStore.isVisible('tiktok-tools-section')" />
              <PackingPanel v-if="panelsStore.isVisible('packing-section')" />
              <MarketingPanel v-if="panelsStore.isVisible('sourcing-section')" />
              <ReportPanel v-if="panelsStore.isVisible('data-report-section')" />
              <MonthlyReportPanel v-if="panelsStore.isVisible('monthly-report-section')" />
              <OtherPanel v-if="panelsStore.isVisible('other-platforms-section')" />
              <MarketDataPanel v-if="panelsStore.isVisible('market-data-section')" />
            </MainContent>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { usePanelsStore } from '~/stores/panels'
import { useNavigationStore } from '~/stores/navigation'
import { onMounted, nextTick } from 'vue'
import SearchSection from '~/components/SearchSection.vue'
import SideNavigation from '~/components/SideNavigation.vue'
import MainContent from '~/components/MainContent.vue'
import ShopeePanel from '~/components/panels/ShopeePanel.vue'
import TikTokPanel from '~/components/panels/TikTokPanel.vue'
import ToolsPanel from '~/components/panels/ToolsPanel.vue'
import LazadaPanel from '~/components/panels/LazadaPanel.vue'
import FestivalCountdown from '~/components/FestivalCountdown.vue'
import CustomUrlsPanel from '~/components/panels/CustomUrlsPanel.vue'
import ExchangeRatePanel from '~/components/panels/ExchangeRatePanel.vue'
import NewsPanel from '~/components/panels/NewsPanel.vue'
import LocalBackendPanel from '~/components/panels/LocalBackendPanel.vue'
import SoftwarePanel from '~/components/panels/SoftwarePanel.vue'
import PaymentPanel from '~/components/panels/PaymentPanel.vue'
import LogisticsPanel from '~/components/panels/LogisticsPanel.vue'
import WarehousePanel from '~/components/panels/WarehousePanel.vue'
import LocalizationPanel from '~/components/panels/LocalizationPanel.vue'
import RegistrationPanel from '~/components/panels/RegistrationPanel.vue'
import ProductAnalysisPanel from '~/components/panels/ProductAnalysisPanel.vue'
import AnalysisPanel from '~/components/panels/AnalysisPanel.vue'
import PackingPanel from '~/components/panels/PackingPanel.vue'
import MarketDataPanel from '~/components/panels/MarketDataPanel.vue'
import MarketingPanel from '~/components/panels/MarketingPanel.vue'
import ReportPanel from '~/components/panels/ReportPanel.vue'
import MonthlyReportPanel from '~/components/panels/MonthlyReportPanel.vue'
import OtherPanel from '~/components/panels/OtherPanel.vue'

const panelsStore = usePanelsStore()
const navigationStore = useNavigationStore()

// 设置页面标题
useHead({
  title: 'DNY123 - 东南亚跨境电商导航'
})

// 页面加载完成后初始化导航位置
onMounted(() => {
  nextTick(() => {
    navigationStore.initializeNavPosition()
  })
})
</script>

<style scoped>
/* 主页面样式 */
.min-h-screen {
  min-height: 100vh;
}
</style> 