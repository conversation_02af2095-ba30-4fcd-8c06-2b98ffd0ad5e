{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxtjs/tailwindcss": "^6.14.0", "@pinia/nuxt": "^0.11.0", "autoprefixer": "^10.4.21", "nuxt": "^3.17.3", "nuxt-app": "file:", "pinia": "^3.0.2", "postcss": "^8.5.3", "tailwindcss": "^3.3.0", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}