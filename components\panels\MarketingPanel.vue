<template>
  <section id="sourcing-section" class="bg-white rounded-lg shadow-lg py-3 px-6 mt-4">
    <div class="mb-3">
      <h3 class="text-base font-medium text-gray-800 mb-2 pb-2 border-b border-gray-200">货源网站</h3>
      <!-- 货源网站网格 -->
      <div class="space-y-1">
        <!-- 第一行 -->
        <div class="grid grid-cols-5 gap-1.5">
          <!-- 1688 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-orange-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">1</span>
            </div>
            <span class="text-sm text-gray-700">1688</span>
          </div>

          <!-- 51货源 东南亚爆款 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-orange-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">51</span>
            </div>
            <span class="text-sm text-gray-700">51货源 东南亚爆款</span>
          </div>

          <!-- 万跨供应链 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-gray-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">万</span>
            </div>
            <span class="text-sm text-gray-700">万跨供应链</span>
          </div>

          <!-- 中国制造 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-red-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">中</span>
            </div>
            <span class="text-sm text-gray-700">中国制造</span>
          </div>

          <!-- 淘宝网 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-orange-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">淘</span>
            </div>
            <span class="text-sm text-gray-700">淘宝网</span>
          </div>
        </div>

        <!-- 第二行 -->
        <div class="grid grid-cols-5 gap-1.5">
          <!-- 阿里国际 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-orange-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">阿</span>
            </div>
            <span class="text-sm text-gray-700">阿里国际</span>
          </div>

          <!-- 购途网GO2 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-blue-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">GO</span>
            </div>
            <span class="text-sm text-gray-700">购途网GO2</span>
          </div>

          <!-- CJDropshipping -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-orange-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">CJ</span>
            </div>
            <span class="text-sm text-gray-700">CJDropshipping</span>
          </div>

          <!-- 一起做网店 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-pink-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">一</span>
            </div>
            <span class="text-sm text-gray-700">一起做网店</span>
          </div>

          <!-- 搜款网 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-red-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">搜</span>
            </div>
            <span class="text-sm text-gray-700">搜款网</span>
          </div>
        </div>

        <!-- 第三行 -->
        <div class="grid grid-cols-5 gap-1.5">
          <!-- 网商园 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-blue-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">网</span>
            </div>
            <span class="text-sm text-gray-700">网商园</span>
          </div>

          <!-- 义乌购 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-yellow-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">义</span>
            </div>
            <span class="text-sm text-gray-700">义乌购</span>
          </div>

          <!-- 批发户 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-gray-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">批</span>
            </div>
            <span class="text-sm text-gray-700">批发户</span>
          </div>

          <!-- 西之月全球货盘 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-purple-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">西</span>
            </div>
            <span class="text-sm text-gray-700">西之月全球货盘</span>
          </div>

          <!-- SDS定制 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-blue-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">S</span>
            </div>
            <span class="text-sm text-gray-700">SDS定制</span>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// 货源网站面板组件
</script>

<style scoped>
.transition-colors {
  transition: background-color 0.2s ease;
}
</style>