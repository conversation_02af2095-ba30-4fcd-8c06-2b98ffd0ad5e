<template>
  <section id="localization-section" class="bg-white rounded-lg shadow-lg py-3 px-6 mt-4">
    <div class="mb-3">
      <h3 class="text-base font-medium text-gray-800 mb-2 pb-2 border-b border-gray-200">本地化服务</h3>
      <!-- 本地化服务网格 -->
      <div class="grid grid-cols-6 gap-3">
        <!-- 印尼靠谱本土资源 -->
        <div class="software-item p-2 rounded-lg hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-red-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">印</span>
            </div>
            <h4 class="software-title text-sm font-medium text-gray-800">印尼靠谱本土资源</h4>
          </div>
          <p class="software-desc text-xs text-gray-600 line-clamp-2">印尼本土电商服务资源</p>
        </div>
        
        <!-- DNYECS品牌出海 -->
        <div class="software-item p-2 rounded-lg hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-blue-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">D</span>
            </div>
            <h4 class="software-title text-sm font-medium text-gray-800">DNYECS品牌出海</h4>
          </div>
          <p class="software-desc text-xs text-gray-600 line-clamp-2">东南亚全面品牌出海服务</p>
        </div>
        
        <!-- 跨海跨境 -->
        <div class="software-item p-2 rounded-lg hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-blue-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">跨</span>
            </div>
            <h4 class="software-title text-sm font-medium text-gray-800">跨海跨境</h4>
          </div>
          <p class="software-desc text-xs text-gray-600 line-clamp-2">欧美、东南亚综合服务商</p>
        </div>
        
        <!-- 中国台湾虾皮本土资源 -->
        <div class="software-item p-2 rounded-lg hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-orange-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">台</span>
            </div>
            <h4 class="software-title text-sm font-medium text-gray-800">中国台湾虾皮本土资源</h4>
          </div>
          <p class="software-desc text-xs text-gray-600 line-clamp-2">中国台湾地区本土服务商</p>
        </div>
        
        <!-- 本地认证资料汇总 -->
        <div class="software-item p-2 rounded-lg hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-green-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">认</span>
            </div>
            <h4 class="software-title text-sm font-medium text-gray-800">本地认证资料汇总</h4>
          </div>
          <p class="software-desc text-xs text-gray-600 line-clamp-2">各国认证资料综合服务</p>
        </div>
        
        <!-- 菲律宾本土服务 -->
        <div class="software-item p-2 rounded-lg hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-purple-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">菲</span>
            </div>
            <h4 class="software-title text-sm font-medium text-gray-800">菲律宾本土服务</h4>
          </div>
          <p class="software-desc text-xs text-gray-600 line-clamp-2">菲律宾电商本地化服务</p>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// 本地化服务面板组件
</script>

<style scoped>
/* 行文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  box-orient: vertical;
  overflow: hidden;
}

/* 统一的软件项目hover效果 */
.software-item {
  transition: all 0.2s ease-in-out;
}

.software-item:hover {
  background-color: #dbeafe !important;
}

.software-item:hover .software-title {
  color: #f97316 !important;
}

.software-item:hover .software-desc {
  color: #111827 !important;
}

/* 通用group hover效果 - 适用于所有软件项目 */
.group {
  transition: all 0.2s ease-in-out;
}

.group:hover {
  background-color: #dbeafe !important;
}

.group:hover h4 {
  color: #f97316 !important;
}

.group:hover p {
  color: #111827 !important;
}
</style> 