<template>
  <div class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
    <img :src="activity.banner" :alt="activity.title" class="w-full h-48 object-cover">
    <div class="p-6">
      <h3 class="text-2xl font-semibold text-gray-800 mb-2">{{ activity.title }}</h3>
      <p class="text-gray-600 mb-4">{{ activity.description }}</p>
      <div class="text-sm text-gray-500">
        <p>开始时间: {{ activity.startDate }}</p>
        <p>结束时间: {{ activity.endDate }}</p>
      </div>
      <button class="mt-4 bg-orange-600 hover:bg-orange-700 text-white font-medium px-4 py-2 rounded">
        {{ buttonText }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Activity {
  id: number
  title: string
  banner: string
  description: string
  startDate: string
  endDate: string
}

// 组件参数定义
const props = withDefaults(defineProps<{
  activity: Activity
  buttonText?: string
}>(), {
  buttonText: '参与活动'
})
</script> 