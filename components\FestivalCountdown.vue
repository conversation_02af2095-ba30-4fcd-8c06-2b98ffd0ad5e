<template>
  <section class="bg-gray-100 rounded-lg border border-gray-200 p-2">
    <div class="flex items-center justify-between w-full">
      <!-- 左侧：年周数和距2026年天数 -->
      <div class="text-xs text-gray-600 flex-shrink-0">
        {{ year }}年第<span class="text-orange-600">{{ weekNumber }}</span>周，距2026年还有<span class="text-orange-600">{{ daysTo2026 }}</span>天
      </div>
      <!-- 右侧：节日倒计时 -->
      <div class="flex items-center space-x-8 flex-1 justify-end">
        <span v-for="festival in panelsStore.festivals" :key="festival.name" class="text-xs text-gray-600">
          距 <span class="text-blue-600">{{ festival.name }}</span> 还有 <span :class="festival.color === 'red' ? 'text-orange-600' : 'text-orange-600'">{{ panelsStore.getDaysUntil(festival.date) }}</span> 天
        </span>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import { usePanelsStore } from '~/stores/panels'

const panelsStore = usePanelsStore()
let timeInterval: NodeJS.Timeout | null = null

// 年、周数、距2026年天数
const year = 2025
const weekNumber = ref(1)
const daysTo2026 = ref(0)

function getWeekNumber(date: Date) {
  // 以每年1月1日为第一周
  const start = new Date(date.getFullYear(), 0, 1)
  const diff = date.getTime() - start.getTime()
  return Math.floor(diff / (1000 * 60 * 60 * 24 * 7)) + 1
}

function getDaysTo2026(date: Date) {
  const target = new Date('2026-01-01')
  const diff = target.getTime() - date.getTime()
  return Math.ceil(diff / (1000 * 60 * 60 * 24))
}

function updateInfo() {
  const now = new Date()
  weekNumber.value = getWeekNumber(now)
  daysTo2026.value = getDaysTo2026(now)
}

onMounted(() => {
  updateInfo()
  panelsStore.updateCurrentTime()
  timeInterval = setInterval(() => {
    updateInfo()
    panelsStore.updateCurrentTime()
  }, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
.flex-1 {
  flex: 1 1 0%;
}
</style> 