<template>
  <section id="warehouse-section" class="bg-white rounded-lg shadow-lg py-3 px-6 mt-4">
    <div class="mb-3">
      <div class="flex items-center justify-between mb-2 pb-2 border-b border-gray-200">
        <h3 class="text-base font-medium text-gray-800">东南亚海外仓</h3>
        <a href="#" class="text-blue-600 hover:text-blue-800 text-xs">更多 ></a>
      </div>
      
      <!-- 海外仓公司网格 -->
      <div class="grid grid-cols-6 gap-2 space-y-1">
        <!-- 元仓海外仓 -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-red-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">元</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">元仓海外仓</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">越南、泰国、马来西亚、印尼、菲律宾</p>
        </div>

        <!-- 猴仓 -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-blue-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">猴</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">猴仓</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">马来、菲律宾、泰国、印尼、越南</p>
        </div>

        <!-- 易速菲海外仓 -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-red-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">易</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">易速菲海外仓</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">马来、菲律宾、泰国、印尼、越南</p>
        </div>

        <!-- 海仓国际 -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-blue-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">海</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">海仓国际</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">泰国、马来、菲律宾、印尼、越南</p>
        </div>

        <!-- 万邑通海外仓 -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-green-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">万</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">万邑通海外仓</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">全球海外仓网络</p>
        </div>

        <!-- 递四方海外仓 -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-purple-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">递</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">递四方海外仓</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">东南亚主要市场</p>
        </div>

        <!-- 纵腾海外仓 -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-teal-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">纵</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">纵腾海外仓</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">泰国、马来、菲律宾、印尼、越南</p>
        </div>

        <!-- 速卖通海外仓 -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-orange-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">速</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">速卖通海外仓</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">全球海外仓服务</p>
        </div>

        <!-- 燕文海外仓 -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-indigo-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">燕</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">燕文海外仓</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">东南亚专业海外仓</p>
        </div>

        <!-- 斑马海外仓 -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-gray-700 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">斑</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">斑马海外仓</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">智能仓储解决方案</p>
        </div>

        <!-- 中外运海外仓 -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-red-700 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">中</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">中外运海外仓</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">国际物流领导品牌</p>
        </div>

        <!-- 更多海外仓 -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-gray-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">+</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">更多海外仓</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">查看更多海外仓服务</p>
        </div>
      </div>
    </div>
  </section>
</template>
    </div>
  </section>
</template>

<script setup lang="ts">
// 东南亚海外仓面板组件
</script>

<style scoped>
.transition-shadow {
  transition: box-shadow 0.3s ease;
}
</style> 