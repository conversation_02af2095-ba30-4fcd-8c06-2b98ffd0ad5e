<template>
  <section id="registration-section" class="bg-white rounded-lg shadow-lg py-3 px-6 mt-4">
    <div class="mb-3">
      <h3 class="text-base font-medium text-gray-800 mb-2 pb-2 border-b border-gray-200">本地商标&公司注册</h3>
      <!-- 注册服务网格 -->
      <div class="grid grid-cols-6 gap-3">
        <!-- 开云出海 -->
        <div class="software-item p-2 rounded-lg hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-orange-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">开</span>
            </div>
            <h4 class="software-title text-sm font-medium text-gray-800">开云出海</h4>
          </div>
          <p class="software-desc text-xs text-gray-600 line-clamp-2">菲律宾、泰国、印尼、马来、越南、缅甸</p>
        </div>
        
        <!-- 安合出海 -->
        <div class="software-item p-2 rounded-lg hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-blue-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">安</span>
            </div>
            <h4 class="software-title text-sm font-medium text-gray-800">安合出海</h4>
          </div>
          <p class="software-desc text-xs text-gray-600 line-clamp-2">安合出海，为东南亚国家提供各国商标申请服务</p>
        </div>
        
        <!-- e-Eyes Compliance -->
        <div class="software-item p-2 rounded-lg hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-gray-700 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">e</span>
            </div>
            <h4 class="software-title text-sm font-medium text-gray-800">e-Eyes Compliance</h4>
          </div>
          <p class="software-desc text-xs text-gray-600 line-clamp-2">马来西亚/印度尼西亚，认证和合规</p>
        </div>
        
        <!-- 大熊财税 -->
        <div class="software-item p-2 rounded-lg hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-gray-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">大</span>
            </div>
            <h4 class="software-title text-sm font-medium text-gray-800">大熊财税</h4>
          </div>
          <p class="software-desc text-xs text-gray-600 line-clamp-2">东南亚本土财税服务</p>
        </div>

        <!-- 文菱商标注册 -->
        <div class="software-item p-2 rounded-lg hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-purple-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">文</span>
            </div>
            <h4 class="software-title text-sm font-medium text-gray-800">文菱商标注册</h4>
          </div>
          <p class="software-desc text-xs text-gray-600 line-clamp-2">东南亚全区域商标注册服务</p>
        </div>

        <!-- 虎鲸注册 -->
        <div class="software-item p-2 rounded-lg hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-blue-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">虎</span>
            </div>
            <h4 class="software-title text-sm font-medium text-gray-800">虎鲸注册</h4>
          </div>
          <p class="software-desc text-xs text-gray-600 line-clamp-2">一站式海外公司注册与商标服务</p>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// 商标注册面板组件
</script>

<style scoped>
/* 行文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  box-orient: vertical;
  overflow: hidden;
}

/* 统一的软件项目hover效果 */
.software-item {
  transition: all 0.2s ease-in-out;
}

.software-item:hover {
  background-color: #dbeafe !important;
}

.software-item:hover .software-title {
  color: #f97316 !important;
}

.software-item:hover .software-desc {
  color: #111827 !important;
}

/* 通用group hover效果 - 适用于所有软件项目 */
.group {
  transition: all 0.2s ease-in-out;
}

.group:hover {
  background-color: #dbeafe !important;
}

.group:hover h4 {
  color: #f97316 !important;
}

.group:hover p {
  color: #111827 !important;
}
</style> 