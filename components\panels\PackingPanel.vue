<template>
  <section id="market-analysis-section" class="bg-white rounded-lg shadow-lg py-3 px-6 mt-4">
    <div class="mb-3">
      <h3 class="text-base font-medium text-gray-800 mb-2 pb-2 border-b border-gray-200">打包发货</h3>
      <!-- 打包发货服务商网格 -->
      <div class="space-y-1">
        <!-- 第一行 -->
        <div class="grid grid-cols-5 gap-1.5">
          <!-- 虾皮台湾货代 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-red-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">虾</span>
            </div>
            <span class="text-sm text-gray-700">虾皮台湾货代</span>
          </div>

          <!-- 货小易 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-blue-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">货</span>
            </div>
            <span class="text-sm text-gray-700">货小易</span>
          </div>

          <!-- 泉州中弘达云仓 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-orange-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">泉</span>
            </div>
            <span class="text-sm text-gray-700">泉州中弘达云仓</span>
          </div>

          <!-- 东南亚货代/海外仓 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-green-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">东</span>
            </div>
            <span class="text-sm text-gray-700">东南亚货代/海外仓</span>
          </div>

          <!-- 快工云仓 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-cyan-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">快</span>
            </div>
            <span class="text-sm text-gray-700">快工云仓</span>
          </div>
        </div>

        <!-- 第二行 -->
        <div class="grid grid-cols-5 gap-1.5">
          <!-- 境捷一仓 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-purple-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">境</span>
            </div>
            <span class="text-sm text-gray-700">境捷一仓</span>
          </div>

          <!-- Lazada100 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-blue-600 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">L</span>
            </div>
            <span class="text-sm text-gray-700">Lazada100</span>
          </div>

          <!-- 嘉吉猫代贴单 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-yellow-600 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">嘉</span>
            </div>
            <span class="text-sm text-gray-700">嘉吉猫代贴单</span>
          </div>

          <!-- 即达代发货 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-red-600 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">即</span>
            </div>
            <span class="text-sm text-gray-700">即达代发货</span>
          </div>

          <!-- 微笑仓储 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-pink-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">微</span>
            </div>
            <span class="text-sm text-gray-700">微笑仓储</span>
          </div>
        </div>

        <!-- 第三行 -->
        <div class="grid grid-cols-5 gap-1.5">
          <!-- 飞马云仓 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-gray-600 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">飞</span>
            </div>
            <span class="text-sm text-gray-700">飞马云仓</span>
          </div>

          <!-- 东方云仓 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-blue-700 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">东</span>
            </div>
            <span class="text-sm text-gray-700">东方云仓</span>
          </div>

          <!-- 汇达云仓 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-indigo-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">汇</span>
            </div>
            <span class="text-sm text-gray-700">汇达云仓</span>
          </div>

          <!-- 鑫标代打包 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-teal-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">鑫</span>
            </div>
            <span class="text-sm text-gray-700">鑫标代打包</span>
          </div>

          <!-- 格文代发 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-emerald-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">格</span>
            </div>
            <span class="text-sm text-gray-700">格文代发</span>
          </div>
        </div>

        <!-- 第四行 -->
        <div class="grid grid-cols-5 gap-1.5">
          <!-- 义乌飞碟云仓 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-violet-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">义</span>
            </div>
            <span class="text-sm text-gray-700">义乌飞碟云仓</span>
          </div>

          <!-- 更多服务商 -->
          <div class="flex items-center px-1.5 py-1 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div class="w-4 h-4 bg-gray-500 rounded flex items-center justify-center mr-1.5 flex-shrink-0">
              <span class="text-white text-xs font-bold">+</span>
            </div>
            <span class="text-sm text-gray-700">更多服务商</span>
          </div>

          <!-- 空占位 -->
          <div class="invisible flex items-center px-1.5 py-1">
            <div class="w-4 h-4 rounded flex items-center justify-center mr-1.5 flex-shrink-0"></div>
            <span class="text-sm text-gray-700"></span>
          </div>

          <!-- 空占位 -->
          <div class="invisible flex items-center px-1.5 py-1">
            <div class="w-4 h-4 rounded flex items-center justify-center mr-1.5 flex-shrink-0"></div>
            <span class="text-sm text-gray-700"></span>
          </div>

          <!-- 空占位 -->
          <div class="invisible flex items-center px-1.5 py-1">
            <div class="w-4 h-4 rounded flex items-center justify-center mr-1.5 flex-shrink-0"></div>
            <span class="text-sm text-gray-700"></span>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// 打包发货面板组件
</script>

<style scoped>
.transition-colors {
  transition: background-color 0.2s ease;
}
</style>
