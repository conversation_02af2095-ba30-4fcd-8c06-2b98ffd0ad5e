<template>
  <section id="software-section" class="bg-white rounded-lg shadow-lg py-3 px-6 mt-4">
    <div class="mb-3">
      <h3 class="text-base font-medium text-gray-800 mb-2 pb-2 border-b border-gray-200">综合软件</h3>
      <!-- 软件工具网格 -->
      <div class="grid grid-cols-6 gap-3">
        <!-- BigSellerERP -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-purple-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">B</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">BigSellerERP</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">93%卖家首选，免费东南亚本土ERP</p>
        </div>

        <!-- ChatPlusAI -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-blue-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">C</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">ChatPlusAI（乐聊）</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">Shopee/Lazada/TikTok/ 美客多，AI客服智能客服更精准</p>
        </div>

        <!-- 妙手ERP -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-green-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">妙</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">妙手ERP</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">免费使用！80万+跨境卖家首选ERP，不限单量</p>
        </div>

        <!-- EasyBoss ERP -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-emerald-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">E</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">EasyBoss ERP</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">东南亚本土商家专用ERP，每月5000单免费使用不限账号</p>
        </div>

        <!-- 爱箱聊 AI客服 -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-blue-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">爱</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">爱箱聊 AI客服</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">妙手旗下多平台多店铺智能客服系统，支持Shopee、Laz</p>
        </div>

        <!-- 心融BI -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-cyan-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">心</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">心融BI</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">东南亚电商数据运营工具，实时监控数据/利润核算/广告</p>
        </div>

        <!-- 半马浏览器 -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-indigo-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">半</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">半马浏览器</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">跨境安全必备，600w卖家都在用，助你安全高效运营店铺</p>
        </div>

        <!-- 多客 AI客服 -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-blue-700 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">多</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">多客 AI客服</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">免费使用！Shopee\TikTok\Lazada客客多，让对话简洁化</p>
        </div>

        <!-- 店小秘ERP -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-orange-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">店</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">店小秘ERP</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">免费跨境电商ERP，超130万跨境卖家的共同选择</p>
        </div>

        <!-- lycheejp全球代理IP -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-purple-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">L</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">lycheejp全球代理IP</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">100%原生独享，双ISP高性能，大带宽代理IP</p>
        </div>

        <!-- 千易ERP东南亚版 -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-teal-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">千</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">千易ERP东南亚版</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">90%虾皮卖家都在用/专为东南亚卖家打造/百出出单</p>
        </div>

        <!-- 台湾本土ERP -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-blue-800 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">台</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">台湾本土ERP</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">虾皮台湾本土全链路ERP，不限店铺，不限订单</p>
        </div>

        <!-- 极风WMS -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-gray-700 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">极</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">极风WMS</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">免费海外仓管理系统，提升海外仓运效率，减本增效！</p>
        </div>

        <!-- 易仓ECCANG ERP -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-green-700 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">易</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">易仓ECCANG ERP</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">免费亚马逊ERP，AI智能驱动，1天上手30分钟出单，小团队</p>
        </div>

        <!-- 联水溶接货ERP -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-cyan-700 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">联</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">联水溶接货ERP</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">一站式/跨平台/多店铺一点货系统</p>
        </div>
        
        <!-- 东坤科技 -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-blue-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">东</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">东坤科技</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">专注于为中国商家提供TikTok电商，一站式营销服务，Tik</p>
        </div>
        
        <!-- Shopee智能广告优化 -->
        <div class="p-2 rounded-lg transition duration-200 hover:bg-blue-50 cursor-pointer group">
          <div class="flex items-center mb-1">
            <div class="w-6 h-6 bg-red-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-xs font-bold">S</span>
            </div>
            <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-500">Shopee智能广告优化</h4>
          </div>
          <p class="text-xs text-gray-600 line-clamp-2 group-hover:text-gray-900">免费试用！效率高，效果好！多店铺AI广告投放管理，省</p>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// 综合软件面板组件
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  box-orient: vertical;
  overflow: hidden;
}

/* 软件卡片hover效果 - 使用最可靠的CSS方法 */
.software-card {
  transition: all 0.2s ease-in-out;
  background-color: transparent;
}

.software-card:hover {
  background-color: #dbeafe !important;
}

.software-card .software-title {
  transition: color 0.2s ease-in-out;
  color: #1f2937;
}

.software-card:hover .software-title {
  color: #f97316 !important;
}

.software-card .software-desc {
  transition: color 0.2s ease-in-out;
  color: #4b5563;
}

.software-card:hover .software-desc {
  color: #111827 !important;
}

/* 兼容旧的group样式 */
.group {
  transition: all 0.2s ease-in-out;
}

.group:hover {
  background-color: #dbeafe !important;
}

.group:hover h4 {
  color: #f97316 !important;
}

.group:hover p {
  color: #111827 !important;
}

/* 确保Tailwind的group-hover类也能工作 */
.group:hover .group-hover\:text-orange-500 {
  color: #f97316 !important;
}

.group:hover .group-hover\:text-gray-900 {
  color: #111827 !important;
}
</style>