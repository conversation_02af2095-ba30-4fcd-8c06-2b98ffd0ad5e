<template>
  <aside class="relative mr-0 w-full">
    <!-- 导航容器 -->
    <div 
      ref="navBar"
      :class="[
        'w-full rounded-lg p-3 z-50',
        'custom-scrollbar nav-container',
        navigationStore.isNavSticky ? 'fixed nav-sticky' : 'relative'
      ]"
      style="width: 100% !important;"
      :style="[navigationStore.navBarStyle, {width: '100% !important'}]"
    >
      <div class="space-y-0.5 w-full">
        <div
          v-for="item in navigationStore.navigationItems"
          :key="item.id"
          class="w-full"
        >
          <a
            :href="item.href" 
            @click.prevent="navigationStore.scrollToSection(item.id)"
            :class="[
              'inline-block py-1.5 text-sm font-medium relative whitespace-nowrap',
              navigationStore.activeSection === item.id 
                ? 'text-orange-500 pl-4' 
                : 'text-gray-700 hover:text-orange-600'
            ]"
            :title="item.name"
          >
            <span v-if="navigationStore.activeSection === item.id" class="absolute left-0 top-1/2 transform -translate-y-1/2 text-orange-500 font-bold">—</span>
            <span 
              :class="[
                'px-2 py-0.5 rounded-md',
                navigationStore.activeSection === item.id 
                  ? '' 
                  : 'hover:bg-[#FF5722] hover:text-white'
              ]"
            >{{ item.name }}</span>
          </a>
        </div>
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useNavigationStore } from '~/stores/navigation'

const navigationStore = useNavigationStore()
const navBar = ref<HTMLElement | null>(null)

onMounted(() => {
  // 添加滚动监听
  window.addEventListener('scroll', navigationStore.handleScroll)
  
  // 添加窗口大小变化监听
  window.addEventListener('resize', navigationStore.initializeNavPosition)
  
  // 使用 nextTick 确保DOM完全渲染后再初始化
  nextTick(() => {
    // 初始化导航位置计算
    navigationStore.initializeNavPosition()
    
    // 初始化时检查当前位置
    navigationStore.handleScroll()
  })
})

onUnmounted(() => {
  // 清理滚动监听
  window.removeEventListener('scroll', navigationStore.handleScroll)
  
  // 清理窗口大小变化监听
  window.removeEventListener('resize', navigationStore.initializeNavPosition)
  
  if (navigationStore.scrollTimeout) {
    clearTimeout(navigationStore.scrollTimeout)
  }
})
</script>

<style scoped>
/* 导航项样式优化 */
a {
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

/* 自定义滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Firefox滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* 固定状态下的样式 - 无动画 */
.nav-sticky {
  max-height: calc(100vh - 40px);
  overflow-y: auto;
}

/* 移除所有过渡动画 */
.nav-container {
  transition: none !important;
}
</style> 