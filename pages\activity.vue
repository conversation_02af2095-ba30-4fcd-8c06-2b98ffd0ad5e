<template>
  <div>
    <!-- 首屏活动内容 (SSG) -->
    <section class="bg-orange-50 py-12">
      <div class="container">
        <h1 class="text-4xl font-bold text-orange-800 mb-8">热门活动</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <ActivityCard 
            v-for="activity in activityList" 
            :key="activity.id"
            :activity="activity"
            button-text="立即参与"
          />
        </div>
      </div>
    </section>
    
    <!-- 动态加载更多活动 (客户端) -->
    <section class="py-12 bg-gray-50">
      <div class="container">
        <h2 class="text-3xl font-bold text-gray-800 mb-8">更多活动</h2>
        
        <button 
          v-if="!moreActivitiesLoaded && !loading" 
          @click="loadMoreActivities" 
          class="mb-8 bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium px-6 py-2 rounded"
        >
          查看更多活动
        </button>
        
        <div v-if="loading" class="flex justify-center my-8">
          <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
        </div>
        
        <div v-if="moreActivitiesLoaded" class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <ActivityCard 
            v-for="activity in moreActivities" 
            :key="activity.id"
            :activity="activity"
            button-text="了解详情"
          />
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { useActivityStore } from '~/stores/activity'
import { ref, onMounted, watch } from 'vue'
import ActivityCard from '~/components/ActivityCard.vue'

const activityStore = useActivityStore()
const activityList = ref([])
const moreActivities = ref([])
const loading = ref(false)
const moreActivitiesLoaded = ref(false)

// SSG - 在服务器端预渲染阶段获取数据
const { data } = await useAsyncData('activity-list', () => {
  return activityStore.fetchActivityList()
})

activityList.value = data.value

// 客户端加载更多活动
const loadMoreActivities = async () => {
  loading.value = true
  try {
    const data = await activityStore.fetchMoreActivities()
    moreActivities.value = data
    moreActivitiesLoaded.value = true
  } finally {
    loading.value = false
  }
}

// 监听状态的变化
watch(() => activityStore.loading, (newValue) => {
  loading.value = newValue
})

// 确保从store中获取最新状态
onMounted(() => {
  moreActivities.value = activityStore.moreActivities
  moreActivitiesLoaded.value = moreActivities.value.length > 0
})
</script> 