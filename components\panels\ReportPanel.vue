<template>
  <section id="data-report-section" class="bg-white rounded-lg shadow-lg py-3 px-6 mt-4">
    <div class="mb-3">
      <h3 class="text-base font-medium text-gray-800 mb-2 pb-2 border-b border-gray-200">其他工具</h3>
      <!-- 其他工具网格 -->
      <div class="space-y-4">
        <!-- 软件工具部分 -->
        <div class="flex items-center gap-4">
          <span class="text-sm text-red-500 font-medium">软件工具：</span>
          <div class="flex items-center gap-6 flex-wrap">
            <div class="text-sm text-gray-700 hover:text-blue-600 cursor-pointer">妙手</div>
            <div class="text-sm text-gray-700 hover:text-blue-600 cursor-pointer">芒果店长</div>
            <div class="text-sm text-gray-700 hover:text-blue-600 cursor-pointer">奢优云ERP</div>
            <div class="text-sm text-gray-700 hover:text-blue-600 cursor-pointer">易仓WMS</div>
            <div class="text-sm text-gray-700 hover:text-blue-600 cursor-pointer">旺销王</div>
            <div class="text-sm text-gray-700 hover:text-blue-600 cursor-pointer">在线定价表</div>
            <div class="text-sm text-gray-700 hover:text-blue-600 cursor-pointer">汇率换算</div>
            <div class="text-sm text-gray-700 hover:text-blue-600 cursor-pointer">关键词热度</div>
          </div>
        </div>
        
        <!-- 孵化营训部分 -->
        <div class="flex items-center gap-4">
          <span class="text-sm text-red-500 font-medium">孵化营训：</span>
          <div class="flex items-center gap-6 flex-wrap">
            <div class="text-sm text-gray-700 hover:text-blue-600 cursor-pointer">暴走蜗牛</div>
            <div class="text-sm text-gray-700 hover:text-blue-600 cursor-pointer">跨境深蓝海</div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// 其他工具面板组件
</script>

<style scoped>
.transition-shadow {
  transition: box-shadow 0.3s ease;
}
</style> 