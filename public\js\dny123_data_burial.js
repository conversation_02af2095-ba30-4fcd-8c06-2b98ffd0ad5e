/**
 * name: amz123-data-burial-sdk
 * version: v0.0.31
  * author: 2023010 
 * date: 2024/4/21 16:15:40*/
(()=>{var he=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Xe(r){if(r.__esModule)return r;var s=r.default;if(typeof s=="function"){var p=function f(){if(this instanceof f){var g=[null];return g.push.apply(g,arguments),new(Function.bind.apply(s,g))}return s.apply(this,arguments)};p.prototype=s.prototype}else p={};return Object.defineProperty(p,"__esModule",{value:!0}),Object.keys(r).forEach(function(f){var g=Object.getOwnPropertyDescriptor(r,f);Object.defineProperty(p,f,g.get?g:{enumerable:!0,get:function(){return r[f]}})}),p}var Ee=Object.defineProperty,Ke=Object.getOwnPropertyDescriptor,Je=Object.getOwnPropertyNames,$e=Object.prototype.hasOwnProperty,De={};((r,s)=>{for(var p in s)Ee(r,p,{get:s[p],enumerable:!0})})(De,{AmzDebug:()=>Ye,AmzError:()=>et,AmzInfo:()=>qe,AmzLog:()=>Ze,AmzTable:()=>Qe});var Me,W=(Me=De,((r,s,p,f)=>{if(s&&typeof s=="object"||typeof s=="function")for(let g of Je(s))$e.call(r,g)||g===p||Ee(r,g,{get:()=>s[g],enumerable:!(f=Ke(s,g))||f.enumerable});return r})(Ee({},"__esModule",{value:!0}),Me));function le(r,...s){try{return((p=globalThis==null?void 0:globalThis.localStorage)!=null&&p.getItem("__zfty_magic_box__")||globalThis.__zfty_magic_box__?(...f)=>f.join(";").toString():()=>{})(...s)}catch{}var p}function Ze(...r){return le("log",...r)}function qe(...r){return le("info",...r)}function Ye(...r){return le("debug",...r)}function Qe(...r){return le("table",...r)}function et(...r){return le("error",...r)}var Le,fe,ge,pe={};async function me(){const r="sdk_fingerprint",s=localStorage.getItem(r);if(s)return s;const p=(await pe.getPromise()).map(g=>g.value),f=await pe.x64hash128(p.join(""),31);return localStorage.setItem(r,f),f}fe=he,ge=function(){Array.isArray===void 0&&(Array.isArray=function(e){return Object.prototype.toString.call(e)==="[object Array]"});var r=function(e,t){e=[e[0]>>>16,65535&e[0],e[1]>>>16,65535&e[1]],t=[t[0]>>>16,65535&t[0],t[1]>>>16,65535&t[1]];var n=[0,0,0,0];return n[3]+=e[3]+t[3],n[2]+=n[3]>>>16,n[3]&=65535,n[2]+=e[2]+t[2],n[1]+=n[2]>>>16,n[2]&=65535,n[1]+=e[1]+t[1],n[0]+=n[1]>>>16,n[1]&=65535,n[0]+=e[0]+t[0],n[0]&=65535,[n[0]<<16|n[1],n[2]<<16|n[3]]},s=function(e,t){e=[e[0]>>>16,65535&e[0],e[1]>>>16,65535&e[1]],t=[t[0]>>>16,65535&t[0],t[1]>>>16,65535&t[1]];var n=[0,0,0,0];return n[3]+=e[3]*t[3],n[2]+=n[3]>>>16,n[3]&=65535,n[2]+=e[2]*t[3],n[1]+=n[2]>>>16,n[2]&=65535,n[2]+=e[3]*t[2],n[1]+=n[2]>>>16,n[2]&=65535,n[1]+=e[1]*t[3],n[0]+=n[1]>>>16,n[1]&=65535,n[1]+=e[2]*t[2],n[0]+=n[1]>>>16,n[1]&=65535,n[1]+=e[3]*t[1],n[0]+=n[1]>>>16,n[1]&=65535,n[0]+=e[0]*t[3]+e[1]*t[2]+e[2]*t[1]+e[3]*t[0],n[0]&=65535,[n[0]<<16|n[1],n[2]<<16|n[3]]},p=function(e,t){return(t%=64)==32?[e[1],e[0]]:t<32?[e[0]<<t|e[1]>>>32-t,e[1]<<t|e[0]>>>32-t]:(t-=32,[e[1]<<t|e[0]>>>32-t,e[0]<<t|e[1]>>>32-t])},f=function(e,t){return(t%=64)==0?e:t<32?[e[0]<<t|e[1]>>>32-t,e[1]<<t]:[e[1]<<t-32,0]},g=function(e,t){return[e[0]^t[0],e[1]^t[1]]},L=function(e){return e=g(e,[0,e[0]>>>1]),e=s(e,[4283543511,3981806797]),e=g(e,[0,e[0]>>>1]),e=s(e,[3301882366,444984403]),e=g(e,[0,e[0]>>>1])},o=function(e,t){t=t||0;for(var n=(e=e||"").length%16,a=e.length-n,v=[0,t],c=[0,t],T=[0,0],y=[0,0],I=[2277735313,289559509],M=[1291169091,658871167],A=0;A<a;A+=16)T=[255&e.charCodeAt(A+4)|(255&e.charCodeAt(A+5))<<8|(255&e.charCodeAt(A+6))<<16|(255&e.charCodeAt(A+7))<<24,255&e.charCodeAt(A)|(255&e.charCodeAt(A+1))<<8|(255&e.charCodeAt(A+2))<<16|(255&e.charCodeAt(A+3))<<24],y=[255&e.charCodeAt(A+12)|(255&e.charCodeAt(A+13))<<8|(255&e.charCodeAt(A+14))<<16|(255&e.charCodeAt(A+15))<<24,255&e.charCodeAt(A+8)|(255&e.charCodeAt(A+9))<<8|(255&e.charCodeAt(A+10))<<16|(255&e.charCodeAt(A+11))<<24],T=s(T,I),T=p(T,31),T=s(T,M),v=g(v,T),v=p(v,27),v=r(v,c),v=r(s(v,[0,5]),[0,1390208809]),y=s(y,M),y=p(y,33),y=s(y,I),c=g(c,y),c=p(c,31),c=r(c,v),c=r(s(c,[0,5]),[0,944331445]);switch(T=[0,0],y=[0,0],n){case 15:y=g(y,f([0,e.charCodeAt(A+14)],48));case 14:y=g(y,f([0,e.charCodeAt(A+13)],40));case 13:y=g(y,f([0,e.charCodeAt(A+12)],32));case 12:y=g(y,f([0,e.charCodeAt(A+11)],24));case 11:y=g(y,f([0,e.charCodeAt(A+10)],16));case 10:y=g(y,f([0,e.charCodeAt(A+9)],8));case 9:y=g(y,[0,e.charCodeAt(A+8)]),y=s(y,M),y=p(y,33),y=s(y,I),c=g(c,y);case 8:T=g(T,f([0,e.charCodeAt(A+7)],56));case 7:T=g(T,f([0,e.charCodeAt(A+6)],48));case 6:T=g(T,f([0,e.charCodeAt(A+5)],40));case 5:T=g(T,f([0,e.charCodeAt(A+4)],32));case 4:T=g(T,f([0,e.charCodeAt(A+3)],24));case 3:T=g(T,f([0,e.charCodeAt(A+2)],16));case 2:T=g(T,f([0,e.charCodeAt(A+1)],8));case 1:T=g(T,[0,e.charCodeAt(A)]),T=s(T,I),T=p(T,31),T=s(T,M),v=g(v,T)}return v=g(v,[0,e.length]),c=g(c,[0,e.length]),v=r(v,c),c=r(c,v),v=L(v),c=L(c),v=r(v,c),c=r(c,v),("00000000"+(v[0]>>>0).toString(16)).slice(-8)+("00000000"+(v[1]>>>0).toString(16)).slice(-8)+("00000000"+(c[0]>>>0).toString(16)).slice(-8)+("00000000"+(c[1]>>>0).toString(16)).slice(-8)},_={preprocessor:null,audio:{timeout:1e3,excludeIOS11:!0},fonts:{swfContainerId:"fingerprintjs2",swfPath:"flash/compiled/FontList.swf",userDefinedFonts:[],extendedJsFonts:!1},screen:{detectScreenOrientation:!0},plugins:{sortPluginsFor:[/palemoon/i],excludeIE:!1},extraComponents:[],excludes:{enumerateDevices:!0,pixelRatio:!0,doNotTrack:!0,fontsFlash:!0,adBlock:!0},NOT_AVAILABLE:"not available",ERROR:"error",EXCLUDED:"excluded"},E=function(e,t){if(Array.prototype.forEach&&e.forEach===Array.prototype.forEach)e.forEach(t);else if(e.length===+e.length)for(var n=0,a=e.length;n<a;n++)t(e[n],n,e);else for(var v in e)e.hasOwnProperty(v)&&t(e[v],v,e)},C=function(e,t){var n=[];return e==null?n:Array.prototype.map&&e.map===Array.prototype.map?e.map(t):(E(e,function(a,v,c){n.push(t(a,v,c))}),n)},P=function(){return navigator.mediaDevices&&navigator.mediaDevices.enumerateDevices},D=function(e){var t=[window.screen.width,window.screen.height];return e.screen.detectScreenOrientation&&t.sort().reverse(),t},b=function(e){if(window.screen.availWidth&&window.screen.availHeight){var t=[window.screen.availHeight,window.screen.availWidth];return e.screen.detectScreenOrientation&&t.sort().reverse(),t}return e.NOT_AVAILABLE},B=function(e){if(navigator.plugins==null)return e.NOT_AVAILABLE;for(var t=[],n=0,a=navigator.plugins.length;n<a;n++)navigator.plugins[n]&&t.push(navigator.plugins[n]);return R(e)&&(t=t.sort(function(v,c){return v.name>c.name?1:v.name<c.name?-1:0})),C(t,function(v){var c=C(v,function(T){return[T.type,T.suffixes]});return[v.name,v.description,c]})},N=function(e){var t=[];return Object.getOwnPropertyDescriptor&&Object.getOwnPropertyDescriptor(window,"ActiveXObject")||"ActiveXObject"in window?t=C(["AcroPDF.PDF","Adodb.Stream","AgControl.AgControl","DevalVRXCtrl.DevalVRXCtrl.1","MacromediaFlashPaper.MacromediaFlashPaper","Msxml2.DOMDocument","Msxml2.XMLHTTP","PDF.PdfCtrl","QuickTime.QuickTime","QuickTimeCheckObject.QuickTimeCheck.1","RealPlayer","RealPlayer.RealPlayer(tm) ActiveX Control (32-bit)","RealVideo.RealVideo(tm) ActiveX Control (32-bit)","Scripting.Dictionary","SWCtl.SWCtl","Shell.UIHelper","ShockwaveFlash.ShockwaveFlash","Skype.Detection","TDCCtl.TDCCtl","WMPlayer.OCX","rmocx.RealPlayer G2 Control","rmocx.RealPlayer G2 Control.1"],function(n){try{return new window.ActiveXObject(n),n}catch{return e.ERROR}}):t.push(e.NOT_AVAILABLE),navigator.plugins&&(t=t.concat(B(e))),t},R=function(e){for(var t=!1,n=0,a=e.plugins.sortPluginsFor.length;n<a;n++){var v=e.plugins.sortPluginsFor[n];if(navigator.userAgent.match(v)){t=!0;break}}return t},i=function(e){try{return!!window.sessionStorage}catch{return e.ERROR}},m=function(e){try{return!!window.localStorage}catch{return e.ERROR}},S=function(e){if(h())return e.EXCLUDED;try{return!!window.indexedDB}catch{return e.ERROR}},w=function(e){return navigator.hardwareConcurrency?navigator.hardwareConcurrency:e.NOT_AVAILABLE},x=function(e){return navigator.cpuClass||e.NOT_AVAILABLE},k=function(e){return navigator.platform?navigator.platform:e.NOT_AVAILABLE},H=function(e){return navigator.doNotTrack?navigator.doNotTrack:navigator.msDoNotTrack?navigator.msDoNotTrack:window.doNotTrack?window.doNotTrack:e.NOT_AVAILABLE},G=function(){var e,t=0;navigator.maxTouchPoints!==void 0?t=navigator.maxTouchPoints:navigator.msMaxTouchPoints!==void 0&&(t=navigator.msMaxTouchPoints);try{document.createEvent("TouchEvent"),e=!0}catch{e=!1}return[t,e,"ontouchstart"in window]},X=function(e){var t=[],n=document.createElement("canvas");n.width=2e3,n.height=200,n.style.display="inline";var a=n.getContext("2d");return a.rect(0,0,10,10),a.rect(2,2,6,6),t.push("canvas winding:"+(a.isPointInPath(5,5,"evenodd")===!1?"yes":"no")),a.textBaseline="alphabetic",a.fillStyle="#f60",a.fillRect(125,1,62,20),a.fillStyle="#069",e.dontUseFakeFontInCanvas?a.font="11pt Arial":a.font="11pt no-real-font-123",a.fillText("Cwm fjordbank glyphs vext quiz, 😃",2,15),a.fillStyle="rgba(102, 204, 0, 0.2)",a.font="18pt Arial",a.fillText("Cwm fjordbank glyphs vext quiz, 😃",4,45),a.globalCompositeOperation="multiply",a.fillStyle="rgb(255,0,255)",a.beginPath(),a.arc(50,50,50,0,2*Math.PI,!0),a.closePath(),a.fill(),a.fillStyle="rgb(0,255,255)",a.beginPath(),a.arc(100,50,50,0,2*Math.PI,!0),a.closePath(),a.fill(),a.fillStyle="rgb(255,255,0)",a.beginPath(),a.arc(75,100,50,0,2*Math.PI,!0),a.closePath(),a.fill(),a.fillStyle="rgb(255,0,255)",a.arc(75,75,75,0,2*Math.PI,!0),a.arc(75,75,25,0,2*Math.PI,!0),a.fill("evenodd"),n.toDataURL&&t.push("canvas fp:"+n.toDataURL()),t},j=function(){var e,t=function(M){return e.clearColor(0,0,0,1),e.enable(e.DEPTH_TEST),e.depthFunc(e.LEQUAL),e.clear(e.COLOR_BUFFER_BIT|e.DEPTH_BUFFER_BIT),"["+M[0]+", "+M[1]+"]"};if(!(e=Te()))return null;var n=[],a=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,a);var v=new Float32Array([-.2,-.9,0,.4,-.26,0,0,.*********,0]);e.bufferData(e.ARRAY_BUFFER,v,e.STATIC_DRAW),a.itemSize=3,a.numItems=3;var c=e.createProgram(),T=e.createShader(e.VERTEX_SHADER);e.shaderSource(T,"attribute vec2 attrVertex;varying vec2 varyinTexCoordinate;uniform vec2 uniformOffset;void main(){varyinTexCoordinate=attrVertex+uniformOffset;gl_Position=vec4(attrVertex,0,1);}"),e.compileShader(T);var y=e.createShader(e.FRAGMENT_SHADER);e.shaderSource(y,"precision mediump float;varying vec2 varyinTexCoordinate;void main() {gl_FragColor=vec4(varyinTexCoordinate,0,1);}"),e.compileShader(y),e.attachShader(c,T),e.attachShader(c,y),e.linkProgram(c),e.useProgram(c),c.vertexPosAttrib=e.getAttribLocation(c,"attrVertex"),c.offsetUniform=e.getUniformLocation(c,"uniformOffset"),e.enableVertexAttribArray(c.vertexPosArray),e.vertexAttribPointer(c.vertexPosAttrib,a.itemSize,e.FLOAT,!1,0,0),e.uniform2f(c.offsetUniform,1,1),e.drawArrays(e.TRIANGLE_STRIP,0,a.numItems);try{n.push(e.canvas.toDataURL())}catch{}n.push("extensions:"+(e.getSupportedExtensions()||[]).join(";")),n.push("webgl aliased line width range:"+t(e.getParameter(e.ALIASED_LINE_WIDTH_RANGE))),n.push("webgl aliased point size range:"+t(e.getParameter(e.ALIASED_POINT_SIZE_RANGE))),n.push("webgl alpha bits:"+e.getParameter(e.ALPHA_BITS)),n.push("webgl antialiasing:"+(e.getContextAttributes().antialias?"yes":"no")),n.push("webgl blue bits:"+e.getParameter(e.BLUE_BITS)),n.push("webgl depth bits:"+e.getParameter(e.DEPTH_BITS)),n.push("webgl green bits:"+e.getParameter(e.GREEN_BITS)),n.push("webgl max anisotropy:"+function(M){var A=M.getExtension("EXT_texture_filter_anisotropic")||M.getExtension("WEBKIT_EXT_texture_filter_anisotropic")||M.getExtension("MOZ_EXT_texture_filter_anisotropic");if(A){var z=M.getParameter(A.MAX_TEXTURE_MAX_ANISOTROPY_EXT);return z===0&&(z=2),z}return null}(e)),n.push("webgl max combined texture image units:"+e.getParameter(e.MAX_COMBINED_TEXTURE_IMAGE_UNITS)),n.push("webgl max cube map texture size:"+e.getParameter(e.MAX_CUBE_MAP_TEXTURE_SIZE)),n.push("webgl max fragment uniform vectors:"+e.getParameter(e.MAX_FRAGMENT_UNIFORM_VECTORS)),n.push("webgl max render buffer size:"+e.getParameter(e.MAX_RENDERBUFFER_SIZE)),n.push("webgl max texture image units:"+e.getParameter(e.MAX_TEXTURE_IMAGE_UNITS)),n.push("webgl max texture size:"+e.getParameter(e.MAX_TEXTURE_SIZE)),n.push("webgl max varying vectors:"+e.getParameter(e.MAX_VARYING_VECTORS)),n.push("webgl max vertex attribs:"+e.getParameter(e.MAX_VERTEX_ATTRIBS)),n.push("webgl max vertex texture image units:"+e.getParameter(e.MAX_VERTEX_TEXTURE_IMAGE_UNITS)),n.push("webgl max vertex uniform vectors:"+e.getParameter(e.MAX_VERTEX_UNIFORM_VECTORS)),n.push("webgl max viewport dims:"+t(e.getParameter(e.MAX_VIEWPORT_DIMS))),n.push("webgl red bits:"+e.getParameter(e.RED_BITS)),n.push("webgl renderer:"+e.getParameter(e.RENDERER)),n.push("webgl shading language version:"+e.getParameter(e.SHADING_LANGUAGE_VERSION)),n.push("webgl stencil bits:"+e.getParameter(e.STENCIL_BITS)),n.push("webgl vendor:"+e.getParameter(e.VENDOR)),n.push("webgl version:"+e.getParameter(e.VERSION));try{var I=e.getExtension("WEBGL_debug_renderer_info");I&&(n.push("webgl unmasked vendor:"+e.getParameter(I.UNMASKED_VENDOR_WEBGL)),n.push("webgl unmasked renderer:"+e.getParameter(I.UNMASKED_RENDERER_WEBGL)))}catch{}return e.getShaderPrecisionFormat?(E(["FLOAT","INT"],function(M){E(["VERTEX","FRAGMENT"],function(A){E(["HIGH","MEDIUM","LOW"],function(z){E(["precision","rangeMin","rangeMax"],function(J){var $=e.getShaderPrecisionFormat(e[A+"_SHADER"],e[z+"_"+M])[J];J!=="precision"&&(J="precision "+J);var ye=["webgl ",A.toLowerCase()," shader ",z.toLowerCase()," ",M.toLowerCase()," ",J,":",$].join("");n.push(ye)})})})}),ue(e),n):(ue(e),n)},Q=function(){try{var e=Te(),t=e.getExtension("WEBGL_debug_renderer_info"),n=e.getParameter(t.UNMASKED_VENDOR_WEBGL)+"~"+e.getParameter(t.UNMASKED_RENDERER_WEBGL);return ue(e),n}catch{return null}},ee=function(){var e=document.createElement("div");e.innerHTML="&nbsp;",e.className="adsbox";var t=!1;try{document.body.appendChild(e),t=document.getElementsByClassName("adsbox")[0].offsetHeight===0,document.body.removeChild(e)}catch{t=!1}return t},te=function(){if(navigator.languages!==void 0)try{if(navigator.languages[0].substr(0,2)!==navigator.language.substr(0,2))return!0}catch{return!0}return!1},ne=function(){return window.screen.width<window.screen.availWidth||window.screen.height<window.screen.availHeight},re=function(){var e,t=navigator.userAgent.toLowerCase(),n=navigator.oscpu,a=navigator.platform.toLowerCase();return e=t.indexOf("windows phone")>=0?"Windows Phone":t.indexOf("windows")>=0||t.indexOf("win16")>=0||t.indexOf("win32")>=0||t.indexOf("win64")>=0||t.indexOf("win95")>=0||t.indexOf("win98")>=0||t.indexOf("winnt")>=0||t.indexOf("wow64")>=0?"Windows":t.indexOf("android")>=0?"Android":t.indexOf("linux")>=0||t.indexOf("cros")>=0||t.indexOf("x11")>=0?"Linux":t.indexOf("iphone")>=0||t.indexOf("ipad")>=0||t.indexOf("ipod")>=0||t.indexOf("crios")>=0||t.indexOf("fxios")>=0?"iOS":t.indexOf("macintosh")>=0||t.indexOf("mac_powerpc)")>=0?"Mac":"Other",("ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0)&&e!=="Windows"&&e!=="Windows Phone"&&e!=="Android"&&e!=="iOS"&&e!=="Other"&&t.indexOf("cros")===-1||n!==void 0&&((n=n.toLowerCase()).indexOf("win")>=0&&e!=="Windows"&&e!=="Windows Phone"||n.indexOf("linux")>=0&&e!=="Linux"&&e!=="Android"||n.indexOf("mac")>=0&&e!=="Mac"&&e!=="iOS"||(n.indexOf("win")===-1&&n.indexOf("linux")===-1&&n.indexOf("mac")===-1)!=(e==="Other"))?!0:a.indexOf("win")>=0&&e!=="Windows"&&e!=="Windows Phone"||(a.indexOf("linux")>=0||a.indexOf("android")>=0||a.indexOf("pike")>=0)&&e!=="Linux"&&e!=="Android"||(a.indexOf("mac")>=0||a.indexOf("ipad")>=0||a.indexOf("ipod")>=0||a.indexOf("iphone")>=0)&&e!=="Mac"&&e!=="iOS"||!(a.indexOf("arm")>=0&&e==="Windows Phone")&&!(a.indexOf("pike")>=0&&t.indexOf("opera mini")>=0)&&((a.indexOf("win")<0&&a.indexOf("linux")<0&&a.indexOf("mac")<0&&a.indexOf("iphone")<0&&a.indexOf("ipad")<0&&a.indexOf("ipod")<0)!=(e==="Other")||navigator.plugins===void 0&&e!=="Windows"&&e!=="Windows Phone")},ae=function(){var e,t=navigator.userAgent.toLowerCase(),n=navigator.productSub;if(t.indexOf("edge/")>=0||t.indexOf("iemobile/")>=0||t.indexOf("opera mini")>=0)return!1;if(((e=t.indexOf("firefox/")>=0?"Firefox":t.indexOf("opera/")>=0||t.indexOf(" opr/")>=0?"Opera":t.indexOf("chrome/")>=0?"Chrome":t.indexOf("safari/")>=0?t.indexOf("android 1.")>=0||t.indexOf("android 2.")>=0||t.indexOf("android 3.")>=0||t.indexOf("android 4.")>=0?"AOSP":"Safari":t.indexOf("trident/")>=0?"Internet Explorer":"Other")=="Chrome"||e==="Safari"||e==="Opera")&&n!=="20030107")return!0;var a,v=eval.toString().length;if(v===37&&e!=="Safari"&&e!=="Firefox"&&e!=="Other"||v===39&&e!=="Internet Explorer"&&e!=="Other"||v===33&&e!=="Chrome"&&e!=="AOSP"&&e!=="Opera"&&e!=="Other")return!0;try{throw"a"}catch(c){try{c.toSource(),a=!0}catch{a=!1}}return a&&e!=="Firefox"&&e!=="Other"},Z=function(){var e=document.createElement("canvas");return!(!e.getContext||!e.getContext("2d"))},q=function(){if(!Z())return!1;var e=Te(),t=!!window.WebGLRenderingContext&&!!e;return ue(e),t},l=function(){return navigator.appName==="Microsoft Internet Explorer"||!(navigator.appName!=="Netscape"||!/Trident/.test(navigator.userAgent))},h=function(){return("msWriteProfilerMark"in window)+("msLaunchUri"in navigator)+("msSaveBlob"in navigator)>=2},u=function(){return window.swfobject!==void 0},d=function(){return window.swfobject.hasFlashPlayerVersion("9.0.0")},Ve=function(e,t){var n="___fp_swf_loaded";window[n]=function(c){e(c)};var a=t.fonts.swfContainerId;(function(c){var T=document.createElement("div");T.setAttribute("id",c.fonts.swfContainerId),document.body.appendChild(T)})();var v={onReady:n};window.swfobject.embedSWF(t.fonts.swfPath,a,"1","1","9.0.0",!1,v,{allowScriptAccess:"always",menu:"false"},{})},Te=function(){var e=document.createElement("canvas"),t=null;try{t=e.getContext("webgl")||e.getContext("experimental-webgl")}catch{}return t||(t=null),t},ue=function(e){var t=e.getExtension("WEBGL_lose_context");t!=null&&t.loseContext()},He=[{key:"userAgent",getData:function(e){e(navigator.userAgent)}},{key:"webdriver",getData:function(e,t){e(navigator.webdriver==null?t.NOT_AVAILABLE:navigator.webdriver)}},{key:"language",getData:function(e,t){e(navigator.language||navigator.userLanguage||navigator.browserLanguage||navigator.systemLanguage||t.NOT_AVAILABLE)}},{key:"colorDepth",getData:function(e,t){e(window.screen.colorDepth||t.NOT_AVAILABLE)}},{key:"deviceMemory",getData:function(e,t){e(navigator.deviceMemory||t.NOT_AVAILABLE)}},{key:"pixelRatio",getData:function(e,t){e(window.devicePixelRatio||t.NOT_AVAILABLE)}},{key:"hardwareConcurrency",getData:function(e,t){e(w(t))}},{key:"screenResolution",getData:function(e,t){e(D(t))}},{key:"availableScreenResolution",getData:function(e,t){e(b(t))}},{key:"timezoneOffset",getData:function(e){e(new Date().getTimezoneOffset())}},{key:"timezone",getData:function(e,t){window.Intl&&window.Intl.DateTimeFormat?e(new window.Intl.DateTimeFormat().resolvedOptions().timeZone||t.NOT_AVAILABLE):e(t.NOT_AVAILABLE)}},{key:"sessionStorage",getData:function(e,t){e(i(t))}},{key:"localStorage",getData:function(e,t){e(m(t))}},{key:"indexedDb",getData:function(e,t){e(S(t))}},{key:"addBehavior",getData:function(e){e(!!window.HTMLElement.prototype.addBehavior)}},{key:"openDatabase",getData:function(e){e(!!window.openDatabase)}},{key:"cpuClass",getData:function(e,t){e(x(t))}},{key:"platform",getData:function(e,t){e(k(t))}},{key:"doNotTrack",getData:function(e,t){e(H(t))}},{key:"plugins",getData:function(e,t){l()?t.plugins.excludeIE?e(t.EXCLUDED):e(N(t)):e(B(t))}},{key:"canvas",getData:function(e,t){Z()?e(X(t)):e(t.NOT_AVAILABLE)}},{key:"webgl",getData:function(e,t){q()?e(j()):e(t.NOT_AVAILABLE)}},{key:"webglVendorAndRenderer",getData:function(e){q()?e(Q()):e()}},{key:"adBlock",getData:function(e){e(ee())}},{key:"hasLiedLanguages",getData:function(e){e(te())}},{key:"hasLiedResolution",getData:function(e){e(ne())}},{key:"hasLiedOs",getData:function(e){e(re())}},{key:"hasLiedBrowser",getData:function(e){e(ae())}},{key:"touchSupport",getData:function(e){e(G())}},{key:"fonts",getData:function(e,t){var n=["monospace","sans-serif","serif"],a=["Andale Mono","Arial","Arial Black","Arial Hebrew","Arial MT","Arial Narrow","Arial Rounded MT Bold","Arial Unicode MS","Bitstream Vera Sans Mono","Book Antiqua","Bookman Old Style","Calibri","Cambria","Cambria Math","Century","Century Gothic","Century Schoolbook","Comic Sans","Comic Sans MS","Consolas","Courier","Courier New","Geneva","Georgia","Helvetica","Helvetica Neue","Impact","Lucida Bright","Lucida Calligraphy","Lucida Console","Lucida Fax","LUCIDA GRANDE","Lucida Handwriting","Lucida Sans","Lucida Sans Typewriter","Lucida Sans Unicode","Microsoft Sans Serif","Monaco","Monotype Corsiva","MS Gothic","MS Outlook","MS PGothic","MS Reference Sans Serif","MS Sans Serif","MS Serif","MYRIAD","MYRIAD PRO","Palatino","Palatino Linotype","Segoe Print","Segoe Script","Segoe UI","Segoe UI Light","Segoe UI Semibold","Segoe UI Symbol","Tahoma","Times","Times New Roman","Times New Roman PS","Trebuchet MS","Verdana","Wingdings","Wingdings 2","Wingdings 3"];t.fonts.extendedJsFonts&&(a=a.concat(["Abadi MT Condensed Light","Academy Engraved LET","ADOBE CASLON PRO","Adobe Garamond","ADOBE GARAMOND PRO","Agency FB","Aharoni","Albertus Extra Bold","Albertus Medium","Algerian","Amazone BT","American Typewriter","American Typewriter Condensed","AmerType Md BT","Andalus","Angsana New","AngsanaUPC","Antique Olive","Aparajita","Apple Chancery","Apple Color Emoji","Apple SD Gothic Neo","Arabic Typesetting","ARCHER","ARNO PRO","Arrus BT","Aurora Cn BT","AvantGarde Bk BT","AvantGarde Md BT","AVENIR","Ayuthaya","Bandy","Bangla Sangam MN","Bank Gothic","BankGothic Md BT","Baskerville","Baskerville Old Face","Batang","BatangChe","Bauer Bodoni","Bauhaus 93","Bazooka","Bell MT","Bembo","Benguiat Bk BT","Berlin Sans FB","Berlin Sans FB Demi","Bernard MT Condensed","BernhardFashion BT","BernhardMod BT","Big Caslon","BinnerD","Blackadder ITC","BlairMdITC TT","Bodoni 72","Bodoni 72 Oldstyle","Bodoni 72 Smallcaps","Bodoni MT","Bodoni MT Black","Bodoni MT Condensed","Bodoni MT Poster Compressed","Bookshelf Symbol 7","Boulder","Bradley Hand","Bradley Hand ITC","Bremen Bd BT","Britannic Bold","Broadway","Browallia New","BrowalliaUPC","Brush Script MT","Californian FB","Calisto MT","Calligrapher","Candara","CaslonOpnface BT","Castellar","Centaur","Cezanne","CG Omega","CG Times","Chalkboard","Chalkboard SE","Chalkduster","Charlesworth","Charter Bd BT","Charter BT","Chaucer","ChelthmITC Bk BT","Chiller","Clarendon","Clarendon Condensed","CloisterBlack BT","Cochin","Colonna MT","Constantia","Cooper Black","Copperplate","Copperplate Gothic","Copperplate Gothic Bold","Copperplate Gothic Light","CopperplGoth Bd BT","Corbel","Cordia New","CordiaUPC","Cornerstone","Coronet","Cuckoo","Curlz MT","DaunPenh","Dauphin","David","DB LCD Temp","DELICIOUS","Denmark","DFKai-SB","Didot","DilleniaUPC","DIN","DokChampa","Dotum","DotumChe","Ebrima","Edwardian Script ITC","Elephant","English 111 Vivace BT","Engravers MT","EngraversGothic BT","Eras Bold ITC","Eras Demi ITC","Eras Light ITC","Eras Medium ITC","EucrosiaUPC","Euphemia","Euphemia UCAS","EUROSTILE","Exotc350 Bd BT","FangSong","Felix Titling","Fixedsys","FONTIN","Footlight MT Light","Forte","FrankRuehl","Fransiscan","Freefrm721 Blk BT","FreesiaUPC","Freestyle Script","French Script MT","FrnkGothITC Bk BT","Fruitger","FRUTIGER","Futura","Futura Bk BT","Futura Lt BT","Futura Md BT","Futura ZBlk BT","FuturaBlack BT","Gabriola","Galliard BT","Gautami","Geeza Pro","Geometr231 BT","Geometr231 Hv BT","Geometr231 Lt BT","GeoSlab 703 Lt BT","GeoSlab 703 XBd BT","Gigi","Gill Sans","Gill Sans MT","Gill Sans MT Condensed","Gill Sans MT Ext Condensed Bold","Gill Sans Ultra Bold","Gill Sans Ultra Bold Condensed","Gisha","Gloucester MT Extra Condensed","GOTHAM","GOTHAM BOLD","Goudy Old Style","Goudy Stout","GoudyHandtooled BT","GoudyOLSt BT","Gujarati Sangam MN","Gulim","GulimChe","Gungsuh","GungsuhChe","Gurmukhi MN","Haettenschweiler","Harlow Solid Italic","Harrington","Heather","Heiti SC","Heiti TC","HELV","Herald","High Tower Text","Hiragino Kaku Gothic ProN","Hiragino Mincho ProN","Hoefler Text","Humanst 521 Cn BT","Humanst521 BT","Humanst521 Lt BT","Imprint MT Shadow","Incised901 Bd BT","Incised901 BT","Incised901 Lt BT","INCONSOLATA","Informal Roman","Informal011 BT","INTERSTATE","IrisUPC","Iskoola Pota","JasmineUPC","Jazz LET","Jenson","Jester","Jokerman","Juice ITC","Kabel Bk BT","Kabel Ult BT","Kailasa","KaiTi","Kalinga","Kannada Sangam MN","Kartika","Kaufmann Bd BT","Kaufmann BT","Khmer UI","KodchiangUPC","Kokila","Korinna BT","Kristen ITC","Krungthep","Kunstler Script","Lao UI","Latha","Leelawadee","Letter Gothic","Levenim MT","LilyUPC","Lithograph","Lithograph Light","Long Island","Lydian BT","Magneto","Maiandra GD","Malayalam Sangam MN","Malgun Gothic","Mangal","Marigold","Marion","Marker Felt","Market","Marlett","Matisse ITC","Matura MT Script Capitals","Meiryo","Meiryo UI","Microsoft Himalaya","Microsoft JhengHei","Microsoft New Tai Lue","Microsoft PhagsPa","Microsoft Tai Le","Microsoft Uighur","Microsoft YaHei","Microsoft Yi Baiti","MingLiU","MingLiU_HKSCS","MingLiU_HKSCS-ExtB","MingLiU-ExtB","Minion","Minion Pro","Miriam","Miriam Fixed","Mistral","Modern","Modern No. 20","Mona Lisa Solid ITC TT","Mongolian Baiti","MONO","MoolBoran","Mrs Eaves","MS LineDraw","MS Mincho","MS PMincho","MS Reference Specialty","MS UI Gothic","MT Extra","MUSEO","MV Boli","Nadeem","Narkisim","NEVIS","News Gothic","News GothicMT","NewsGoth BT","Niagara Engraved","Niagara Solid","Noteworthy","NSimSun","Nyala","OCR A Extended","Old Century","Old English Text MT","Onyx","Onyx BT","OPTIMA","Oriya Sangam MN","OSAKA","OzHandicraft BT","Palace Script MT","Papyrus","Parchment","Party LET","Pegasus","Perpetua","Perpetua Titling MT","PetitaBold","Pickwick","Plantagenet Cherokee","Playbill","PMingLiU","PMingLiU-ExtB","Poor Richard","Poster","PosterBodoni BT","PRINCETOWN LET","Pristina","PTBarnum BT","Pythagoras","Raavi","Rage Italic","Ravie","Ribbon131 Bd BT","Rockwell","Rockwell Condensed","Rockwell Extra Bold","Rod","Roman","Sakkal Majalla","Santa Fe LET","Savoye LET","Sceptre","Script","Script MT Bold","SCRIPTINA","Serifa","Serifa BT","Serifa Th BT","ShelleyVolante BT","Sherwood","Shonar Bangla","Showcard Gothic","Shruti","Signboard","SILKSCREEN","SimHei","Simplified Arabic","Simplified Arabic Fixed","SimSun","SimSun-ExtB","Sinhala Sangam MN","Sketch Rockwell","Skia","Small Fonts","Snap ITC","Snell Roundhand","Socket","Souvenir Lt BT","Staccato222 BT","Steamer","Stencil","Storybook","Styllo","Subway","Swis721 BlkEx BT","Swiss911 XCm BT","Sylfaen","Synchro LET","System","Tamil Sangam MN","Technical","Teletype","Telugu Sangam MN","Tempus Sans ITC","Terminal","Thonburi","Traditional Arabic","Trajan","TRAJAN PRO","Tristan","Tubular","Tunga","Tw Cen MT","Tw Cen MT Condensed","Tw Cen MT Condensed Extra Bold","TypoUpright BT","Unicorn","Univers","Univers CE 55 Medium","Univers Condensed","Utsaah","Vagabond","Vani","Vijaya","Viner Hand ITC","VisualUI","Vivaldi","Vladimir Script","Vrinda","Westminster","WHITNEY","Wide Latin","ZapfEllipt BT","ZapfHumnst BT","ZapfHumnst Dm BT","Zapfino","Zurich BlkEx BT","Zurich Ex BT","ZWAdobeF"])),a=(a=a.concat(t.fonts.userDefinedFonts)).filter(function(O,F){return a.indexOf(O)===F});var v=document.getElementsByTagName("body")[0],c=document.createElement("div"),T=document.createElement("div"),y={},I={},M=function(){var O=document.createElement("span");return O.style.position="absolute",O.style.left="-9999px",O.style.fontSize="72px",O.style.fontStyle="normal",O.style.fontWeight="normal",O.style.letterSpacing="normal",O.style.lineBreak="auto",O.style.lineHeight="normal",O.style.textTransform="none",O.style.textAlign="left",O.style.textDecoration="none",O.style.textShadow="none",O.style.whiteSpace="normal",O.style.wordBreak="normal",O.style.wordSpacing="normal",O.innerHTML="mmmmmmmmmmlli",O},A=function(O,F){var V=M();return V.style.fontFamily="'"+O+"',"+F,V},z=function(O){for(var F=!1,V=0;V<n.length;V++)if(F=O[V].offsetWidth!==y[n[V]]||O[V].offsetHeight!==I[n[V]])return F;return F},J=function(){for(var O=[],F=0,V=n.length;F<V;F++){var oe=M();oe.style.fontFamily=n[F],c.appendChild(oe),O.push(oe)}return O}();v.appendChild(c);for(var $=0,ye=n.length;$<ye;$++)y[n[$]]=J[$].offsetWidth,I[n[$]]=J[$].offsetHeight;var je=function(){for(var O={},F=0,V=a.length;F<V;F++){for(var oe=[],we=0,We=n.length;we<We;we++){var ke=A(a[F],n[we]);T.appendChild(ke),oe.push(ke)}O[a[F]]=oe}return O}();v.appendChild(T);for(var xe=[],de=0,ze=a.length;de<ze;de++)z(je[a[de]])&&xe.push(a[de]);v.removeChild(T),v.removeChild(c),e(xe)},pauseBefore:!0},{key:"fontsFlash",getData:function(e,t){return u()?d()?t.fonts.swfPath?void Ve(function(n){e(n)},t):e("missing options.fonts.swfPath"):e("flash not installed"):e("swf object not loaded")},pauseBefore:!0},{key:"audio",getData:function(e,t){var n=t.audio;if(n.excludeIOS11&&navigator.userAgent.match(/OS 11.+Version\/11.+Safari/))return e(t.EXCLUDED);var a=window.OfflineAudioContext||window.webkitOfflineAudioContext;if(a==null)return e(t.NOT_AVAILABLE);var v=new a(1,44100,44100),c=v.createOscillator();c.type="triangle",c.frequency.setValueAtTime(1e4,v.currentTime);var T=v.createDynamicsCompressor();E([["threshold",-50],["knee",40],["ratio",12],["reduction",-20],["attack",0],["release",.25]],function(I){T[I[0]]!==void 0&&typeof T[I[0]].setValueAtTime=="function"&&T[I[0]].setValueAtTime(I[1],v.currentTime)}),c.connect(T),T.connect(v.destination),c.start(0),v.startRendering();var y=setTimeout(function(){return v.oncomplete=function(){},v=null,e("audioTimeout")},n.timeout);v.oncomplete=function(I){var M;try{clearTimeout(y),M=I.renderedBuffer.getChannelData(0).slice(4500,5e3).reduce(function(A,z){return A+Math.abs(z)},0).toString(),c.disconnect(),T.disconnect()}catch(A){return void e(A)}e(M)}}},{key:"enumerateDevices",getData:function(e,t){if(!P())return e(t.NOT_AVAILABLE);navigator.mediaDevices.enumerateDevices().then(function(n){e(n.map(function(a){return"id="+a.deviceId+";gid="+a.groupId+";"+a.kind+";"+a.label}))}).catch(function(n){e(n)})}}],K=function(e){throw new Error("'new Fingerprint()' is deprecated, see https://github.com/fingerprintjs/fingerprintjs#upgrade-guide-from-182-to-200")};return K.get=function(e,t){t?e||(e={}):(t=e,e={}),function(c,T){if(T==null)return c;var y,I;for(I in T)(y=T[I])==null||Object.prototype.hasOwnProperty.call(c,I)||(c[I]=y)}(e,_),e.components=e.extraComponents.concat(He);var n={data:[],addPreprocessedComponent:function(c,T){typeof e.preprocessor=="function"&&(T=e.preprocessor(c,T)),n.data.push({key:c,value:T})}},a=-1,v=function(c){if((a+=1)>=e.components.length)t(n.data);else{var T=e.components[a];if(e.excludes[T.key])v(!1);else{if(!c&&T.pauseBefore)return a-=1,void setTimeout(function(){v(!0)},1);try{T.getData(function(y){n.addPreprocessedComponent(T.key,y),v(!1)},e)}catch(y){n.addPreprocessedComponent(T.key,String(y)),v(!1)}}}};v(!1)},K.getPromise=function(e){return new Promise(function(t,n){K.get(e,t)})},K.getV18=function(e,t){return t==null&&(t=e,e={}),K.get(e,function(n){for(var a=[],v=0;v<n.length;v++){var c=n[v];if(c.value===(e.NOT_AVAILABLE||"not available"))a.push({key:c.key,value:"unknown"});else if(c.key==="plugins")a.push({key:"plugins",value:C(c.value,function(y){var I=C(y[2],function(M){return M.join?M.join("~"):M}).join(",");return[y[0],y[1],I].join("::")})});else if(["canvas","webgl"].indexOf(c.key)!==-1&&Array.isArray(c.value))a.push({key:c.key,value:c.value.join("~")});else if(["sessionStorage","localStorage","indexedDb","addBehavior","openDatabase"].indexOf(c.key)!==-1){if(!c.value)continue;a.push({key:c.key,value:1})}else c.value?a.push(c.value.join?{key:c.key,value:c.value.join(";")}:c):a.push({key:c.key,value:c.value})}var T=o(C(a,function(y){return y.value}).join("~~~"),31);t(T,a)})},K.x64hash128=o,K.VERSION="2.1.4",K},(Le={get exports(){return pe},set exports(r){pe=r}}).exports?Le.exports=ge():fe.exports?fe.exports=ge():fe.Fingerprint2=ge();const Ce=()=>{const r=location.href;return r.includes("brandark.com")?2:r.includes("tt123")?5:r.includes("vn.amz123")?7:r.includes("dny123")?8:3};function ie(r,s,p){var L;const{deep:f=5,defaultValue:g=""}=p??{};return r===document.body||!r||f<=0||r.parentElement===null?g:r.dataset[s]?((L=r==null?void 0:r.dataset)==null?void 0:L[s])??g:ie(r.parentElement,s,{deep:f-1,defaultValue:g})}async function Re(r){var s,p;try{const f=`${ie(r,"sdkPosition")}-${ie(r,"sdkIndex")}`,g=ie(r,"sdkResourceId",{deep:3,defaultValue:`${(s=r.textContent)==null?void 0:s.trim()}`??""}),L=((p=r==null?void 0:r.dataset)==null?void 0:p.sdkUrl)??(r==null?void 0:r.href)??"",o=ie(r,"sdkPartnerId",{deep:3,defaultValue:"0"}),_=ie(r,"sdkPath",{deep:3,defaultValue:location.pathname}),E=await me(),C=Ce(),P={3:"amz123-",5:"tt123-",8:"dny123-"},D=P[C]||P[3];return{resource_id:g,resource_position:f,resource_url:L,partner_id:o,path:_,finger_print:E,target_type:D+"click"}}catch(f){return W.AmzError(f),null}}var ce={},se={};Object.defineProperty(se,"__esModule",{value:!0}),se.LOCAL_STORAGE_TOKEN_NAME=void 0,se.LOCAL_STORAGE_TOKEN_NAME="amz123-global-user-info";var U={};Object.defineProperty(U,"__esModule",{value:!0}),U.checkGlobalUserInfoExpire=U.saveGlobalUserInfoToLS=U.getGlobalUserInfoFromLocalStorage=U.getTokenFromLocalStorage=U.deleteAllCookies=U.getTokenFromBBSToken=U.clearGlobalUserInfo=void 0;const Oe=se,Y=()=>{if(globalThis.document===void 0)throw new Error("当前不再客户端运行环境")};function _e(){Y(),localStorage.removeItem(Oe.LOCAL_STORAGE_TOKEN_NAME),Ne()}U.clearGlobalUserInfo=_e;function Ne(){Y();for(var r=document.cookie.split(";"),s=0;s<r.length;s++){var p=r[s],f=p.indexOf("="),g=f>-1?p.substr(0,f):p;document.cookie=`${g}=;expires=Thu, 01 Jan 1970 00:00:00 GMT`}}function Ae(){var r;Y();try{const s=localStorage.getItem(Oe.LOCAL_STORAGE_TOKEN_NAME);return s?new Date().valueOf()/1e3>((r=JSON.parse(s).expire)!==null&&r!==void 0?r:0)?void _e():JSON.parse(s):void 0}catch{return}}U.getTokenFromBBSToken=()=>(Y(),document.cookie.replace(/(?:(?:^|.*;\s*)bbs_token\s*\=\s*([^;]*).*$)|^.*$/,"$1")),U.deleteAllCookies=Ne,U.getTokenFromLocalStorage=function(){Y();const r=Ae();return r?r==null?void 0:r.token:void 0},U.getGlobalUserInfoFromLocalStorage=Ae,U.saveGlobalUserInfoToLS=function(r){Y();try{localStorage.setItem(Oe.LOCAL_STORAGE_TOKEN_NAME,JSON.stringify(r))}catch{}},U.checkGlobalUserInfoExpire=function(r){Y();const s=Ae();try{s&&s.expire<Date.now()/1e3&&(_e(),r&&window.location.reload())}catch{}},function(r){Object.defineProperty(r,"__esModule",{value:!0}),r.checkGlobalUserInfoExpire=r.saveGlobalUserInfoToLS=r.getGlobalUserInfoFromLocalStorage=r.getTokenFromLocalStorage=r.getTokenFromBBSToken=r.clearGlobalUserInfo=r.LOCAL_STORAGE_TOKEN_NAME=void 0;var s=se;Object.defineProperty(r,"LOCAL_STORAGE_TOKEN_NAME",{enumerable:!0,get:function(){return s.LOCAL_STORAGE_TOKEN_NAME}});var p=U;Object.defineProperty(r,"clearGlobalUserInfo",{enumerable:!0,get:function(){return p.clearGlobalUserInfo}}),Object.defineProperty(r,"getTokenFromBBSToken",{enumerable:!0,get:function(){return p.getTokenFromBBSToken}}),Object.defineProperty(r,"getTokenFromLocalStorage",{enumerable:!0,get:function(){return p.getTokenFromLocalStorage}}),Object.defineProperty(r,"getGlobalUserInfoFromLocalStorage",{enumerable:!0,get:function(){return p.getGlobalUserInfoFromLocalStorage}}),Object.defineProperty(r,"saveGlobalUserInfoToLS",{enumerable:!0,get:function(){return p.saveGlobalUserInfoToLS}}),Object.defineProperty(r,"checkGlobalUserInfoExpire",{enumerable:!0,get:function(){return p.checkGlobalUserInfoExpire}})}(ce);var be={},tt={get exports(){return be},set exports(r){be=r}},Be={},nt={get exports(){return Be},set exports(r){Be=r}};const rt=Xe(Object.freeze(Object.defineProperty({__proto__:null,default:{}},Symbol.toStringTag,{value:"Module"})));var Pe,Se;function at(){return Pe||(Pe=1,nt.exports=(r=r||function(s,p){var f;if(typeof window<"u"&&window.crypto&&(f=window.crypto),typeof self<"u"&&self.crypto&&(f=self.crypto),typeof globalThis<"u"&&globalThis.crypto&&(f=globalThis.crypto),!f&&typeof window<"u"&&window.msCrypto&&(f=window.msCrypto),!f&&he!==void 0&&he.crypto&&(f=he.crypto),!f)try{f=rt}catch{}var g=function(){if(f){if(typeof f.getRandomValues=="function")try{return f.getRandomValues(new Uint32Array(1))[0]}catch{}if(typeof f.randomBytes=="function")try{return f.randomBytes(4).readInt32LE()}catch{}}throw new Error("Native crypto module could not be used to get secure random number.")},L=Object.create||function(){function i(){}return function(m){var S;return i.prototype=m,S=new i,i.prototype=null,S}}(),o={},_=o.lib={},E=_.Base={extend:function(i){var m=L(this);return i&&m.mixIn(i),m.hasOwnProperty("init")&&this.init!==m.init||(m.init=function(){m.$super.init.apply(this,arguments)}),m.init.prototype=m,m.$super=this,m},create:function(){var i=this.extend();return i.init.apply(i,arguments),i},init:function(){},mixIn:function(i){for(var m in i)i.hasOwnProperty(m)&&(this[m]=i[m]);i.hasOwnProperty("toString")&&(this.toString=i.toString)},clone:function(){return this.init.prototype.extend(this)}},C=_.WordArray=E.extend({init:function(i,m){i=this.words=i||[],this.sigBytes=m!=p?m:4*i.length},toString:function(i){return(i||D).stringify(this)},concat:function(i){var m=this.words,S=i.words,w=this.sigBytes,x=i.sigBytes;if(this.clamp(),w%4)for(var k=0;k<x;k++){var H=S[k>>>2]>>>24-k%4*8&255;m[w+k>>>2]|=H<<24-(w+k)%4*8}else for(var G=0;G<x;G+=4)m[w+G>>>2]=S[G>>>2];return this.sigBytes+=x,this},clamp:function(){var i=this.words,m=this.sigBytes;i[m>>>2]&=4294967295<<32-m%4*8,i.length=s.ceil(m/4)},clone:function(){var i=E.clone.call(this);return i.words=this.words.slice(0),i},random:function(i){for(var m=[],S=0;S<i;S+=4)m.push(g());return new C.init(m,i)}}),P=o.enc={},D=P.Hex={stringify:function(i){for(var m=i.words,S=i.sigBytes,w=[],x=0;x<S;x++){var k=m[x>>>2]>>>24-x%4*8&255;w.push((k>>>4).toString(16)),w.push((15&k).toString(16))}return w.join("")},parse:function(i){for(var m=i.length,S=[],w=0;w<m;w+=2)S[w>>>3]|=parseInt(i.substr(w,2),16)<<24-w%8*4;return new C.init(S,m/2)}},b=P.Latin1={stringify:function(i){for(var m=i.words,S=i.sigBytes,w=[],x=0;x<S;x++){var k=m[x>>>2]>>>24-x%4*8&255;w.push(String.fromCharCode(k))}return w.join("")},parse:function(i){for(var m=i.length,S=[],w=0;w<m;w++)S[w>>>2]|=(255&i.charCodeAt(w))<<24-w%4*8;return new C.init(S,m)}},B=P.Utf8={stringify:function(i){try{return decodeURIComponent(escape(b.stringify(i)))}catch{throw new Error("Malformed UTF-8 data")}},parse:function(i){return b.parse(unescape(encodeURIComponent(i)))}},N=_.BufferedBlockAlgorithm=E.extend({reset:function(){this._data=new C.init,this._nDataBytes=0},_append:function(i){typeof i=="string"&&(i=B.parse(i)),this._data.concat(i),this._nDataBytes+=i.sigBytes},_process:function(i){var m,S=this._data,w=S.words,x=S.sigBytes,k=this.blockSize,H=x/(4*k),G=(H=i?s.ceil(H):s.max((0|H)-this._minBufferSize,0))*k,X=s.min(4*G,x);if(G){for(var j=0;j<G;j+=k)this._doProcessBlock(w,j);m=w.splice(0,G),S.sigBytes-=X}return new C.init(m,X)},clone:function(){var i=E.clone.call(this);return i._data=this._data.clone(),i},_minBufferSize:0});_.Hasher=N.extend({cfg:E.extend(),init:function(i){this.cfg=this.cfg.extend(i),this.reset()},reset:function(){N.reset.call(this),this._doReset()},update:function(i){return this._append(i),this._process(),this},finalize:function(i){return i&&this._append(i),this._doFinalize()},blockSize:16,_createHelper:function(i){return function(m,S){return new i.init(S).finalize(m)}},_createHmacHelper:function(i){return function(m,S){return new R.HMAC.init(i,S).finalize(m)}}});var R=o.algo={};return o}(Math),r)),Be;var r}tt.exports=(Se=at(),function(r){var s=Se,p=s.lib,f=p.WordArray,g=p.Hasher,L=s.algo,o=[];(function(){for(var b=0;b<64;b++)o[b]=4294967296*r.abs(r.sin(b+1))|0})();var _=L.MD5=g.extend({_doReset:function(){this._hash=new f.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(b,B){for(var N=0;N<16;N++){var R=B+N,i=b[R];b[R]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var m=this._hash.words,S=b[B+0],w=b[B+1],x=b[B+2],k=b[B+3],H=b[B+4],G=b[B+5],X=b[B+6],j=b[B+7],Q=b[B+8],ee=b[B+9],te=b[B+10],ne=b[B+11],re=b[B+12],ae=b[B+13],Z=b[B+14],q=b[B+15],l=m[0],h=m[1],u=m[2],d=m[3];l=E(l,h,u,d,S,7,o[0]),d=E(d,l,h,u,w,12,o[1]),u=E(u,d,l,h,x,17,o[2]),h=E(h,u,d,l,k,22,o[3]),l=E(l,h,u,d,H,7,o[4]),d=E(d,l,h,u,G,12,o[5]),u=E(u,d,l,h,X,17,o[6]),h=E(h,u,d,l,j,22,o[7]),l=E(l,h,u,d,Q,7,o[8]),d=E(d,l,h,u,ee,12,o[9]),u=E(u,d,l,h,te,17,o[10]),h=E(h,u,d,l,ne,22,o[11]),l=E(l,h,u,d,re,7,o[12]),d=E(d,l,h,u,ae,12,o[13]),u=E(u,d,l,h,Z,17,o[14]),l=C(l,h=E(h,u,d,l,q,22,o[15]),u,d,w,5,o[16]),d=C(d,l,h,u,X,9,o[17]),u=C(u,d,l,h,ne,14,o[18]),h=C(h,u,d,l,S,20,o[19]),l=C(l,h,u,d,G,5,o[20]),d=C(d,l,h,u,te,9,o[21]),u=C(u,d,l,h,q,14,o[22]),h=C(h,u,d,l,H,20,o[23]),l=C(l,h,u,d,ee,5,o[24]),d=C(d,l,h,u,Z,9,o[25]),u=C(u,d,l,h,k,14,o[26]),h=C(h,u,d,l,Q,20,o[27]),l=C(l,h,u,d,ae,5,o[28]),d=C(d,l,h,u,x,9,o[29]),u=C(u,d,l,h,j,14,o[30]),l=P(l,h=C(h,u,d,l,re,20,o[31]),u,d,G,4,o[32]),d=P(d,l,h,u,Q,11,o[33]),u=P(u,d,l,h,ne,16,o[34]),h=P(h,u,d,l,Z,23,o[35]),l=P(l,h,u,d,w,4,o[36]),d=P(d,l,h,u,H,11,o[37]),u=P(u,d,l,h,j,16,o[38]),h=P(h,u,d,l,te,23,o[39]),l=P(l,h,u,d,ae,4,o[40]),d=P(d,l,h,u,S,11,o[41]),u=P(u,d,l,h,k,16,o[42]),h=P(h,u,d,l,X,23,o[43]),l=P(l,h,u,d,ee,4,o[44]),d=P(d,l,h,u,re,11,o[45]),u=P(u,d,l,h,q,16,o[46]),l=D(l,h=P(h,u,d,l,x,23,o[47]),u,d,S,6,o[48]),d=D(d,l,h,u,j,10,o[49]),u=D(u,d,l,h,Z,15,o[50]),h=D(h,u,d,l,G,21,o[51]),l=D(l,h,u,d,re,6,o[52]),d=D(d,l,h,u,k,10,o[53]),u=D(u,d,l,h,te,15,o[54]),h=D(h,u,d,l,w,21,o[55]),l=D(l,h,u,d,Q,6,o[56]),d=D(d,l,h,u,q,10,o[57]),u=D(u,d,l,h,X,15,o[58]),h=D(h,u,d,l,ae,21,o[59]),l=D(l,h,u,d,H,6,o[60]),d=D(d,l,h,u,ne,10,o[61]),u=D(u,d,l,h,x,15,o[62]),h=D(h,u,d,l,ee,21,o[63]),m[0]=m[0]+l|0,m[1]=m[1]+h|0,m[2]=m[2]+u|0,m[3]=m[3]+d|0},_doFinalize:function(){var b=this._data,B=b.words,N=8*this._nDataBytes,R=8*b.sigBytes;B[R>>>5]|=128<<24-R%32;var i=r.floor(N/4294967296),m=N;B[15+(R+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),B[14+(R+64>>>9<<4)]=16711935&(m<<8|m>>>24)|4278255360&(m<<24|m>>>8),b.sigBytes=4*(B.length+1),this._process();for(var S=this._hash,w=S.words,x=0;x<4;x++){var k=w[x];w[x]=16711935&(k<<8|k>>>24)|4278255360&(k<<24|k>>>8)}return S},clone:function(){var b=g.clone.call(this);return b._hash=this._hash.clone(),b}});function E(b,B,N,R,i,m,S){var w=b+(B&N|~B&R)+i+S;return(w<<m|w>>>32-m)+B}function C(b,B,N,R,i,m,S){var w=b+(B&R|N&~R)+i+S;return(w<<m|w>>>32-m)+B}function P(b,B,N,R,i,m,S){var w=b+(B^N^R)+i+S;return(w<<m|w>>>32-m)+B}function D(b,B,N,R,i,m,S){var w=b+(N^(B|~R))+i+S;return(w<<m|w>>>32-m)+B}s.MD5=g._createHelper(_),s.HmacMD5=g._createHmacHelper(_)}(Math),Se.MD5);const ot=be,it=(r={})=>{if(typeof r!="object")throw new Error("🤖 无效的payload数据");const s={},p=Object.keys(r);p.sort();const f=["referrer","sdk_version"];return p.forEach(g=>{f.includes(g)||(s[g]=r[g])}),W.AmzLog(s,JSON.stringify(s)),ot(JSON.stringify(s)).toString()},Ge=async(r,s={},p={headersConfig:{},fetchOptionConfig:{},timeout:8e3})=>{try{const f=new AbortController,g=JSON.stringify({...s,sign:it(s)}),L={headers:{"Content-Type":"application/json","Access-Control-Allow-Origin":"*","App-Id":globalThis.__navApp__.appId,"Project-Id":"buried",timestamp:""+~~(Date.now()/1e3),Authorization:ce.getTokenFromLocalStorage()??ce.getTokenFromBBSToken(),...p.headersConfig},method:"POST",signal:f.signal,credentials:"include"};setTimeout(()=>{f.abort()},p.timeout);const o={body:g,...L};r=location.href.includes("https://www.amz123.com/")?"https://api.amz123.com"+r:"/api"+r;const _=await fetch(r,o);if(_.status===200){const E=await _.json();if((E==null?void 0:E.status)===0)return E.data}throw Error(JSON.stringify({statusText:_.statusText,status:_.status}))}catch(f){return void W.AmzError("请求失败：",f)}},st=r=>Ge("/statistics/v1/sdk/stats",{...r,referrer:document.referrer,sdk_version:"0.1.0"}),ct=r=>Ge("/statistics/v1/sdk/batch_stats",{...r}),Fe=async r=>{const s=r.filter(g=>g.getAttribute("data-sdk-report")==="2"||g.getAttribute("data-sdk-report")==="3"),p=[],f=s.length-1;s.forEach(async(g,L)=>{let o={};o={...await Re(g)},o.event_type="show",delete o.finger_print,delete o.sdk_version,delete o.referer,delete o.target_type,p.push(o),L===f&&ve(p)})},lt=async r=>{try{const s=await me(),p=location.pathname;st({...r,finger_print:s,path:p})}catch(s){W.AmzError(s)}},ut=async(r,s=!1)=>{try{ve(r,s)}catch(p){W.AmzError(p)}};function Ie(r){try{r.getAttribute("data-sdk-add-visit")&&r.addEventListener("click",()=>{setTimeout(()=>{const s=document.querySelectorAll("*[data-sdk-report]"),p=Array.from(s);Fe(p),Ue()},1e3)}),r.addEventListener("click",async s=>{r.classList.contains("amz-peg-menu-item")||s.stopPropagation();const p=await Re(r);let f=p;if(W.AmzLog("上报的数据是：",p),p)return delete f.target_type,delete f.finger_print,f.event_type="click",ve([f],!0);throw Error("上报数据为空")}),r.dataset.sdkMarked="1"}catch(s){W.AmzError("初始化点击行为函数异常:",s)}}const Ue=async r=>{const s=location.pathname;let p=location.href,f="0";const g=document.querySelector('meta[name="partner_id"]');g&&(f=g.getAttribute("content")||"0"),ve([{path:s,resource_position:"",resource_id:"global-visit",resource_url:p,partner_id:f,event_type:r||"in"}])},ve=async(r,s=!1)=>{const p=Math.floor(new Date().getTime()/1e3),f={sdk_version:"0.1.0",finger_print:await me(),referer:document.referrer},g={3:"amz123-",5:"tt123-",8:"dny123-"},L=(g[Ce()]||g[3])+(s?"click":"visit"),o=await ct({target_type:L,batch_data:r,created_at:p,ua_info:f});o!=null&&o.cookies&&(o==null?void 0:o.cookies.length)>0&&(o==null||o.cookies.forEach(_=>{if(_.expires>0){const E=new Date(1e3*_.expires).toUTCString();document.cookie=`${_.name}=${_.value}; expires=${E}; path=${_.path}; domain=${_.domain};`}else document.cookie=`${_.name}=${_.value};  path=${_.path}; domain=${_.domain}`}))};(function(){const r=Ce(),s=location.href.includes("vn.amz123")?"vi":"cn";globalThis.__navApp__={appId:r,langKey:s},globalThis.__report__=lt,globalThis.__batchReport__=ut,window.addEventListener("DOMContentLoaded",()=>{(async()=>{try{const p=document.querySelectorAll("*[data-sdk-report]"),f=Array.from(p);f.filter(g=>g.getAttribute("data-sdk-report")==="1"||g.getAttribute("data-sdk-report")==="3").forEach(g=>{g.dataset.sdkMarked!=="1"&&Ie(g)}),Fe(f)}catch(p){W.AmzError("监听点击事件异常:",p)}})()}),window.addEventListener("DOMContentLoaded",()=>{Ue("in")}),window.addEventListener("beforeunload",()=>{(async()=>{const p=await me(),f=Math.floor(new Date().getTime()/1e3),g={sdk_version:"0.1.0",finger_print:p,referer:document.referrer},L={event_type:"out",partner_id:"0",path:location.pathname,resource_id:"global-visit",resource_position:"",resource_url:location.href},o=JSON.stringify({batch_data:[L],created_at:f,target_type:"amz123-visit",ua_info:g,authorization:ce.getTokenFromLocalStorage()??ce.getTokenFromBBSToken()}),_=new Blob([o],{type:"application/json"});let E="/statistics/v1/sdk/batch_stats";E=location.href.includes("https://www.amz123.com/")?"https://api.amz123.com"+E:"/api"+E,navigator.sendBeacon(E,_)})()}),window.addEventListener("DOMContentLoaded",function(){try{new MutationObserver(function(p){p.forEach(function(f){const g=f.target.querySelectorAll("*[data-sdk-report]");Array.from(g).filter(L=>L.getAttribute("data-sdk-report")==="1"||L.getAttribute("data-sdk-report")==="3").forEach(L=>{L.dataset.sdkMarked!=="1"&&(W.AmzLog("监听到DOM节点创建:",L),Ie(L))})})}).observe(document.body,{subtree:!0,childList:!0,attributes:!1})}catch(p){W.AmzError("监听页面DOM生成异常:",p)}})})();
})();