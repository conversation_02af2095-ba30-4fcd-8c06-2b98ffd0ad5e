<template>
  <section id="tiktok-tools-section" class="bg-white rounded-lg shadow-lg py-3 px-6 mt-4">
    <div class="mb-3">
      <h3 class="text-base font-medium text-gray-800 mb-2 pb-2 border-b border-gray-200">TikTok常用</h3>
      <!-- TikTok常用功能网格 -->
      <div class="space-y-3">
        <!-- 第一行 -->
        <div class="grid grid-cols-6 gap-3">
          <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
            <div class="w-6 h-6 bg-red-500 rounded flex items-center justify-center">
              <span class="text-white text-xs font-bold">TK</span>
            </div>
            <span class="text-sm text-gray-700">TK专用网络节点</span>
          </div>
          
          <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
            <div class="w-6 h-6 bg-gray-800 rounded flex items-center justify-center">
              <span class="text-white text-xs font-bold">●</span>
            </div>
            <span class="text-sm text-gray-700">OBS</span>
          </div>
          
          <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
            <div class="w-6 h-6 bg-pink-500 rounded flex items-center justify-center">
              <span class="text-white text-xs font-bold">🎯</span>
            </div>
            <span class="text-sm text-gray-700">TK邀约达人神器...</span>
          </div>
          
          <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
            <div class="w-6 h-6 bg-red-600 rounded flex items-center justify-center">
              <span class="text-white text-xs font-bold">🎬</span>
            </div>
            <span class="text-sm text-gray-700">TK直播伴侣【外】</span>
          </div>
          
          <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
            <div class="w-6 h-6 bg-cyan-500 rounded flex items-center justify-center">
              <span class="text-white text-xs font-bold">💎</span>
            </div>
            <span class="text-sm text-gray-700">伪装度检测</span>
          </div>
          
          <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
            <div class="w-6 h-6 bg-yellow-600 rounded flex items-center justify-center">
              <span class="text-white text-xs font-bold">🏷</span>
            </div>
            <span class="text-sm text-gray-700">TK短视频标签查询</span>
          </div>
        </div>
        
        <!-- 第二行 -->
        <div class="grid grid-cols-6 gap-3">
          <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
            <div class="w-6 h-6 bg-black rounded flex items-center justify-center">
              <span class="text-white text-xs font-bold">📋</span>
            </div>
            <span class="text-sm text-gray-700">入驻与注册指南</span>
          </div>
          
          <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
            <div class="w-6 h-6 bg-blue-500 rounded flex items-center justify-center">
              <span class="text-white text-xs font-bold">📊</span>
            </div>
            <span class="text-sm text-gray-700">TK数据分析工具</span>
          </div>
          
          <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
            <div class="w-6 h-6 bg-green-500 rounded flex items-center justify-center">
              <span class="text-white text-xs font-bold">🎵</span>
            </div>
            <span class="text-sm text-gray-700">TK音乐库</span>
          </div>
          
          <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
            <div class="w-6 h-6 bg-purple-500 rounded flex items-center justify-center">
              <span class="text-white text-xs font-bold">🎨</span>
            </div>
            <span class="text-sm text-gray-700">视频编辑工具</span>
          </div>
          
          <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
            <div class="w-6 h-6 bg-orange-500 rounded flex items-center justify-center">
              <span class="text-white text-xs font-bold">📱</span>
            </div>
            <span class="text-sm text-gray-700">TK移动端工具</span>
          </div>
          
          <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
            <div class="w-6 h-6 bg-teal-500 rounded flex items-center justify-center">
              <span class="text-white text-xs font-bold">🔧</span>
            </div>
            <span class="text-sm text-gray-700">更多TK工具</span>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// TikTok常用工具面板组件
</script>

<style scoped>
.transition-shadow {
  transition: box-shadow 0.3s ease;
}
</style> 