import { defineStore } from 'pinia'
import { reactive } from 'vue'

export const usePanelsStore = defineStore('panels', {
  state: () => ({
    // 标签页状态
    activeTab: 'tools',
    customTab: 'website',
    newsTab: 'headlines',
    activeOtherTab: 'other',
    countryTab: 'other',
    
    // 面板显示状态
    visiblePanels: new Set([
      'shopee-section',
      'tiktok-section',
      'lazada-section',
      'tools-section',
      'custom-urls-section',
      'exchange-rate-section',
      'news-section',
      'local-backend-section',
      'software-section',
      'payment-section',
      'logistics-section',
      'warehouse-section',
      'localization-section',
      'registration-section',
      'product-analysis-section',
      'analysis-section',
      'market-analysis-section',
      'marketing-section',
      'report-section',
      'other-section',
      'zhiwuwuyan',
      'market-data-section'
    ]),



    // 时间和节日相关
    currentTime: '',
    festivals: [
      { name: '春节', date: '2025-01-29', color: 'red' },
      { name: '开斋节', date: '2025-03-30', color: 'green' },
      { name: '泰国泼水节', date: '2025-04-13', color: 'blue' },
      { name: '6.6年中大促', date: '2025-06-06', color: 'purple' },
      { name: '618', date: '2025-06-18', color: 'orange' }
    ]
  }),

  getters: {
    isVisible: (state) => (panelId: string) => {
      return state.visiblePanels.has(panelId)
    },

    getDaysUntil: (state) => (dateString: string) => {
      const targetDate = new Date(dateString)
      const today = new Date()
      const diffTime = targetDate.getTime() - today.getTime()
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      return diffDays > 0 ? diffDays : 0
    }
  },

  actions: {
    togglePanel(panelId: string) {
      if (this.visiblePanels.has(panelId)) {
        this.visiblePanels.delete(panelId)
      } else {
        this.visiblePanels.add(panelId)
      }
    },

    setActiveTab(tabName: string) {
      this.activeTab = tabName
    },

    setCustomTab(tabName: string) {
      this.customTab = tabName
    },

    setNewsTab(tabName: string) {
      this.newsTab = tabName
    },

    setActiveOtherTab(tabName: string) {
      this.activeOtherTab = tabName
    },

    setCountryTab(tabName: string) {
      this.countryTab = tabName
    },

    updateCurrentTime() {
      const now = new Date()
      const options: Intl.DateTimeFormatOptions = {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }
      
      this.currentTime = now.toLocaleDateString('zh-CN') + ' ' + 
        now.toLocaleTimeString('zh-CN', options)
    }
  }
}) 