<template>
  <div>
    <!-- 主Logo和搜索区域 -->
    <div class="py-6 bg-white">
      <div class="w-3/4 mx-auto px-4">
        <!-- Logo和搜索框容器 -->
        <div class="flex items-center justify-between mb-4">
          <!-- DNY123 Logo -->
          <div class="flex-shrink-0">
            <img src="/images/logo4.png" alt="DNY123" class="h-20">
          </div>

          <!-- 搜索框区域 -->
          <div class="flex-1 max-w-3xl mx-8">
            <!-- 搜索引擎按钮 -->
            <div class="flex justify-center items-center mb-4">
              <div class="flex items-center gap-4 overflow-x-auto scrollbar-hide search-engines">
                <button 
                  v-for="engine in searchEngines" 
                  :key="engine.name"
                  @click="selectEngine(engine)"
                  :class="[
                    'px-2 py-1 text-sm transition-all duration-200 relative whitespace-nowrap flex-shrink-0',
                    selectedEngine.name === engine.name ? 'text-black font-medium' : 'text-gray-500 hover:text-gray-700'
                  ]"
                >
                  {{ engine.name }}
                  <!-- 选中状态的下划线 -->
                  <div 
                    v-if="selectedEngine.name === engine.name"
                    class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-orange-500 rounded-full"
                  ></div>
                </button>
              </div>
            </div>

            <!-- 搜索框 -->
            <div class="relative bg-white rounded-full shadow-sm transition-all duration-200" :class="isInputFocused ? 'border-2 border-blue-400' : 'border-0'">
              <input 
                v-model="searchQuery"
                type="text" 
                :placeholder="selectedEngine.placeholder"
                class="w-full px-6 py-4 text-base rounded-full focus:outline-none bg-transparent"
                @focus="isInputFocused = true"
                @blur="isInputFocused = false"
                @keyup.enter="handleSearch"
              >
              <button 
                @click="handleSearch"
                class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-orange-500 hover:bg-orange-600 text-white p-3 rounded-full transition-colors"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                </svg>
              </button>
            </div>
          </div>

          <!-- 占位符保持平衡 -->
          <div class="flex-shrink-0 w-32"></div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="bg-gray-50 py-8">
      <div class="mx-auto px-4" style="width: 70%;">
        <!-- 快捷导航与面板整体容器 -->
        <div class="w-full flex">
          <!-- 左侧悬浮导航 -->
          <aside class="relative">
              <!-- 分类导航 -->
              <div 
                ref="navBar"
                :class="[
                  'w-64 bg-white rounded-lg shadow-lg p-4 z-50 transition-all duration-200',
                  isNavSticky ? 'fixed' : 'absolute'
                ]"
                :style="navBarStyle"
              >
                <div class="space-y-1">
                  <a
                    href="#tools-section" 
                    @click.prevent="scrollToSection('tools-section')"
                    :class="[
                      'block px-4 py-2.5 text-base transition-colors font-medium relative',
                      activeSection === 'tools-section' 
                        ? 'text-orange-500' 
                        : 'text-gray-700 hover:bg-orange-100 hover:text-orange-600 rounded-md'
                    ]"
                  >
                    <span v-if="activeSection === 'tools-section'" class="absolute left-1 top-1/2 transform -translate-y-1/2 text-orange-500 font-bold">—</span>
                    <span :class="activeSection === 'tools-section' ? 'ml-4' : ''">常用工具</span>
                  </a>
                  <a 
                    href="#news-section" 
                    @click.prevent="scrollToSection('news-section')"
                    :class="[
                      'block px-4 py-2.5 text-base transition-colors relative',
                      activeSection === 'news-section' 
                        ? 'text-orange-500' 
                        : 'text-gray-600 hover:bg-orange-100 hover:text-orange-600 rounded-md'
                    ]"
                  >
                    <span v-if="activeSection === 'news-section'" class="absolute left-1 top-1/2 transform -translate-y-1/2 text-orange-500 font-bold">—</span>
                    <span :class="activeSection === 'news-section' ? 'ml-4' : ''">跨境资讯</span>
                  </a>
                  <a 
                    href="#local-backend-section" 
                    @click.prevent="scrollToSection('local-backend-section')"
                    :class="[
                      'block px-4 py-2.5 text-base transition-colors relative',
                      activeSection === 'local-backend-section' 
                        ? 'text-orange-500' 
                        : 'text-gray-600 hover:bg-orange-100 hover:text-orange-600 rounded-md'
                    ]"
                  >
                    <span v-if="activeSection === 'local-backend-section'" class="absolute left-1 top-1/2 transform -translate-y-1/2 text-orange-500 font-bold">—</span>
                    <span :class="activeSection === 'local-backend-section' ? 'ml-4' : ''">本土后台</span>
                  </a>
                  <a 
                    href="#software-section" 
                    @click.prevent="scrollToSection('software-section')"
                    :class="[
                      'block px-4 py-2.5 text-base transition-colors relative',
                      activeSection === 'software-section' 
                        ? 'text-orange-500' 
                        : 'text-gray-600 hover:bg-orange-100 hover:text-orange-600 rounded-md'
                    ]"
                  >
                    <span v-if="activeSection === 'software-section'" class="absolute left-1 top-1/2 transform -translate-y-1/2 text-orange-500 font-bold">—</span>
                    <span :class="activeSection === 'software-section' ? 'ml-4' : ''">综合软件</span>
                  </a>
                  <a 
                    href="#payment-section" 
                    @click.prevent="scrollToSection('payment-section')"
                    :class="[
                      'block px-4 py-2.5 text-base transition-colors relative',
                      activeSection === 'payment-section' 
                        ? 'text-orange-500' 
                        : 'text-gray-600 hover:bg-orange-100 hover:text-orange-600 rounded-md'
                    ]"
                  >
                    <span v-if="activeSection === 'payment-section'" class="absolute left-1 top-1/2 transform -translate-y-1/2 text-orange-500 font-bold">—</span>
                    <span :class="activeSection === 'payment-section' ? 'ml-4' : ''">收款方式</span>
                  </a>
                  <a 
                    href="#logistics-section" 
                    @click.prevent="scrollToSection('logistics-section')"
                    :class="[
                      'block px-4 py-2.5 text-base transition-colors relative',
                      activeSection === 'logistics-section' 
                        ? 'text-orange-500' 
                        : 'text-gray-600 hover:bg-orange-100 hover:text-orange-600 rounded-md'
                    ]"
                  >
                    <span v-if="activeSection === 'logistics-section'" class="absolute left-1 top-1/2 transform -translate-y-1/2 text-orange-500 font-bold">—</span>
                    <span :class="activeSection === 'logistics-section' ? 'ml-4' : ''">东南亚物流</span>
                  </a>
                  <a 
                    href="#warehouse-section" 
                    @click.prevent="scrollToSection('warehouse-section')"
                    :class="[
                      'block px-4 py-2.5 text-base transition-colors relative',
                      activeSection === 'warehouse-section' 
                        ? 'text-orange-500' 
                        : 'text-gray-600 hover:bg-orange-100 hover:text-orange-600 rounded-md'
                    ]"
                  >
                    <span v-if="activeSection === 'warehouse-section'" class="absolute left-1 top-1/2 transform -translate-y-1/2 text-orange-500 font-bold">—</span>
                    <span :class="activeSection === 'warehouse-section' ? 'ml-4' : ''">东南亚海外仓</span>
                  </a>
                  <a 
                    href="#registration-section" 
                    @click.prevent="scrollToSection('registration-section')"
                    :class="[
                      'block px-4 py-2.5 text-base transition-colors relative',
                      activeSection === 'registration-section' 
                        ? 'text-orange-500' 
                        : 'text-gray-600 hover:bg-orange-100 hover:text-orange-600 rounded-md'
                    ]"
                  >
                    <span v-if="activeSection === 'registration-section'" class="absolute left-1 top-1/2 transform -translate-y-1/2 text-orange-500 font-bold">—</span>
                    <span :class="activeSection === 'registration-section' ? 'ml-4' : ''">本地商标&公司注册</span>
                  </a>
                  <a 
                    href="#analysis-section" 
                    @click.prevent="scrollToSection('analysis-section')"
                    :class="[
                      'block px-4 py-2.5 text-base transition-colors relative',
                      activeSection === 'analysis-section' 
                        ? 'text-orange-500' 
                        : 'text-gray-600 hover:bg-orange-100 hover:text-orange-600 rounded-md'
                    ]"
                  >
                    <span v-if="activeSection === 'analysis-section'" class="absolute left-1 top-1/2 transform -translate-y-1/2 text-orange-500 font-bold">—</span>
                    <span :class="activeSection === 'analysis-section' ? 'ml-4' : ''">TikTok常用</span>
                  </a>
                  <a 
                    href="#localization-section" 
                    @click.prevent="scrollToSection('localization-section')"
                    :class="[
                      'block px-4 py-2.5 text-base transition-colors relative',
                      activeSection === 'localization-section' 
                        ? 'text-orange-500' 
                        : 'text-gray-600 hover:bg-orange-100 hover:text-orange-600 rounded-md'
                    ]"
                  >
                    <span v-if="activeSection === 'localization-section'" class="absolute left-1 top-1/2 transform -translate-y-1/2 text-orange-500 font-bold">—</span>
                    <span :class="activeSection === 'localization-section' ? 'ml-4' : ''">本地化服务</span>
              </a>
              <a
                href="#tiktok-section" 
                @click.prevent="scrollToSection('tiktok-section')"
                :class="[
                      'block px-4 py-2.5 text-base transition-colors relative',
                  activeSection === 'tiktok-section' 
                    ? 'text-orange-500' 
                        : 'text-gray-600 hover:bg-orange-100 hover:text-orange-600 rounded-md'
              ]"
              >
                <span v-if="activeSection === 'tiktok-section'" class="absolute left-1 top-1/2 transform -translate-y-1/2 text-orange-500 font-bold">—</span>
                    <span :class="activeSection === 'tiktok-section' ? 'ml-4' : ''">TikTok常用</span>
              </a>
              <a
                    href="#shipping-section" 
                    @click.prevent="scrollToSection('shipping-section')"
                :class="[
                      'block px-4 py-2.5 text-base transition-colors relative',
                      activeSection === 'shipping-section' 
                    ? 'text-orange-500' 
                        : 'text-gray-600 hover:bg-orange-100 hover:text-orange-600 rounded-md'
              ]"
              >
                    <span v-if="activeSection === 'shipping-section'" class="absolute left-1 top-1/2 transform -translate-y-1/2 text-orange-500 font-bold">—</span>
                    <span :class="activeSection === 'shipping-section' ? 'ml-4' : ''">打包发货</span>
              </a>
                <a
                    href="#source-section" 
                    @click.prevent="scrollToSection('source-section')"
                  :class="[
                    'block px-4 py-2.5 text-base transition-colors relative',
                      activeSection === 'source-section' 
                      ? 'text-orange-500' 
                      : 'text-gray-600 hover:bg-orange-100 hover:text-orange-600 rounded-md'
                  ]"
                >
                    <span v-if="activeSection === 'source-section'" class="absolute left-1 top-1/2 transform -translate-y-1/2 text-orange-500 font-bold">—</span>
                    <span :class="activeSection === 'source-section' ? 'ml-4' : ''">货源网站</span>
                </a>
                                  <a 
                    href="#marketing-section" 
                    @click.prevent="scrollToSection('marketing-section')"
                    :class="[
                      'block px-4 py-2.5 text-base transition-colors relative',
                      activeSection === 'marketing-section' 
                        ? 'text-orange-500' 
                        : 'text-gray-600 hover:bg-orange-100 hover:text-orange-600 rounded-md'
                    ]"
                  >
                    <span v-if="activeSection === 'marketing-section'" class="absolute left-1 top-1/2 transform -translate-y-1/2 text-orange-500 font-bold">—</span>
                    <span :class="activeSection === 'marketing-section' ? 'ml-4' : ''">货源网站</span>
                  </a>

                  <a 
                    href="#report-section" 
                    @click.prevent="scrollToSection('report-section')"
                    :class="[
                      'block px-4 py-2.5 text-base transition-colors relative',
                      activeSection === 'report-section' 
                        ? 'text-orange-500' 
                        : 'text-gray-600 hover:bg-orange-100 hover:text-orange-600 rounded-md'
                    ]"
                  >
                    <span v-if="activeSection === 'report-section'" class="absolute left-1 top-1/2 transform -translate-y-1/2 text-orange-500 font-bold">—</span>
                    <span :class="activeSection === 'report-section' ? 'ml-4' : ''">其他工具</span>
                  </a>
                  <a 
                    href="#other-section" 
                    @click.prevent="scrollToSection('other-section')"
                    :class="[
                      'block px-4 py-2.5 text-base transition-colors relative',
                      activeSection === 'other-section' 
                        ? 'text-orange-500' 
                        : 'text-gray-600 hover:bg-orange-100 hover:text-orange-600 rounded-md'
                  ]"
                  >
                    <span v-if="activeSection === 'other-section'" class="absolute left-1 top-1/2 transform -translate-y-1/2 text-orange-500 font-bold">—</span>
                    <span :class="activeSection === 'other-section' ? 'ml-4' : ''">月度报告</span>
                  </a>
                  <a 
                    href="#market-analysis" 
                    @click.prevent="scrollToSection('market-analysis')"
                    :class="[
                      'block px-4 py-2.5 text-base transition-colors relative',
                      activeSection === 'market-analysis' 
                        ? 'text-orange-500' 
                        : 'text-gray-600 hover:bg-orange-100 hover:text-orange-600 rounded-md'
                  ]"
                  >
                    <span v-if="activeSection === 'market-analysis'" class="absolute left-1 top-1/2 transform -translate-y-1/2 text-orange-500 font-bold">—</span>
                    <span :class="activeSection === 'market-analysis' ? 'ml-4' : ''">市场分析</span>
                  </a>
            </div>
              </div>
            </aside>

            <!-- 主要内容区域 - 居中展示 -->
            <div class="w-full pl-72 transition-all duration-200">
            <!-- 主要内容 -->
            <div class="space-y-6">
              <!-- 各国时间横向排列 -->
              <div class="flex justify-between items-center text-sm text-gray-600 bg-transparent py-2">
                <div class="flex items-center gap-2">
                  <img src="/images/countries/id_flag.png" alt="印尼" class="w-4 h-4 rounded-full">
                  <img src="/images/countries/vn_flag.png" alt="越南" class="w-4 h-4 rounded-full">
                  <img src="/images/countries/th_flag.png" alt="泰国" class="w-4 h-4 rounded-full">
                  <span>印尼 越南 泰国 06-18 12:56:26</span>
                </div>
                <div class="flex items-center gap-2">
                  <img src="/images/countries/cn_flag.png" alt="北京" class="w-4 h-4 rounded-full">
                  <img src="/images/countries/ph_flag.png" alt="菲律宾" class="w-4 h-4 rounded-full">
                  <img src="/images/countries/my_flag.png" alt="马来" class="w-4 h-4 rounded-full">
                  <span>北京 菲律宾 马来 06-18 13:56:26</span>
                </div>
                <div class="flex items-center gap-2">
                  <img src="/images/countries/br_flag.png" alt="巴西" class="w-4 h-4 rounded-full">
                  <span>巴西 06-18 02:56:26</span>
                </div>
                <div class="flex items-center gap-2">
                  <img src="/images/countries/mx_flag.png" alt="墨西哥" class="w-4 h-4 rounded-full">
                  <span>墨西哥 06-18 00:56:26</span>
                </div>
              </div>



              <!-- 第一个面板：Shopee站点 + 后台（24个） -->
              <section id="shopee-section" class="bg-white rounded-lg shadow-lg p-6">
                <div class="grid grid-cols-12 gap-3">
                  <!-- Shopee站点教程 (12个) -->
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <img src="/images/countries/ph_flag.png" alt="菲律宾" class="w-16 h-16 mb-2 object-cover rounded-full">
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">菲律宾站</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <img src="/images/countries/tw_flag.png" alt="中国台湾" class="w-16 h-16 mb-2 object-cover rounded-full">
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">中国台湾站</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <img src="/images/countries/my_flag.png" alt="马来西亚" class="w-16 h-16 mb-2 object-cover rounded-full">
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">马来站</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <img src="/images/countries/th_flag.png" alt="泰国" class="w-16 h-16 mb-2 object-cover rounded-full">
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">泰国站</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <img src="/images/countries/id_flag.png" alt="印尼" class="w-16 h-16 mb-2 object-cover rounded-full">
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">印尼站</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <img src="/images/countries/vn_flag.png" alt="越南" class="w-16 h-16 mb-2 object-cover rounded-full">
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">越南站</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <img src="/images/countries/sg_flag.png" alt="新加坡" class="w-16 h-16 mb-2 object-cover rounded-full">
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">新加坡站</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <img src="/images/countries/br_flag.png" alt="巴西" class="w-16 h-16 mb-2 object-cover rounded-full">
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">巴西站</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <img src="/images/countries/mx_flag.png" alt="墨西哥" class="w-16 h-16 mb-2 object-cover rounded-full">
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">墨西哥站</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <img src="/images/countries/cn_flag.png" alt="中国" class="w-16 h-16 mb-2 object-cover rounded-full">
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">中国卖家中心</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-orange-500 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">S</span>
        </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">虾皮知识大纲</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-green-500 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">入</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">Shopee入驻</span>
                  </a>

                  <!-- Shopee后台入口 (12个) -->
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-orange-500 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">S</span>
          </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">菲律宾后台</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-orange-500 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">S</span>
        </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">中国台湾后台</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-orange-500 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">S</span>
                  </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">马来后台</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-orange-500 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">S</span>
                  </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">泰国后台</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-orange-500 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">S</span>
                  </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">印尼后台</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-orange-500 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">S</span>
                  </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">越南后台</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-orange-500 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">S</span>
                  </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">新加坡后台</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-orange-500 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">S</span>
                  </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">巴西后台</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-orange-500 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">S</span>
                  </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">墨西哥后台</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-blue-500 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">紫</span>
                  </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">紫鸟跨境浏览器</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-black rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">T</span>
                  </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">TikTok选品工具</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-green-600 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">周</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">市场周报</span>
                  </a>
      </div>
    </section>
    
              <!-- 第二个面板：抖音后台（12个） -->
              <section id="tiktok-section" class="bg-white rounded-lg shadow-lg p-6">
                <div class="grid grid-cols-12 gap-3">
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-black rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">抖</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">印尼后台</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-black rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">抖</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">菲律宾后台</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-black rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">抖</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">马来后台</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-black rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">抖</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">泰国后台</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-black rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">抖</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">越南后台</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-black rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">抖</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">新加坡后台</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-black rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">跨</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">跨境后台</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-blue-500 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">PC</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">PC前台</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-red-500 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">广</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">广告管理平台</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-black rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">T</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">TikTok知识大纲</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-red-600 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">TT</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">TTS东南亚官方优先入驻</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-black rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">卖</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">卖家大学</span>
                  </a>
                </div>
              </section>

              <!-- 第三个面板：Lazada（12个） -->
              <section id="lazada-section" class="bg-white rounded-lg shadow-lg p-6">
                <div class="grid grid-cols-12 gap-3">
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-blue-600 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">L</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">菲律宾站</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-blue-600 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">L</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">马来站</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-blue-600 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">L</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">泰国站</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-blue-600 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">L</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">印尼站</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-blue-600 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">L</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">越南站</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-blue-600 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">L</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">新加坡站</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-orange-500 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">L</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">Lazada后台</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-blue-600 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">U</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">Lazada大学</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-orange-600 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">16</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">1688</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-orange-500 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">知</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">Lazada知识大纲</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-yellow-500 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">热</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">热搜词排名</span>
                  </a>
                  <a href="#" class="flex flex-col items-center p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 mb-2 bg-blue-600 rounded-full flex items-center justify-center">
                      <span class="text-white text-lg font-bold">入</span>
                    </div>
                    <span class="text-sm text-center text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full">Lazada入驻</span>
                  </a>
              </div>
            </section>
            
              <!-- 节日倒计时 -->
              <section class="bg-transparent p-0 mt-6">
                <div class="flex justify-between items-center text-sm">
                  <div class="text-gray-500">
                    2025年第{{ Math.floor((new Date().getTime() - new Date('2025-01-01').getTime()) / (1000 * 60 * 60 * 24 * 7)) + 1 }}周，距2026年还有{{ Math.ceil((new Date('2026-01-01').getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) }}天
                  </div>
                  <div class="flex space-x-6">
                    <span class="text-blue-600 hover:text-orange-500 cursor-pointer transition-colors">距 <span class="text-blue-600">春节</span> 还有 <span class="text-red-500">{{ getDaysUntil('2025-01-29') }}</span> 天</span>
                    <span class="text-blue-600 hover:text-orange-500 cursor-pointer transition-colors">距 <span class="text-blue-600">开斋节</span> 还有 <span class="text-green-500">{{ getDaysUntil('2025-03-30') }}</span> 天</span>
                    <span class="text-blue-600 hover:text-orange-500 cursor-pointer transition-colors">距 <span class="text-blue-600">泰国泼水节</span> 还有 <span class="text-blue-500">{{ getDaysUntil('2025-04-13') }}</span> 天</span>
                    <span class="text-blue-600 hover:text-orange-500 cursor-pointer transition-colors">距 <span class="text-blue-600">6.6年中大促</span> 还有 <span class="text-purple-500">{{ getDaysUntil('2025-06-06') }}</span> 天</span>
                    <span class="text-blue-600 hover:text-orange-500 cursor-pointer transition-colors">距 <span class="text-blue-600">618</span> 还有 <span class="text-red-500">{{ getDaysUntil('2025-06-18') }}</span> 天</span>
                  </div>
                </div>
              </section>

              <!-- 常用工具面板 -->
              <section id="tools-section" class="bg-white rounded-lg shadow-lg p-6 mt-6">
                <!-- 标签页 -->
                <div class="flex border-b border-gray-200 mb-6">
        <button 
                      @click="activeTab = 'tools'"
                      :class="['px-4 py-2 text-sm font-medium border-b-2 transition-colors', 
                              activeTab === 'tools' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700']"
                    >
                      常用工具
        </button>
                    <button 
                      @click="activeTab = 'shopee'"
                      :class="['px-4 py-2 text-sm font-medium border-b-2 transition-colors',
                              activeTab === 'shopee' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700']"
                    >
                      Shopee拉美站点
                    </button>
      <button 
                      @click="activeTab = 'tiktok'"
                      :class="['px-4 py-2 text-sm font-medium border-b-2 transition-colors',
                              activeTab === 'tiktok' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700']"
                    >
                      TikTok Shop欧美站点
      </button>
                  </div>

                  <!-- 工具内容 -->
                  <div v-show="activeTab === 'tools'" class="grid grid-cols-6 gap-x-8 gap-y-4 text-sm">
                    <!-- 第一行 -->
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🔄</span>在线汇率换算
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🏆</span>1688以图搜商款
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">📊</span>虾皮在线价格
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🔍</span>在线单位换算
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">💡</span>关键词热度查询
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">😊</span>在线Emoji图案
                    </a>

                    <!-- 第二行 -->
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🔍</span>菲律宾热搜词查询
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🇺🇸</span>泰国热搜词查询
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🇺🇸</span>马来西亚热搜词查询
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🇻🇳</span>新加坡热搜词查询
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🇨🇳</span>中国台湾热搜词查询
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🇲🇽</span>墨西哥热搜词查询
                    </a>

                    <!-- 第三行 -->
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">👍</span>优选好评计算
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">⚖️</span>百度翻译
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🔄</span>谷歌翻译
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">👥</span>阿里图片翻译
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">💰</span>大小写转换
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">👑</span>标题组合器
                    </a>

                    <!-- 第四行 -->
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🎭</span>多国翻译神器
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🥇</span>各国定价表
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">💳</span>仓库关联方式
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">📅</span>跨境卖家日历
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">📝</span>作图神器
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🏪</span>快捷查询
                    </a>

                    <!-- 第五行 -->
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">💯</span>免费开店咨询
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">⚡</span>虾皮店铺ID获取
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">📋</span>词频统计
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🔍</span>海关编码查询
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">📊</span>商标分类表
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">📋</span>去除重复文本
                    </a>

                    <!-- 第六行 -->
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🔍</span>菲律宾关键词查询
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">📋</span>平台联系方式
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">💚</span>带货达人黑名单
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">💎</span>船舶位置查询
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🌍</span>东南亚地图
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🗺️</span>船舶位置查询
                    </a>
        </div>
        
                  <!-- Shopee拉美站点内容 -->
                  <div v-show="activeTab === 'shopee'" class="grid grid-cols-8 gap-x-6 gap-y-4 text-sm">
                    <!-- 第一行 -->
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🇨🇴</span>哥伦比亚站
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🇨🇱</span>智利站
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🇲🇽</span>墨西哥站
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🇪🇸</span>西班牙站
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🔗</span>氪伦比亚平台
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🔧</span>智利后台
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🔧</span>墨西哥后台
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🔧</span>西班牙后台
                    </a>
          </div>

                  <!-- TikTok Shop欧美站点内容 -->
                  <div v-show="activeTab === 'tiktok'" class="grid grid-cols-8 gap-x-6 gap-y-4 text-sm">
                    <!-- 第一行 -->
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🇺🇸</span>美国站
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🇬🇧</span>英国站
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🇩🇪</span>德国站
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🇫🇷</span>法国站
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🇮🇹</span>意大利站
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🇪🇸</span>西班牙站
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🇨🇦</span>加拿大站
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🇦🇺</span>澳大利亚站
                    </a>
        </div>
                </section>

                <!-- 自定义网址面板 -->
                <section id="custom-urls-section" class="bg-white rounded-lg shadow-lg p-6 mt-6">
                  <div class="flex items-center justify-between mb-6">
                    <!-- 标签页 -->
                    <div class="flex items-center gap-1">
                      <button 
                        @click="customTab = 'website'"
                        :class="[
                          'px-4 py-2 text-sm rounded-full transition-colors',
                          customTab === 'website' 
                            ? 'bg-blue-500 text-white' 
                            : 'text-gray-600 hover:text-blue-500'
                        ]"
                      >
                        自定义网址
                      </button>
                      <button 
                        @click="customTab = 'leisure'"
                        :class="[
                          'px-4 py-2 text-sm rounded-full transition-colors',
                          customTab === 'leisure' 
                            ? 'bg-blue-500 text-white' 
                            : 'text-gray-600 hover:text-blue-500'
                        ]"
                      >
                        生活休闲
                      </button>
                    </div>
                    <button class="text-blue-600 hover:text-blue-800 text-sm">+ 添加</button>
                  </div>
                  
                  <!-- 自定义网址内容 -->
                  <div v-if="customTab === 'website'" class="grid grid-cols-6 gap-x-8 gap-y-4 text-sm">
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🔷</span>微博
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">📚</span>知乎
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">📧</span>163邮箱
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">📄</span>金山文档
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🛒</span>淘宝网
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🌐</span>紫鸟浏览器
                    </a>
                  </div>

                  <!-- 生活休闲内容 -->
                  <div v-if="customTab === 'leisure'" class="grid grid-cols-6 gap-x-8 gap-y-4 text-sm">
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🎵</span>网易云音乐
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">📺</span>哔哩哔哩
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🎬</span>爱奇艺
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🍔</span>美团外卖
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🛍️</span>京东商城
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">✈️</span>携程旅行
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">📰</span>今日头条
                    </a>
                    <a href="#" class="flex items-center text-blue-600 hover:underline">
                      <span class="mr-2">🎮</span>Steam
                    </a>
                  <a href="#" class="flex items-center text-blue-600 hover:underline">
                    <span class="mr-2">📖</span>起点中文网
                  </a>
                  <a href="#" class="flex items-center text-blue-600 hover:underline">
                    <span class="mr-2">🏠</span>贝壳找房
                  </a>
                  <a href="#" class="flex items-center text-blue-600 hover:underline">
                    <span class="mr-2">🚗</span>汽车之家
                  </a>
                  <a href="#" class="flex items-center text-blue-600 hover:underline">
                    <span class="mr-2">💰</span>支付宝
                  </a>
      </div>
    </section>

              <!-- 实时汇率信息 -->
              <div class="mt-6 text-xs">
                <span class="text-blue-600 hover:text-orange-500 transition-colors cursor-pointer">
                  实时汇率&nbsp; (更新时间:<span class="text-orange-600">2025-06-18 13:25:31</span>)
                </span>
                <span class="ml-6 text-blue-600 hover:text-orange-500 transition-colors cursor-pointer">1人民币=<span class="text-orange-600">4.11</span>新台币</span>
                <span class="ml-6 text-blue-600 hover:text-orange-500 transition-colors cursor-pointer">1人民币=<span class="text-orange-600">2272.73</span>印尼盾</span>
                <span class="ml-6 text-blue-600 hover:text-orange-500 transition-colors cursor-pointer">1人民币=<span class="text-orange-600">4.53</span>泰铢</span>
                <span class="ml-6 text-blue-600 hover:text-orange-500 transition-colors cursor-pointer">1人民币=<span class="text-orange-600">7.91</span>菲律宾比索</span>
                <span class="ml-6 text-blue-600 hover:text-orange-500 transition-colors cursor-pointer">1人民币=<span class="text-orange-600">3623.19</span>越南盾</span>
                <span class="ml-6 text-blue-600 hover:text-orange-500 transition-colors cursor-pointer">1马来西亚林吉特=<span class="text-orange-600">1.6925</span>人民币</span>
              </div>

              <!-- 跨境资讯面板 -->
              <section id="news-section" class="bg-white rounded-lg shadow-lg p-6 mt-6">
                <div class="mb-6">
                  <h3 class="text-lg font-medium text-gray-800 mb-4 pb-4 border-b border-gray-200">跨境资讯</h3>
                  <!-- 标签页 -->
                  <div class="flex items-center gap-6">
                    <button 
                      @click="newsTab = 'headlines'"
                      :class="[
                        'flex items-center gap-2 px-3 py-2 text-sm transition-colors',
                        newsTab === 'headlines' 
                          ? 'text-red-600' 
                          : 'text-gray-600 hover:text-red-600'
                      ]"
                    >
                      <span class="w-4 h-4 bg-red-600 rounded-sm flex items-center justify-center text-white text-xs">X</span>
                      东南亚头条
                    </button>
                    <button 
                      @click="newsTab = 'tiktok'"
                      :class="[
                        'flex items-center gap-2 px-3 py-2 text-sm transition-colors',
                        newsTab === 'tiktok' 
                          ? 'text-black' 
                          : 'text-gray-600 hover:text-black'
                      ]"
                    >
                      <span class="w-4 h-4 bg-black rounded-sm flex items-center justify-center text-white text-xs">⚡</span>
                      TikTok知识大纲
                    </button>
                    <button 
                      @click="newsTab = 'shopee'"
                      :class="[
                        'flex items-center gap-2 px-3 py-2 text-sm transition-colors',
                        newsTab === 'shopee' 
                          ? 'text-orange-600' 
                          : 'text-gray-600 hover:text-orange-600'
                      ]"
                    >
                      <span class="w-4 h-4 bg-orange-600 rounded-sm flex items-center justify-center text-white text-xs">S</span>
                      Shopee知识大纲
                    </button>
                    <button 
                      @click="newsTab = 'lazada'"
                      :class="[
                        'flex items-center gap-2 px-3 py-2 text-sm transition-colors',
                        newsTab === 'lazada' 
                          ? 'text-blue-800' 
                          : 'text-gray-600 hover:text-blue-800'
                      ]"
                    >
                      <span class="w-4 h-4 bg-blue-800 rounded-sm flex items-center justify-center text-white text-xs">L</span>
                      Lazada知识大纲
                    </button>
                    <button 
                      @click="newsTab = 'facebook'"
                      :class="[
                        'flex items-center gap-2 px-3 py-2 text-sm transition-colors',
                        newsTab === 'facebook' 
                          ? 'text-blue-600' 
                          : 'text-gray-600 hover:text-blue-600'
                      ]"
                    >
                      <span class="w-4 h-4 bg-blue-600 rounded-sm flex items-center justify-center text-white text-xs">f</span>
                      FACEBOOK
                    </button>
                    <button 
                      @click="newsTab = 'zhiwuwuyan'"
                      :class="[
                        'flex items-center gap-2 px-3 py-2 text-sm transition-colors',
                        newsTab === 'zhiwuwuyan' 
                          ? 'text-blue-500' 
                          : 'text-gray-600 hover:text-blue-500'
                      ]"
                    >
                      <span class="w-4 h-4 bg-blue-500 rounded-sm flex items-center justify-center text-white text-xs">W</span>
                      知无不言
                    </button>
                  </div>
                </div>

              </section>

              <!-- 本土后台面板 -->
              <section id="local-backend-section" class="bg-white rounded-lg shadow-lg p-6 mt-6">
                <div class="mb-6">
                  <h3 class="text-lg font-medium text-gray-800 mb-4 pb-4 border-b border-gray-200">本土后台</h3>
                  <!-- 平台入口网格 -->
                  <div class="space-y-4">
                    <!-- 第一行：橙色图标 -->
                    <div class="grid grid-cols-8 gap-4">
                      <div class="flex items-center space-x-2">
                        <div class="w-8 h-6 bg-orange-500 rounded flex items-center justify-center cursor-pointer hover:bg-orange-600 transition-colors">
                          <span class="text-white text-xs font-medium">🇵🇭</span>
                        </div>
                        <span class="text-xs text-gray-700">菲律宾后台</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-8 h-6 bg-orange-500 rounded flex items-center justify-center cursor-pointer hover:bg-orange-600 transition-colors">
                          <span class="text-white text-xs font-medium">🇲🇾</span>
                        </div>
                        <span class="text-xs text-gray-700">马来后台</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-8 h-6 bg-orange-500 rounded flex items-center justify-center cursor-pointer hover:bg-orange-600 transition-colors">
                          <span class="text-white text-xs font-medium">🇹🇭</span>
                        </div>
                        <span class="text-xs text-gray-700">泰国后台</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-8 h-6 bg-orange-500 rounded flex items-center justify-center cursor-pointer hover:bg-orange-600 transition-colors">
                          <span class="text-white text-xs font-medium">🇮🇩</span>
                        </div>
                        <span class="text-xs text-gray-700">印尼后台</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-8 h-6 bg-orange-500 rounded flex items-center justify-center cursor-pointer hover:bg-orange-600 transition-colors">
                          <span class="text-white text-xs font-medium">🇻🇳</span>
                        </div>
                        <span class="text-xs text-gray-700">越南后台</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-8 h-6 bg-orange-500 rounded flex items-center justify-center cursor-pointer hover:bg-orange-600 transition-colors">
                          <span class="text-white text-xs font-medium">🇸🇬</span>
                        </div>
                        <span class="text-xs text-gray-700">新加坡后台</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-8 h-6 bg-orange-500 rounded flex items-center justify-center cursor-pointer hover:bg-orange-600 transition-colors">
                          <span class="text-white text-xs font-medium">🇹🇼</span>
                        </div>
                        <span class="text-xs text-gray-700">台湾后台</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-8 h-6 bg-orange-500 rounded flex items-center justify-center cursor-pointer hover:bg-orange-600 transition-colors">
                          <span class="text-white text-xs font-medium">🇧🇷</span>
                        </div>
                        <span class="text-xs text-gray-700">巴西后台</span>
                      </div>
                    </div>
                    
                    <!-- 第二行：紫色图标 -->
                    <div class="grid grid-cols-8 gap-4">
                      <div class="flex items-center space-x-2">
                        <div class="w-8 h-6 bg-purple-600 rounded flex items-center justify-center cursor-pointer hover:bg-purple-700 transition-colors">
                          <span class="text-white text-xs font-medium">🇵🇭</span>
                        </div>
                        <span class="text-xs text-gray-700">菲律宾后台</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-8 h-6 bg-purple-600 rounded flex items-center justify-center cursor-pointer hover:bg-purple-700 transition-colors">
                          <span class="text-white text-xs font-medium">🇲🇾</span>
                        </div>
                        <span class="text-xs text-gray-700">马来后台</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-8 h-6 bg-purple-600 rounded flex items-center justify-center cursor-pointer hover:bg-purple-700 transition-colors">
                          <span class="text-white text-xs font-medium">🇹🇭</span>
                        </div>
                        <span class="text-xs text-gray-700">泰国后台</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-8 h-6 bg-purple-600 rounded flex items-center justify-center cursor-pointer hover:bg-purple-700 transition-colors">
                          <span class="text-white text-xs font-medium">🇮🇩</span>
                        </div>
                        <span class="text-xs text-gray-700">印尼后台</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-8 h-6 bg-purple-600 rounded flex items-center justify-center cursor-pointer hover:bg-purple-700 transition-colors">
                          <span class="text-white text-xs font-medium">🇻🇳</span>
                        </div>
                        <span class="text-xs text-gray-700">越南后台</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-8 h-6 bg-purple-600 rounded flex items-center justify-center cursor-pointer hover:bg-purple-700 transition-colors">
                          <span class="text-white text-xs font-medium">🇸🇬</span>
                        </div>
                        <span class="text-xs text-gray-700">新加坡后台</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-8 h-6 bg-purple-600 rounded flex items-center justify-center cursor-pointer hover:bg-purple-700 transition-colors">
                          <span class="text-white text-xs font-medium">🇹🇼</span>
                        </div>
                        <span class="text-xs text-gray-700">台湾后台</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-8 h-6 bg-purple-600 rounded flex items-center justify-center cursor-pointer hover:bg-purple-700 transition-colors">
                          <span class="text-white text-xs font-medium">🇧🇷</span>
                        </div>
                        <span class="text-xs text-gray-700">巴西后台</span>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              <!-- 综合软件面板 -->
              <section id="software-section" class="bg-white rounded-lg shadow-lg p-6 mt-6">
                <div class="mb-6">
                  <h3 class="text-lg font-medium text-gray-800 mb-4 pb-4 border-b border-gray-200">综合软件</h3>
                  <!-- 软件工具网格 -->
                  <div class="grid grid-cols-6 gap-4">
                    <!-- BigSellerERP -->
                    <div class="p-4 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center mb-2">
                        <div class="w-8 h-8 bg-purple-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-sm font-bold">B</span>
                        </div>
                        <h4 class="text-sm font-medium text-gray-800">BigSellerERP</h4>
                      </div>
                      <p class="text-xs text-gray-600 line-clamp-2">99%卖家首选，免费东南亚本土ERP</p>
                    </div>

                    <!-- ChatPlusAI -->
                    <div class="p-4 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center mb-2">
                        <div class="w-8 h-8 bg-blue-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-sm font-bold">C</span>
                        </div>
                        <h4 class="text-sm font-medium text-gray-800">ChatPlusAI（乐聊）</h4>
                      </div>
                      <p class="text-xs text-gray-600 line-clamp-2">Shopee/Lazada/TikTok/ 美 客 ， AI客服智能客服更加精准...</p>
                    </div>

                    <!-- 妙手ERP -->
                    <div class="p-4 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center mb-2">
                        <div class="w-8 h-8 bg-green-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-sm font-bold">妙</span>
                        </div>
                        <h4 class="text-sm font-medium text-gray-800">妙手ERP</h4>
                      </div>
                      <p class="text-xs text-gray-600 line-clamp-2">免费使用！80万+跨境卖家信赖ERP，不限单量</p>
                    </div>

                    <!-- EasyBoss ERP -->
                    <div class="p-4 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center mb-2">
                        <div class="w-8 h-8 bg-emerald-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-sm font-bold">E</span>
                        </div>
                        <h4 class="text-sm font-medium text-gray-800">EasyBoss ERP</h4>
                      </div>
                      <p class="text-xs text-gray-600 line-clamp-2">东南亚本土商家专用ERP，每月5000单免费使用不限账号</p>
                    </div>

                    <!-- 爱箱聊 AI客服 -->
                    <div class="p-4 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center mb-2">
                        <div class="w-8 h-8 bg-blue-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-sm font-bold">爱</span>
                        </div>
                        <h4 class="text-sm font-medium text-gray-800">爱箱聊 AI客服</h4>
                      </div>
                      <p class="text-xs text-gray-600 line-clamp-2">妙手旗下全平台多客服编辑服务系统，支持Shopee、Laz...</p>
                    </div>

                    <!-- 心融BI -->
                    <div class="p-4 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center mb-2">
                        <div class="w-8 h-8 bg-cyan-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-sm font-bold">心</span>
                        </div>
                        <h4 class="text-sm font-medium text-gray-800">心融BI</h4>
                      </div>
                      <p class="text-xs text-gray-600 line-clamp-2">东南亚商家数据营工具，实时监控数据/利润核算/广告...</p>
                    </div>

                    <!-- 半马浏览器 -->
                    <div class="p-4 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center mb-2">
                        <div class="w-8 h-8 bg-indigo-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-sm font-bold">半</span>
                        </div>
                        <h4 class="text-sm font-medium text-gray-800">半马浏览器</h4>
                      </div>
                      <p class="text-xs text-gray-600 line-clamp-2">跨境安全必备，600w卖家都在用，助你完全避免关联店铺</p>
                    </div>

                    <!-- 多客 AI客服 -->
                    <div class="p-4 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center mb-2">
                        <div class="w-8 h-8 bg-blue-700 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-sm font-bold">多</span>
                        </div>
                        <h4 class="text-sm font-medium text-gray-800">多客 AI客服</h4>
                      </div>
                      <p class="text-xs text-gray-600 line-clamp-2">免费使用！Shopee\TikTok\Lazada客服客，让对话简洁化</p>
                    </div>

                    <!-- 店小秘ERP -->
                    <div class="p-4 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center mb-2">
                        <div class="w-8 h-8 bg-orange-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-sm font-bold">店</span>
                        </div>
                        <h4 class="text-sm font-medium text-gray-800">店小秘ERP</h4>
                      </div>
                      <p class="text-xs text-gray-600 line-clamp-2">免费跨境电商ERP，建议130万跨境卖家的共同选择</p>
                    </div>

                    <!-- lycheejp全球代理IP -->
                    <div class="p-4 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center mb-2">
                        <div class="w-8 h-8 bg-purple-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-sm font-bold">L</span>
                        </div>
                        <h4 class="text-sm font-medium text-gray-800">lycheejp全球代理IP</h4>
                      </div>
                      <p class="text-xs text-gray-600 line-clamp-2">100%原生独享，双ISP高性能，大带宽代理</p>
                    </div>

                    <!-- 千易ERP东南亚版 -->
                    <div class="p-4 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center mb-2">
                        <div class="w-8 h-8 bg-teal-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-sm font-bold">千</span>
                        </div>
                        <h4 class="text-sm font-medium text-gray-800">千易ERP东南亚版</h4>
                      </div>
                      <p class="text-xs text-gray-600 line-clamp-2">90%虾皮卖家都在用/专为东南亚卖家打造/百万出单</p>
                    </div>

                    <!-- 台湾本土ERP -->
                    <div class="p-4 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center mb-2">
                        <div class="w-8 h-8 bg-blue-800 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-sm font-bold">台</span>
                        </div>
                        <h4 class="text-sm font-medium text-gray-800">台湾本土ERP</h4>
                      </div>
                      <p class="text-xs text-gray-600 line-clamp-2">虾皮台湾本土全套跨境ERP，不限店铺，不限订单</p>
                    </div>

                    <!-- 极风WMS -->
                    <div class="p-4 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center mb-2">
                        <div class="w-8 h-8 bg-gray-700 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-sm font-bold">极</span>
                        </div>
                        <h4 class="text-sm font-medium text-gray-800">极风WMS</h4>
                      </div>
                      <p class="text-xs text-gray-600 line-clamp-2">免费海外仓管理系统，搭建专业海外仓运营，高效便捷！</p>
                    </div>

                    <!-- 易仓ECCANG ERP -->
                    <div class="p-4 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center mb-2">
                        <div class="w-8 h-8 bg-green-700 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-sm font-bold">易</span>
                        </div>
                        <h4 class="text-sm font-medium text-gray-800">易仓ECCANG ERP</h4>
                      </div>
                      <p class="text-xs text-gray-600 line-clamp-2">免费东南亚跨境ERP，AI智能选品1天上手30分钟出单，小团队...</p>
                    </div>

                    <!-- 联水溶接货ERP -->
                    <div class="p-4 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center mb-2">
                        <div class="w-8 h-8 bg-cyan-700 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-sm font-bold">联</span>
                        </div>
                        <h4 class="text-sm font-medium text-gray-800">联水溶接货ERP</h4>
                      </div>
                      <p class="text-xs text-gray-600 line-clamp-2">一站式/跨平台/多店铺管理一体化系统</p>
                    </div>
                  </div>
                </div>
              </section>

              <!-- 收款方式面板 -->
              <section id="payment-section" class="bg-white rounded-lg shadow-lg p-6 mt-6">
                <div class="mb-6">
                  <h3 class="text-lg font-medium text-gray-800 mb-4 pb-4 border-b border-gray-200">收款方式</h3>
                  <!-- 收款方式网格 -->
                  <div class="space-y-3">
                    <!-- 第一行 -->
                    <div class="grid grid-cols-5 gap-2">
                      <!-- 连连本土回款 -->
                      <div class="flex items-center p-2 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-5 h-5 bg-blue-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-xs font-bold">连</span>
                        </div>
                        <span class="text-base text-gray-700">连连本土回款</span>
                      </div>

                      <!-- Ksher开时支付 -->
                      <div class="flex items-center p-2 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-5 h-5 bg-red-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-xs font-bold">K</span>
                        </div>
                        <span class="text-base text-gray-700">Ksher开时支付</span>
                      </div>

                      <!-- Pingpong -->
                      <div class="flex items-center p-2 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-5 h-5 bg-cyan-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-xs font-bold">P</span>
                        </div>
                        <span class="text-base text-gray-700">Pingpong</span>
                      </div>

                      <!-- Payoneer派安盈 -->
                      <div class="flex items-center p-2 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-5 h-5 bg-orange-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-xs font-bold">P</span>
                        </div>
                        <span class="text-base text-gray-700">Payoneer派安盈</span>
                      </div>

                      <!-- 寻汇SUNRATE -->
                      <div class="flex items-center p-2 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-5 h-5 bg-gray-700 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-xs font-bold">寻</span>
                        </div>
                        <span class="text-base text-gray-700">寻汇SUNRATE</span>
                      </div>
                    </div>

                    <!-- 第二行 -->
                    <div class="grid grid-cols-5 gap-2">
                      <!-- 珊瑚跨境 CoralGlo... -->
                      <div class="flex items-center p-2 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-5 h-5 bg-pink-500 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-xs font-bold">珊</span>
                        </div>
                        <span class="text-base text-gray-700">珊瑚跨境 CoralGlo...</span>
                      </div>

                      <!-- 万里汇WorldFirst -->
                      <div class="flex items-center p-2 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-5 h-5 bg-red-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-xs font-bold">万</span>
                        </div>
                        <span class="text-base text-gray-700">万里汇WorldFirst</span>
                      </div>

                      <!-- 国家外汇汇总 -->
                      <div class="flex items-center p-2 bg-gray-50 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-5 h-5 bg-yellow-600 rounded flex items-center justify-center mr-2 flex-shrink-0">
                          <span class="text-white text-xs font-bold">国</span>
                        </div>
                        <span class="text-base text-gray-700">国家外汇汇总</span>
                      </div>

                      <!-- 空位1 -->
                      <div></div>

                      <!-- 空位2 -->
                      <div></div>
                    </div>
                  </div>
                </div>
              </section>

              <!-- 东南亚物流面板 -->
              <section id="logistics-section" class="bg-white rounded-lg shadow-lg p-6 mt-6">
                <div class="mb-6">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-800">东南亚物流</h3>
                    <a href="#" class="text-blue-600 hover:text-blue-800 text-sm">更多 ></a>
                  </div>
                  
                  <!-- 物流公司网格 - 第一行 -->
                  <div class="grid grid-cols-5 gap-4 mb-4">
                    <!-- 悦物物流 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-orange-600 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-lg font-bold">悦</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">悦物物流</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">泰国、马来、菲律宾、印尼、新加坡</p>
                        </div>
                      </div>
                    </div>

                    <!-- GoLucky吉运达物流 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-red-600 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-sm font-bold">GM</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">GoLucky吉运达物流</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">菲律宾、马来、泰国、越南、印尼、新加坡</p>
                        </div>
                      </div>
                    </div>

                    <!-- 轻舟物流 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-blue-600 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-lg font-bold">轻</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">轻舟物流</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">菲律宾、马来、泰国、印尼</p>
                        </div>
                      </div>
                    </div>

                    <!-- 大泽物流 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-blue-700 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-lg font-bold">大</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">大泽物流</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">印尼、马来、菲律宾、越南、泰国</p>
                        </div>
                      </div>
                    </div>

                    <!-- B&H邦海国际货运 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-gray-800 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-xs font-bold">B&H</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">B&H邦海国际货运</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">菲律宾</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 物流公司网格 - 第二行 -->
                  <div class="grid grid-cols-5 gap-4">
                    <!-- 台海物流 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-blue-600 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-lg font-bold">台</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">台海物流（货到收）</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">中国台湾</p>
                        </div>
                      </div>
                    </div>

                    <!-- 润美国际 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-blue-500 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-lg font-bold">润</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">润美国际</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">泰国、马来、菲律宾、印尼、新加坡</p>
                        </div>
                      </div>
                    </div>

                    <!-- 广州易达货运代理 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-blue-500 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-lg font-bold">广</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">广州易达货运代理有限...</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">新加坡、马来、印尼</p>
                        </div>
                      </div>
                    </div>

                    <!-- BW蓝翼国际 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-blue-600 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-lg font-bold">BW</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">BW蓝翼国际</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">马来、新加坡、泰国、菲律宾、印尼、越南、巴西</p>
                        </div>
                      </div>
                    </div>

                    <!-- 找物流 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-green-600 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-lg font-bold">找</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">找物流</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">找物流综合服务商</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              <!-- 东南亚海外仓 -->
              <section id="warehouse-section" class="bg-white rounded-lg shadow-lg p-6 mt-6">
                <div class="mb-6">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-800">东南亚海外仓</h3>
                    <a href="#" class="text-blue-600 hover:text-blue-800 text-sm">更多 ></a>
                  </div>
                  
                  <!-- 海外仓公司网格 - 第一行 -->
                  <div class="grid grid-cols-6 gap-4 mb-4">
                    <!-- 元仓海外仓 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-red-600 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-lg font-bold">Z</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">元仓海外仓</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">越南、泰国、马来西亚、印尼、菲律宾</p>
                        </div>
                      </div>
                    </div>

                    <!-- 猴仓 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-blue-600 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-lg font-bold">猴</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">猴仓</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">马来、菲律宾、泰国、印尼、越南</p>
                        </div>
                      </div>
                    </div>

                    <!-- 易速菲海外仓 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-red-600 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-lg font-bold">易</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">易速菲海外仓</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">马来、菲律宾、泰国、印尼、越南</p>
                        </div>
                      </div>
                    </div>

                    <!-- 海仓国际 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-blue-600 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-lg font-bold">海</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">海仓国际</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">泰国、菲律宾、印尼、越南</p>
                        </div>
                      </div>
                    </div>

                    <!-- 未来云集 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-blue-600 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-lg font-bold">未</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">未来云集</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">印尼、菲律宾、泰国、新加坡、马来、越南</p>
                        </div>
                      </div>
                    </div>

                    <!-- 爱亚仓 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-orange-600 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-lg font-bold">爱</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">爱亚仓</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">马来、菲律宾、泰国、印尼、越南</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 海外仓公司网格 - 第二行 -->
                  <div class="grid grid-cols-6 gap-4 mb-4">
                    <!-- 福通海外仓 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-red-600 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-lg font-bold">福</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">福通海外仓</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">菲律宾</p>
                        </div>
                      </div>
                    </div>

                    <!-- 海星海外仓 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-blue-600 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-lg font-bold">海</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">海星海外仓</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">马来西亚、越南、泰国</p>
                        </div>
                      </div>
                    </div>

                    <!-- 斗仓海外仓 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-yellow-600 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-lg font-bold">斗</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">斗仓海外仓</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">印尼、马来、菲律宾、泰国、越南、美国、巴西</p>
                        </div>
                      </div>
                    </div>

                    <!-- 纬洋国际 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-blue-600 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-lg font-bold">纬</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">纬洋国际</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">泰国、菲律宾、马来、越南</p>
                        </div>
                      </div>
                    </div>

                    <!-- 秦鑫海外仓 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-red-600 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-lg font-bold">秦</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">秦鑫海外仓</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">泰国</p>
                        </div>
                      </div>
                    </div>

                    <!-- GoLucky吉运达海外仓 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-red-600 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-sm font-bold">GLU</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">GoLucky吉运达海外仓</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">菲律宾、马来西亚、泰国</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 海外仓公司网格 - 第三行 -->
                  <div class="grid grid-cols-6 gap-4">
                    <!-- 关键科技（深圳）有限公司 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-gray-700 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-lg font-bold">关</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">关键科技（深圳）有限公司</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">泰国、马来西亚</p>
                        </div>
                      </div>
                    </div>

                    <!-- Local海外仓 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-green-600 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-lg font-bold">L</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">Local海外仓</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">新加坡、印尼、菲律宾、泰国、马来、美国、澳洲、中东</p>
                        </div>
                      </div>
                    </div>

                    <!-- 找海外仓 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-orange-600 rounded flex items-center justify-center flex-shrink-0">
                          <span class="text-white text-lg font-bold">找</span>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 mb-1">找海外仓</h4>
                          <p class="text-xs text-gray-600 leading-relaxed">找海外仓综合服务商</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              <!-- 本地化服务 -->
              <section id="localization-section" class="bg-white rounded-lg shadow-lg p-6 mt-6">
                <div class="mb-6">
                  <h3 class="text-lg font-medium text-gray-800 mb-4 pb-4 border-b border-gray-200">本地化服务</h3>
                  <!-- 每行5个公司卡片 -->
                  <div class="grid grid-cols-5 gap-4">
                    <!-- 印尼靠谱本土资源 -->
                    <div class="bg-gray-100 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center space-x-2">
                        <div class="flex-shrink-0">
                          <div class="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">印</span>
                          </div>
                        </div>
                        <div class="flex-1">
                          <h4 class="font-medium text-gray-900 text-sm">印尼靠谱本土资源</h4>
                          <p class="text-xs text-gray-600">印尼</p>
                        </div>
                      </div>
                    </div>
                    
                    <!-- DNYECS品牌出海 -->
                    <div class="bg-gray-100 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center space-x-2">
                        <div class="flex-shrink-0">
                          <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-xs">DNY</span>
                          </div>
                        </div>
                        <div class="flex-1">
                          <h4 class="font-medium text-gray-900 text-sm">DNYECS品牌出海</h4>
                          <p class="text-xs text-gray-600">东南亚</p>
                        </div>
                      </div>
                    </div>
                    
                    <!-- 跨海跨境 -->
                    <div class="bg-gray-100 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center space-x-2">
                        <div class="flex-shrink-0">
                          <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">跨</span>
                          </div>
                        </div>
                        <div class="flex-1">
                          <h4 class="font-medium text-gray-900 text-sm">跨海跨境</h4>
                          <p class="text-xs text-gray-600">欧美、东南亚</p>
                        </div>
                      </div>
                    </div>
                    
                    <!-- 中国台湾虾皮本土资源 -->
                    <div class="bg-gray-100 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center space-x-2">
                        <div class="flex-shrink-0">
                          <div class="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">台</span>
                          </div>
                        </div>
                        <div class="flex-1">
                          <h4 class="font-medium text-gray-900 text-sm">中国台湾虾皮本土资源</h4>
                          <p class="text-xs text-gray-600">中国台湾</p>
                        </div>
                      </div>
                    </div>
                    
                    <!-- 本地认证资料汇总 -->
                    <div class="bg-gray-100 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center space-x-2">
                        <div class="flex-shrink-0">
                          <div class="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-xs">DNY</span>
                          </div>
                        </div>
                        <div class="flex-1">
                          <h4 class="font-medium text-gray-900 text-sm">本地认证资料汇总</h4>
                          <p class="text-xs text-gray-600">综合</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              <!-- 商标注册 -->
              <section id="registration-section" class="bg-white rounded-lg shadow-lg p-6 mt-6">
                <div class="mb-6">
                  <h3 class="text-lg font-medium text-gray-800 mb-4 pb-4 border-b border-gray-200">本地商标&公司注册</h3>
                  <!-- 每行3个公司卡片 -->
                  <div class="grid grid-cols-3 gap-6">
                    <!-- 开云出海 -->
                    <div class="bg-gray-100 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                          <div class="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-lg">开</span>
                          </div>
                        </div>
                        <div class="flex-1">
                          <h4 class="font-medium text-gray-900">开云出海</h4>
                          <p class="text-sm text-gray-600">菲律宾、印尼、马来、越南、缅甸</p>
                        </div>
                      </div>
                    </div>
                    
                    <!-- 安合出海 -->
                    <div class="bg-gray-100 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                          <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-lg">安</span>
                          </div>
                        </div>
                        <div class="flex-1">
                          <h4 class="font-medium text-gray-900">安合出海</h4>
                          <p class="text-sm text-gray-600">安合出海、为东盟、马来、越南、老挝等国家供商标申请服务</p>
                        </div>
                      </div>
                    </div>
                    
                    <!-- e-Eyes Compliance -->
                    <div class="bg-gray-100 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                          <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">eE</span>
                          </div>
                        </div>
                        <div class="flex-1">
                          <h4 class="font-medium text-gray-900">e-Eyes Compliance</h4>
                          <p class="text-sm text-gray-600">马来西亚印度尼西亚、认证和合规</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              <!-- TikTok常用 -->
              <section id="analysis-section" class="bg-white rounded-lg shadow-lg p-6 mt-6">
                <div class="mb-6">
                  <h3 class="text-lg font-medium text-gray-800 mb-4 pb-4 border-b border-gray-200">TikTok常用</h3>
                  <!-- TikTok常用功能网格 -->
                  <div class="space-y-3">
                    <!-- 第一行 -->
                    <div class="grid grid-cols-6 gap-3">
                      <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-6 h-6 bg-red-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-bold">TK</span>
                        </div>
                        <span class="text-sm text-gray-700">TK专用网络节点</span>
                      </div>
                      
                      <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-6 h-6 bg-gray-800 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-bold">●</span>
                        </div>
                        <span class="text-sm text-gray-700">OBS</span>
                      </div>
                      
                      <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-6 h-6 bg-pink-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-bold">🎯</span>
                        </div>
                        <span class="text-sm text-gray-700">TK邀约达人神器...</span>
                      </div>
                      
                      <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-6 h-6 bg-red-600 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-bold">🎬</span>
                        </div>
                        <span class="text-sm text-gray-700">TK直播伴侣【外】</span>
                      </div>
                      
                      <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-6 h-6 bg-cyan-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-bold">💎</span>
                        </div>
                        <span class="text-sm text-gray-700">伪装度检测</span>
                      </div>
                      
                      <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-6 h-6 bg-yellow-600 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-bold">🏷</span>
                        </div>
                        <span class="text-sm text-gray-700">TK短视频标签查询</span>
                      </div>
                    </div>
                    
                                          <!-- 第二行 -->
                      <div class="grid grid-cols-6 gap-3">
                        <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                          <div class="w-6 h-6 bg-black rounded flex items-center justify-center">
                            <span class="text-white text-xs font-bold">📋</span>
                          </div>
                          <span class="text-sm text-gray-700">入驻与注册指南</span>
                        </div>
                        
                        <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                          <div class="w-6 h-6 bg-blue-500 rounded flex items-center justify-center">
                            <span class="text-white text-xs font-bold">🍎</span>
                          </div>
                          <span class="text-sm text-gray-700">苹果id注册</span>
                        </div>
                        
                        <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                          <div class="w-6 h-6 bg-black rounded flex items-center justify-center">
                            <span class="text-white text-xs font-bold">🔧</span>
                          </div>
                          <span class="text-sm text-gray-700">安卓安装步骤</span>
                        </div>
                        
                        <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                          <div class="w-6 h-6 bg-gray-600 rounded flex items-center justify-center">
                            <span class="text-white text-xs font-bold">📊</span>
                          </div>
                          <span class="text-sm text-gray-700">提高伪装度</span>
                        </div>
                        
                        <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                          <div class="w-6 h-6 bg-black rounded flex items-center justify-center">
                            <span class="text-white text-xs font-bold">📱</span>
                          </div>
                          <span class="text-sm text-gray-700">安卓TikTok下载</span>
                        </div>
                        
                        <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                          <div class="w-6 h-6 bg-orange-500 rounded flex items-center justify-center">
                            <span class="text-white text-xs font-bold">🛠</span>
                          </div>
                          <span class="text-sm text-gray-700">TK商品搬家及优化工具</span>
                        </div>
                    </div>
                    
                                          <!-- 第三行 -->
                      <div class="grid grid-cols-6 gap-3">
                        <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                          <div class="w-6 h-6 bg-black rounded flex items-center justify-center">
                            <span class="text-white text-xs font-bold">📋</span>
                          </div>
                          <span class="text-sm text-gray-700">MCN&TAP登录</span>
                        </div>
                        
                        <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                          <div class="w-6 h-6 bg-gray-600 rounded flex items-center justify-center">
                            <span class="text-white text-xs font-bold">🏢</span>
                          </div>
                          <span class="text-sm text-gray-700">工会登录（外）</span>
                        </div>
                        
                        <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                          <div class="w-6 h-6 bg-black rounded flex items-center justify-center">
                            <span class="text-white text-xs font-bold">🏬</span>
                          </div>
                          <span class="text-sm text-gray-700">BC商务中心</span>
                        </div>
                        
                        <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                          <div class="w-6 h-6 bg-gray-600 rounded flex items-center justify-center">
                            <span class="text-white text-xs font-bold">💻</span>
                          </div>
                          <span class="text-sm text-gray-700">开发者中心</span>
                        </div>
                        
                        <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                          <div class="w-6 h-6 bg-black rounded flex items-center justify-center">
                            <span class="text-white text-xs font-bold">🎭</span>
                          </div>
                          <span class="text-sm text-gray-700">创作者市场【外】</span>
                        </div>
                        
                        <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                          <div class="w-6 h-6 bg-red-600 rounded flex items-center justify-center">
                            <span class="text-white text-xs font-bold">📺</span>
                          </div>
                          <span class="text-sm text-gray-700">直播工作台【外】</span>
                        </div>
                    </div>
                    
                                          <!-- 第四行 -->
                      <div class="grid grid-cols-6 gap-3">
                        <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                          <div class="w-6 h-6 bg-black rounded flex items-center justify-center">
                            <span class="text-white text-xs font-bold">🌐</span>
                          </div>
                          <span class="text-sm text-gray-700">TK全托管官网</span>
                        </div>
                        
                        <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                          <div class="w-6 h-6 bg-black rounded flex items-center justify-center">
                            <span class="text-white text-xs font-bold">📱</span>
                          </div>
                          <span class="text-sm text-gray-700">TK官方抖音号</span>
                        </div>
                        
                        <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer">
                          <div class="w-6 h-6 bg-orange-500 rounded flex items-center justify-center">
                            <span class="text-white text-xs font-bold">📚</span>
                          </div>
                          <span class="text-sm text-gray-700">TAP知识大纲</span>
                        </div>
                    </div>
                  </div>
                </div>
              </section>

              <!-- 打包发货 -->
              <section id="market-analysis-section" class="bg-white rounded-lg shadow-lg p-6 mt-6">
                <div class="mb-6">
                  <h3 class="text-lg font-medium text-gray-800 mb-4 pb-4 border-b border-gray-200">打包发货</h3>
                  <!-- 打包发货服务商网格 -->
                  <div class="space-y-3">
                    <!-- 第一行 -->
                    <div class="grid grid-cols-8 gap-3">
                      <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-2 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-6 h-6 bg-red-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-bold">虾</span>
                        </div>
                        <span class="text-xs text-gray-700">虾皮台湾货代</span>
                      </div>
                      
                      <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-2 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-6 h-6 bg-blue-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-bold">货</span>
                        </div>
                        <span class="text-xs text-gray-700">货小易</span>
                      </div>
                      
                      <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-2 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-6 h-6 bg-orange-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-bold">泉</span>
                        </div>
                        <span class="text-xs text-gray-700">泉州中弘达云仓</span>
                      </div>
                      
                      <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-2 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-6 h-6 bg-green-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-bold">东</span>
                        </div>
                        <span class="text-xs text-gray-700">东南亚货代/海外仓</span>
                      </div>
                      
                      <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-2 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-6 h-6 bg-cyan-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-bold">快</span>
                        </div>
                        <span class="text-xs text-gray-700">快工云仓</span>
                      </div>
                      
                      <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-2 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-6 h-6 bg-purple-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-bold">境</span>
                        </div>
                        <span class="text-xs text-gray-700">境捷一仓</span>
                      </div>
                      
                      <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-2 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-6 h-6 bg-blue-600 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-bold">L</span>
                        </div>
                        <span class="text-xs text-gray-700">Lazada100</span>
                      </div>
                      
                      <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-2 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-6 h-6 bg-yellow-600 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-bold">嘉</span>
                        </div>
                        <span class="text-xs text-gray-700">嘉吉猫代贴单</span>
                      </div>
                    </div>
                    
                    <!-- 第二行 -->
                    <div class="grid grid-cols-8 gap-3">
                      <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-2 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-6 h-6 bg-red-600 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-bold">即</span>
                        </div>
                        <span class="text-xs text-gray-700">即达代发货</span>
                      </div>
                      
                      <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-2 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-6 h-6 bg-pink-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-bold">微</span>
                        </div>
                        <span class="text-xs text-gray-700">微笑仓储</span>
                      </div>
                      
                      <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-2 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-6 h-6 bg-gray-600 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-bold">飞</span>
                        </div>
                        <span class="text-xs text-gray-700">飞马云仓</span>
                      </div>
                      
                      <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-2 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-6 h-6 bg-blue-700 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-bold">东</span>
                        </div>
                        <span class="text-xs text-gray-700">东方云仓</span>
                      </div>
                      
                      <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-2 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-6 h-6 bg-indigo-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-bold">汇</span>
                        </div>
                        <span class="text-xs text-gray-700">汇达云仓</span>
                      </div>
                      
                      <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-2 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-6 h-6 bg-teal-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-bold">鑫</span>
                        </div>
                        <span class="text-xs text-gray-700">鑫标代打包</span>
                      </div>
                      
                      <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-2 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-6 h-6 bg-emerald-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-bold">格</span>
                        </div>
                        <span class="text-xs text-gray-700">格文代发</span>
                      </div>
                      
                      <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-2 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="w-6 h-6 bg-violet-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-bold">义</span>
                        </div>
                        <span class="text-xs text-gray-700">义乌飞碟云仓</span>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              <!-- 货源网站 -->
              <section id="marketing-section" class="bg-white rounded-lg shadow-lg p-6 mt-6">
                <div class="mb-6">
                  <h3 class="text-lg font-medium text-gray-800 mb-4 pb-4 border-b border-gray-200">货源网站</h3>
                  <!-- 货源网站网格 -->
                  <div class="space-y-4">
                    <!-- 第一行 -->
                    <div class="grid grid-cols-7 gap-4">
                      <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-orange-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-medium">🏪</span>
                        </div>
                        <span class="text-xs text-gray-700">1688</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-orange-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-medium">📦</span>
                        </div>
                        <span class="text-xs text-gray-700">51货源 东南亚爆款</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-gray-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-medium">📊</span>
                        </div>
                        <span class="text-xs text-gray-700">万跨供应链</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-red-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-medium">🏪</span>
                        </div>
                        <span class="text-xs text-gray-700">中国制造</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-orange-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-medium">🌐</span>
                        </div>
                        <span class="text-xs text-gray-700">淘宝网</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-orange-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-medium">🏢</span>
                        </div>
                        <span class="text-xs text-gray-700">阿里国际</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-blue-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-medium">📦</span>
                        </div>
                        <span class="text-xs text-gray-700">购途网GO2</span>
                      </div>
                    </div>
                    
                    <!-- 第二行 -->
                    <div class="grid grid-cols-7 gap-4">
                      <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-orange-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-medium">🚢</span>
                        </div>
                        <span class="text-xs text-gray-700">CJDropshipping</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-pink-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-medium">🛒</span>
                        </div>
                        <span class="text-xs text-gray-700">一起做网店</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-red-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-medium">🔍</span>
                        </div>
                        <span class="text-xs text-gray-700">搜款网</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-blue-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-medium">🛒</span>
                        </div>
                        <span class="text-xs text-gray-700">网商园</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-yellow-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-medium">🛍️</span>
                        </div>
                        <span class="text-xs text-gray-700">义乌购</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-gray-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-medium">📦</span>
                        </div>
                        <span class="text-xs text-gray-700">批发户</span>
                      </div>
                      
                      <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-purple-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-medium">🌍</span>
                        </div>
                        <span class="text-xs text-gray-700">西之月全球货盘</span>
                      </div>
                    </div>

                    <!-- 第三行 -->
                    <div class="grid grid-cols-7 gap-4">
                      <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-blue-500 rounded flex items-center justify-center">
                          <span class="text-white text-xs font-medium">⚙️</span>
                        </div>
                        <span class="text-xs text-gray-700">SDS定制</span>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              <!-- 其他工具 -->
              <section id="report-section" class="bg-white rounded-lg shadow-lg p-6 mt-6">
                <div class="mb-6">
                  <h3 class="text-lg font-medium text-gray-800 mb-4 pb-4 border-b border-gray-200">其他工具</h3>
                  <!-- 其他工具网格 -->
                  <div class="space-y-4">
                    <!-- 软件工具部分 -->
                    <div class="flex items-center gap-4">
                      <span class="text-sm text-red-500 font-medium">软件工具：</span>
                      <div class="flex items-center gap-6 flex-wrap">
                        <div class="text-sm text-gray-700 hover:text-blue-600 cursor-pointer">妙手</div>
                        <div class="text-sm text-gray-700 hover:text-blue-600 cursor-pointer">芒果店长</div>
                        <div class="text-sm text-gray-700 hover:text-blue-600 cursor-pointer">奢优云ERP</div>
                        <div class="text-sm text-gray-700 hover:text-blue-600 cursor-pointer">易仓WMS</div>
                        <div class="text-sm text-gray-700 hover:text-blue-600 cursor-pointer">旺销王</div>
                        <div class="text-sm text-gray-700 hover:text-blue-600 cursor-pointer">在线定价表</div>
                        <div class="text-sm text-gray-700 hover:text-blue-600 cursor-pointer">汇率换算</div>
                        <div class="text-sm text-gray-700 hover:text-blue-600 cursor-pointer">关键词热度</div>
                      </div>
                    </div>
                    
                    <!-- 孵化营训部分 -->
                    <div class="flex items-center gap-4">
                      <span class="text-sm text-red-500 font-medium">孵化营训：</span>
                      <div class="flex items-center gap-6 flex-wrap">
                        <div class="text-sm text-gray-700 hover:text-blue-600 cursor-pointer">暴走蜗牛</div>
                        <div class="text-sm text-gray-700 hover:text-blue-600 cursor-pointer">跨境深蓝海</div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              <!-- 月度报告 -->
              <section id="other-section" class="bg-white rounded-lg shadow-lg p-6 mt-6">
                <div class="mb-6">
                  <!-- 标题栏 -->
                  <div class="flex justify-between items-center mb-4 pb-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-800">月度报告</h3>
                    <a href="#" class="text-sm text-red-500 hover:text-red-600">更多报告></a>
                  </div>
                  
                  <!-- 报告卡片网格 -->
                  <div class="grid grid-cols-6 gap-4">
                    <!-- 2025年5月报告 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center mb-2">
                        <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-2">
                          <span class="text-white text-xs">📊</span>
                        </div>
                        <span class="text-sm font-medium text-gray-800">2025年5月报告</span>
                      </div>
                      <p class="text-xs text-gray-600 line-clamp-2">两大巨头们的敦峰重要转载2.0%！5月东南亚市场平台最...</p>
                    </div>
                    
                    <!-- 2025年4月报告 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center mb-2">
                        <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-2">
                          <span class="text-white text-xs">📊</span>
                        </div>
                        <span class="text-sm font-medium text-gray-800">2025年4月报告</span>
                      </div>
                      <p class="text-xs text-gray-600 line-clamp-2">两大巨头最高敲定分走该内市场！4月东南亚电商平台最新...</p>
                    </div>
                    
                    <!-- 2025年3月报告 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center mb-2">
                        <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-2">
                          <span class="text-white text-xs">📊</span>
                        </div>
                        <span class="text-sm font-medium text-gray-800">2025年3月报告</span>
                      </div>
                      <p class="text-xs text-gray-600 line-clamp-2">访问整体较上月增涨4000万！3月东南亚电商平台增幅新...</p>
                    </div>
                    
                    <!-- 2025年2月报告 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center mb-2">
                        <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-2">
                          <span class="text-white text-xs">📊</span>
                        </div>
                        <span class="text-sm font-medium text-gray-800">2025年2月报告</span>
                      </div>
                      <p class="text-xs text-gray-600 line-clamp-2">两大巨头多站点获量爆增1.0%！2月东南亚电商平台增...</p>
                    </div>
                    
                    <!-- 2025年1月报告 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center mb-2">
                        <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-2">
                          <span class="text-white text-xs">📊</span>
                        </div>
                        <span class="text-sm font-medium text-gray-800">2025年1月报告</span>
                      </div>
                      <p class="text-xs text-gray-600 line-clamp-2">希望电商销量环涨23.70%！1月东南亚电商平台数据出炉...</p>
                    </div>
                    
                    <!-- 2024年12月报告 -->
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <div class="flex items-center mb-2">
                        <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-2">
                          <span class="text-white text-xs">📊</span>
                        </div>
                        <span class="text-sm font-medium text-gray-800">2024年12月报告</span>
                      </div>
                      <p class="text-xs text-gray-600 line-clamp-2">购买量激增96.1%！12月东南亚电商平台最新增量...</p>
                    </div>
                  </div>
                </div>
              </section>

              <!-- 其他 -->
              <section id="zhiwuwuyan" class="bg-white rounded-lg shadow-lg p-6 mt-6">
                <div class="mb-6">
                  <!-- 国家/地区标题导航 -->
                  <div class="flex items-center justify-between mb-6 border-b border-gray-200 pb-4">
                    <div class="flex items-center space-x-6">
                      <h3 
                        @click="activeOtherTab = 'other'"
                        :class="[
                          'text-lg font-medium cursor-pointer pb-2 transition-all duration-200 relative',
                          activeOtherTab === 'other' 
                            ? 'text-gray-800 border-b-3 border-orange-500' 
                            : 'text-gray-500 hover:text-gray-700'
                        ]"
                      >
                        其他
                        <div v-if="activeOtherTab === 'other'" class="absolute bottom-0 left-0 w-full h-0.5 bg-orange-500 rounded-full"></div>
                      </h3>
                      <h3 
                        @click="activeOtherTab = 'indonesia'"
                        :class="[
                          'text-lg font-medium cursor-pointer pb-2 transition-all duration-200 relative',
                          activeOtherTab === 'indonesia' 
                            ? 'text-gray-800 border-b-3 border-orange-500' 
                            : 'text-gray-500 hover:text-gray-700'
                        ]"
                      >
                        印尼
                        <div v-if="activeOtherTab === 'indonesia'" class="absolute bottom-0 left-0 w-full h-0.5 bg-orange-500 rounded-full"></div>
                      </h3>
                      <h3 
                        @click="activeOtherTab = 'malaysia'"
                        :class="[
                          'text-lg font-medium cursor-pointer pb-2 transition-all duration-200 relative',
                          activeOtherTab === 'malaysia' 
                            ? 'text-gray-800 border-b-3 border-orange-500' 
                            : 'text-gray-500 hover:text-gray-700'
                        ]"
                      >
                        马来西亚
                        <div v-if="activeOtherTab === 'malaysia'" class="absolute bottom-0 left-0 w-full h-0.5 bg-orange-500 rounded-full"></div>
                      </h3>
                      <h3 
                        @click="activeOtherTab = 'singapore'"
                        :class="[
                          'text-lg font-medium cursor-pointer pb-2 transition-all duration-200 relative',
                          activeOtherTab === 'singapore' 
                            ? 'text-gray-800 border-b-3 border-orange-500' 
                            : 'text-gray-500 hover:text-gray-700'
                        ]"
                      >
                        新加坡
                        <div v-if="activeOtherTab === 'singapore'" class="absolute bottom-0 left-0 w-full h-0.5 bg-orange-500 rounded-full"></div>
                      </h3>
                      <h3 
                        @click="activeOtherTab = 'vietnam'"
                        :class="[
                          'text-lg font-medium cursor-pointer pb-2 transition-all duration-200 relative',
                          activeOtherTab === 'vietnam' 
                            ? 'text-gray-800 border-b-3 border-orange-500' 
                            : 'text-gray-500 hover:text-gray-700'
                        ]"
                      >
                        越南
                        <div v-if="activeOtherTab === 'vietnam'" class="absolute bottom-0 left-0 w-full h-0.5 bg-orange-500 rounded-full"></div>
                      </h3>
                      <h3 
                        @click="activeOtherTab = 'philippines'"
                        :class="[
                          'text-lg font-medium cursor-pointer pb-2 transition-all duration-200 relative',
                          activeOtherTab === 'philippines' 
                            ? 'text-gray-800 border-b-3 border-orange-500' 
                            : 'text-gray-500 hover:text-gray-700'
                        ]"
                      >
                        菲律宾
                        <div v-if="activeOtherTab === 'philippines'" class="absolute bottom-0 left-0 w-full h-0.5 bg-orange-500 rounded-full"></div>
                      </h3>
                      <h3 
                        @click="activeOtherTab = 'thailand'"
                        :class="[
                          'text-lg font-medium cursor-pointer pb-2 transition-all duration-200 relative',
                          activeOtherTab === 'thailand' 
                            ? 'text-gray-800 border-b-3 border-orange-500' 
                            : 'text-gray-500 hover:text-gray-700'
                        ]"
                      >
                        泰国
                        <div v-if="activeOtherTab === 'thailand'" class="absolute bottom-0 left-0 w-full h-0.5 bg-orange-500 rounded-full"></div>
                      </h3>
                      <h3 
                        @click="activeOtherTab = 'taiwan'"
                        :class="[
                          'text-lg font-medium cursor-pointer pb-2 transition-all duration-200 relative',
                          activeOtherTab === 'taiwan' 
                            ? 'text-gray-800 border-b-3 border-orange-500' 
                            : 'text-gray-500 hover:text-gray-700'
                        ]"
                      >
                        中国台湾
                        <div v-if="activeOtherTab === 'taiwan'" class="absolute bottom-0 left-0 w-full h-0.5 bg-orange-500 rounded-full"></div>
                      </h3>
                      <h3 
                        @click="activeOtherTab = 'korea'"
                        :class="[
                          'text-lg font-medium cursor-pointer pb-2 transition-all duration-200 relative',
                          activeOtherTab === 'korea' 
                            ? 'text-gray-800 border-b-3 border-orange-500' 
                            : 'text-gray-500 hover:text-gray-700'
                        ]"
                      >
                        韩国
                        <div v-if="activeOtherTab === 'korea'" class="absolute bottom-0 left-0 w-full h-0.5 bg-orange-500 rounded-full"></div>
                      </h3>
                    </div>
                  </div>
                  
                  <!-- 电商平台列表 - 其他 -->
                  <div v-if="activeOtherTab === 'other'" class="grid grid-cols-5 gap-4">
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-green-500 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">🛒</span>
                      </div>
                      <span class="text-sm text-gray-700">Tokopedia</span>
                    </div>
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-red-500 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">L</span>
                      </div>
                      <span class="text-sm text-gray-700">L192</span>
                    </div>
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-orange-500 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">T</span>
                      </div>
                      <span class="text-sm text-gray-700">Thisshop</span>
                    </div>
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-red-600 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">A</span>
                      </div>
                      <span class="text-sm text-gray-700">Akulaku</span>
                    </div>
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-gray-500 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">Z</span>
                      </div>
                      <span class="text-sm text-gray-700">Zilingo</span>
                    </div>
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-blue-500 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">T</span>
                      </div>
                      <span class="text-sm text-gray-700">Tiki</span>
                    </div>
                  </div>
                  
                  <!-- 电商平台列表 - 印尼 -->
                  <div v-if="activeOtherTab === 'indonesia'" class="grid grid-cols-5 gap-4">
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-green-500 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">T</span>
                      </div>
                      <span class="text-sm text-gray-700">Tokopedia</span>
                    </div>
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-blue-500 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">T</span>
                      </div>
                      <span class="text-sm text-gray-700">Tiki</span>
                    </div>
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-orange-500 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">S</span>
                      </div>
                      <span class="text-sm text-gray-700">Shopee印尼</span>
                    </div>
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-blue-600 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">L</span>
                      </div>
                      <span class="text-sm text-gray-700">Lazada印尼</span>
                    </div>
                  </div>
                  
                  <!-- 电商平台列表 - 马来西亚 -->
                  <div v-if="activeOtherTab === 'malaysia'" class="grid grid-cols-5 gap-4">
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-orange-500 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">S</span>
                      </div>
                      <span class="text-sm text-gray-700">Shopee马来</span>
                    </div>
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-blue-600 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">L</span>
                      </div>
                      <span class="text-sm text-gray-700">Lazada马来</span>
                    </div>
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-purple-500 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">P</span>
                      </div>
                      <span class="text-sm text-gray-700">PGMall</span>
                    </div>
                  </div>
                  
                  <!-- 电商平台列表 - 新加坡 -->
                  <div v-if="activeOtherTab === 'singapore'" class="grid grid-cols-5 gap-4">
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-orange-500 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">S</span>
                      </div>
                      <span class="text-sm text-gray-700">Shopee新加坡</span>
                    </div>
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-blue-600 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">L</span>
                      </div>
                      <span class="text-sm text-gray-700">Lazada新加坡</span>
                    </div>
                  </div>
                  
                  <!-- 电商平台列表 - 越南 -->
                  <div v-if="activeOtherTab === 'vietnam'" class="grid grid-cols-5 gap-4">
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-orange-500 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">S</span>
                      </div>
                      <span class="text-sm text-gray-700">Shopee越南</span>
                    </div>
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-blue-600 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">L</span>
                      </div>
                      <span class="text-sm text-gray-700">Lazada越南</span>
                    </div>
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-red-500 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">T</span>
                      </div>
                      <span class="text-sm text-gray-700">Tiki越南</span>
                    </div>
                  </div>
                  
                  <!-- 电商平台列表 - 菲律宾 -->
                  <div v-if="activeOtherTab === 'philippines'" class="grid grid-cols-5 gap-4">
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-orange-500 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">S</span>
                      </div>
                      <span class="text-sm text-gray-700">Shopee菲律宾</span>
                    </div>
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-blue-600 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">L</span>
                      </div>
                      <span class="text-sm text-gray-700">Lazada菲律宾</span>
                    </div>
                  </div>
                  
                  <!-- 电商平台列表 - 泰国 -->
                  <div v-if="activeOtherTab === 'thailand'" class="grid grid-cols-5 gap-4">
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-orange-500 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">S</span>
                      </div>
                      <span class="text-sm text-gray-700">Shopee泰国</span>
                    </div>
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-blue-600 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">L</span>
                      </div>
                      <span class="text-sm text-gray-700">Lazada泰国</span>
                    </div>
                  </div>
                  
                  <!-- 电商平台列表 - 中国台湾 -->
                  <div v-if="activeOtherTab === 'taiwan'" class="grid grid-cols-5 gap-4">
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-orange-500 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">S</span>
                      </div>
                      <span class="text-sm text-gray-700">Shopee台湾</span>
                    </div>
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-green-500 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">P</span>
                      </div>
                      <span class="text-sm text-gray-700">PChome</span>
                    </div>
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-yellow-500 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">M</span>
                      </div>
                      <span class="text-sm text-gray-700">Momo购物</span>
                    </div>
                  </div>
                  
                  <!-- 电商平台列表 - 韩国 -->
                  <div v-if="activeOtherTab === 'korea'" class="grid grid-cols-5 gap-4">
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-red-500 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">G</span>
                      </div>
                      <span class="text-sm text-gray-700">Gmarket</span>
                    </div>
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-blue-500 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">C</span>
                      </div>
                      <span class="text-sm text-gray-700">Coupang</span>
                    </div>
                    <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div class="w-4 h-4 bg-purple-500 rounded-sm flex items-center justify-center">
                        <span class="text-white text-xs">11</span>
                      </div>
                      <span class="text-sm text-gray-700">11st</span>
                    </div>
                  </div>
                </div>
              </section>

              <!-- 市场分析 -->
              <section id="market-analysis" class="bg-white rounded-lg shadow-lg p-6 mt-6">
                <div class="mb-6">
                  <h3 class="text-lg font-medium text-gray-800 mb-6 pb-4 border-b border-gray-200">市场分析</h3>
                  
                  <!-- 市场分析列表 -->
                  <div class="grid grid-cols-5 gap-4">
                    <!-- 印尼市场 -->
                    <div class="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                      <img src="/images/countries/id_flag.png" alt="印尼" class="w-6 h-4 rounded">
                      <span class="text-sm text-gray-700">印尼市场</span>
                    </div>
                    
                    <!-- 马来市场 -->
                    <div class="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                      <img src="/images/countries/my_flag.png" alt="马来西亚" class="w-6 h-4 rounded">
                      <span class="text-sm text-gray-700">马来市场</span>
                    </div>
                    
                    <!-- 泰国市场 -->
                    <div class="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                      <img src="/images/countries/th_flag.png" alt="泰国" class="w-6 h-4 rounded">
                      <span class="text-sm text-gray-700">泰国市场</span>
                    </div>
                    
                    <!-- 菲律宾市场 -->
                    <div class="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                      <img src="/images/countries/ph_flag.png" alt="菲律宾" class="w-6 h-4 rounded">
                      <span class="text-sm text-gray-700">菲律宾市场</span>
                    </div>
                    
                    <!-- 越南市场 -->
                    <div class="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                      <img src="/images/countries/vn_flag.png" alt="越南" class="w-6 h-4 rounded">
                      <span class="text-sm text-gray-700">越南市场</span>
                    </div>
                    
                    <!-- 新加坡市场 -->
                    <div class="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                      <img src="/images/countries/sg_flag.png" alt="新加坡" class="w-6 h-4 rounded">
                      <span class="text-sm text-gray-700">新加坡市场</span>
                    </div>
                    
                    <!-- 中国台湾市场 -->
                    <div class="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                      <img src="/images/countries/tw_flag.png" alt="中国台湾" class="w-6 h-4 rounded">
                      <span class="text-sm text-gray-700">中国台湾市场</span>
                    </div>
                    
                    <!-- 巴西市场 -->
                    <div class="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                      <img src="/images/countries/br_flag.png" alt="巴西" class="w-6 h-4 rounded">
                      <span class="text-sm text-gray-700">巴西市场</span>
                    </div>
                    
                    <!-- 墨西哥市场 -->
                    <div class="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                      <img src="/images/countries/mx_flag.png" alt="墨西哥" class="w-6 h-4 rounded">
                      <span class="text-sm text-gray-700">墨西哥市场</span>
                    </div>
                    
                    <!-- 哥伦比亚市场 -->
                    <div class="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                      <div class="w-6 h-4 bg-yellow-400 rounded flex items-center justify-center">
                        <span class="text-xs text-blue-800 font-bold">C</span>
                      </div>
                      <span class="text-sm text-gray-700">哥伦比亚市场</span>
                    </div>
                  </div>
                </div>
              </section>
            </div>

            </div>
          </div>
        </div>
      </div>

  
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// 动态时间显示
const currentTime = ref('')
let timeInterval = null

const updateTime = () => {
  const now = new Date()
  
  // 各国时间计算
  const options = {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }
  
  // 更新时间显示
  currentTime.value = now.toLocaleDateString('zh-CN') + ' ' + 
    now.toLocaleTimeString('zh-CN', options)
}

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
  
  // 添加滚动监听
  window.addEventListener('scroll', handleScroll)
  
  // 初始化时检查当前位置
  handleScroll()
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
  
  // 清理滚动监听
  window.removeEventListener('scroll', handleScroll)
  
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }
})

// 节日倒计时计算
const festivals = ref([
  { name: '春节', date: '2025-01-29', color: 'red' },
  { name: '开斋节', date: '2025-03-30', color: 'green' },
  { name: '泰国泼水节', date: '2025-04-13', color: 'blue' },
  { name: '6.6年中大促', date: '2025-06-06', color: 'purple' },
  { name: '618', date: '2025-06-18', color: 'orange' }
])

// 计算距离天数
const getDaysUntil = (dateString) => {
  const targetDate = new Date(dateString)
  const today = new Date()
  const diffTime = targetDate - today
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays > 0 ? diffDays : 0
}

// 搜索引擎配置
const searchEngines = ref([
  { name: '百度', placeholder: '百度搜索' },
  { name: 'Google', placeholder: 'Google 搜索' },
  { name: '马来', placeholder: '搜索马来站点' },
  { name: '新加坡', placeholder: '搜索新加坡站点' },
  { name: '印尼', placeholder: '搜索印尼站点' },
  { name: '菲律宾', placeholder: '搜索菲律宾站点' },
  { name: '泰国', placeholder: '搜索泰国站点' },
  { name: '台湾', placeholder: '搜索台湾站点' },
  { name: '越南', placeholder: '搜索越南站点' },
  { name: '巴西', placeholder: '搜索巴西站点' }
])

// 搜索功能
const searchQuery = ref('')
const selectedEngine = ref(searchEngines.value[0]) // 默认选择百度
const isInputFocused = ref(false)

// 选择搜索引擎
const selectEngine = (engine) => {
  selectedEngine.value = engine
  searchQuery.value = '' // 清空搜索内容
}

const handleSearch = () => {
  if (searchQuery.value.trim()) {
    // 这里可以添加搜索逻辑
    console.log('搜索:', searchQuery.value, '引擎:', selectedEngine.value.name)
  }
}

// 标签页管理
const activeTab = ref('tools')
const customTab = ref('website') // 自定义网址标签页状态
const newsTab = ref('headlines') // 跨境资讯标签页状态
const activeOtherTab = ref('other') // 其他面板的活跃tab

// 导航定位功能
const activeSection = ref('shopee-section') // 当前激活的区域
const navBar = ref(null) // 导航栏引用
const isNavSticky = ref(false) // 是否粘性定位
const navBarStyle = ref({
  top: '240px',
  left: 'calc(15% + 16px)' // 15%容器边距 + 16px间距
}) // 导航栏样式
let scrollTimeout = null
let initialNavTop = 240 // 导航栏初始top值

// 滚动到指定区域
const scrollToSection = (sectionId) => {
  const element = document.getElementById(sectionId)
  if (element) {
    const headerOffset = 100 // 预留顶部空间
    const elementPosition = element.getBoundingClientRect().top
    const offsetPosition = elementPosition + window.pageYOffset - headerOffset
    
    window.scrollTo({
      top: offsetPosition,
      behavior: 'smooth'
    })
    
    // 立即更新激活状态
    activeSection.value = sectionId
  }
}

// 更新导航栏位置
const updateNavBarPosition = () => {
  const scrollY = window.scrollY
  const triggerPoint = initialNavTop // 触发点为初始位置
  
  if (scrollY >= triggerPoint) {
    // 滚动超过触发点，切换为固定定位
    isNavSticky.value = true
          navBarStyle.value = {
        top: '20px', // 固定在顶部20px
        left: 'calc(15% + 16px)' // 15%容器边距 + 16px间距
      }
  } else {
    // 未超过触发点，使用绝对定位跟随滚动
    isNavSticky.value = false
          navBarStyle.value = {
        top: `${initialNavTop - scrollY}px`, // 跟随滚动
        left: 'calc(15% + 16px)' // 15%容器边距 + 16px间距
      }
  }
}

// 监听滚动事件，自动更新导航高亮和位置
const handleScroll = () => {
  // 防抖处理
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }
  
  scrollTimeout = setTimeout(() => {
    // 更新导航栏位置
    updateNavBarPosition()
    
    // 更新活动区域
    const sections = [
      'shopee-section',
      'tiktok-section', 
      'lazada-section',
      'tools-section',
      'custom-urls-section',
      'news-section',
      'local-backend-section',
      'software-section',
      'payment-section',
      'logistics-section',
      'warehouse-section',
      'localization-section',
      'market-analysis'
    ]
    
    const scrollPosition = window.scrollY + 150 // 偏移量调整
    
    for (let i = sections.length - 1; i >= 0; i--) {
      const element = document.getElementById(sections[i])
      if (element && element.offsetTop <= scrollPosition) {
        activeSection.value = sections[i]
        break
      }
    }
  }, 10) // 减少防抖时间以获得更流畅的效果
}
</script> 

<style scoped>
/* 自定义滚动条 */
.scrollbar-hide {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* 搜索引擎按钮优化 */
@media (max-width: 768px) {
  .search-engines {
    gap: 0.5rem;
      }
  }
</style> 