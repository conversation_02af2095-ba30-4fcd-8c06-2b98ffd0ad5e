import { defineStore } from 'pinia'

export const useSearchStore = defineStore('search', {
  state: () => ({
    searchQuery: '',
    isInputFocused: false,
    selectedEngine: null as any,
    
    // 搜索引擎配置
    searchEngines: [
      { name: '百度', placeholder: '百度搜索' },
      { name: 'Google', placeholder: 'Google 搜索' },
      { name: '马来', placeholder: '搜索马来站点' },
      { name: '新加坡', placeholder: '搜索新加坡站点' },
      { name: '印尼', placeholder: '搜索印尼站点' },
      { name: '菲律宾', placeholder: '搜索菲律宾站点' },
      { name: '泰国', placeholder: '搜索泰国站点' },
      { name: '台湾', placeholder: '搜索台湾站点' },
      { name: '越南', placeholder: '搜索越南站点' },
      { name: '巴西', placeholder: '搜索巴西站点' }
    ]
  }),

  actions: {
    initializeSearch() {
      // 默认选择百度
      this.selectedEngine = this.searchEngines[0]
    },

    selectEngine(engine: any) {
      this.selectedEngine = engine
      this.searchQuery = '' // 清空搜索内容
    },

    setSearchQuery(query: string) {
      this.searchQuery = query
    },

    setInputFocused(focused: boolean) {
      this.isInputFocused = focused
    },

    handleSearch() {
      if (this.searchQuery.trim()) {
        console.log('搜索:', this.searchQuery, '引擎:', this.selectedEngine?.name)
        // 这里可以添加具体的搜索逻辑
      }
    }
  }
}) 