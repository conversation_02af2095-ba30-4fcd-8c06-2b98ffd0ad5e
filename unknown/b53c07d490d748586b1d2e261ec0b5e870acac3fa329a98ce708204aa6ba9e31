/**
 * name: sdk
 * version: v1.1.0
 * author: amz123
 * date: 2025-04-09 17:20:49
  */
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))r(a);new MutationObserver(a=>{for(const o of a)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(a){const o={};return a.integrity&&(o.integrity=a.integrity),a.referrerpolicy&&(o.referrerPolicy=a.referrerpolicy),a.crossorigin==="use-credentials"?o.credentials="include":a.crossorigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(a){if(a.ep)return;a.ep=!0;const o=n(a);fetch(a.href,o)}})();function ca(e,t){return function(){return e.apply(t,arguments)}}const{toString:la}=Object.prototype,{getPrototypeOf:Wn}=Object,Yn=(e=>t=>{const n=la.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),Pe=e=>(e=e.toLowerCase(),t=>Yn(t)===e),nn=e=>t=>typeof t===e,{isArray:ft}=Array,Ot=nn("undefined");function qo(e){return e!==null&&!Ot(e)&&e.constructor!==null&&!Ot(e.constructor)&&Xe(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const da=Pe("ArrayBuffer");function Po(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&da(e.buffer),t}const Ao=nn("string"),Xe=nn("function"),ua=nn("number"),Vn=e=>e!==null&&typeof e=="object",No=e=>e===!0||e===!1,jt=e=>{if(Yn(e)!=="object")return!1;const t=Wn(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},Mo=Pe("Date"),Do=Pe("File"),Io=Pe("Blob"),Ro=Pe("FileList"),$o=e=>Vn(e)&&Xe(e.pipe),Bo=e=>{const t="[object FormData]";return e&&(typeof FormData=="function"&&e instanceof FormData||la.call(e)===t||Xe(e.toString)&&e.toString()===t)},Fo=Pe("URLSearchParams"),jo=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Pt(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,a;if(typeof e!="object"&&(e=[e]),ft(e))for(r=0,a=e.length;r<a;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),s=o.length;let c;for(r=0;r<s;r++)c=o[r],t.call(null,e[c],c,e)}}function ma(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,a;for(;r-- >0;)if(a=n[r],t===a.toLowerCase())return a;return null}const pa=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),fa=e=>!Ot(e)&&e!==pa;function Pn(){const{caseless:e}=fa(this)&&this||{},t={},n=(r,a)=>{const o=e&&ma(t,a)||a;jt(t[o])&&jt(r)?t[o]=Pn(t[o],r):jt(r)?t[o]=Pn({},r):ft(r)?t[o]=r.slice():t[o]=r};for(let r=0,a=arguments.length;r<a;r++)arguments[r]&&Pt(arguments[r],n);return t}const Uo=(e,t,n,{allOwnKeys:r}={})=>(Pt(t,(a,o)=>{n&&Xe(a)?e[o]=ca(a,n):e[o]=a},{allOwnKeys:r}),e),Ho=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Wo=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Yo=(e,t,n,r)=>{let a,o,s;const c={};if(t=t||{},e==null)return t;do{for(a=Object.getOwnPropertyNames(e),o=a.length;o-- >0;)s=a[o],(!r||r(s,e,t))&&!c[s]&&(t[s]=e[s],c[s]=!0);e=n!==!1&&Wn(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Vo=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},Jo=e=>{if(!e)return null;if(ft(e))return e;let t=e.length;if(!ua(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Go=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Wn(Uint8Array)),Xo=(e,t)=>{const r=(e&&e[Symbol.iterator]).call(e);let a;for(;(a=r.next())&&!a.done;){const o=a.value;t.call(e,o[0],o[1])}},Zo=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Qo=Pe("HTMLFormElement"),Ko=e=>e.toLowerCase().replace(/[_-\s]([a-z\d])(\w*)/g,function(n,r,a){return r.toUpperCase()+a}),Sr=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),ei=Pe("RegExp"),ha=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Pt(n,(a,o)=>{t(a,o,e)!==!1&&(r[o]=a)}),Object.defineProperties(e,r)},ti=e=>{ha(e,(t,n)=>{if(Xe(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(!!Xe(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},ni=(e,t)=>{const n={},r=a=>{a.forEach(o=>{n[o]=!0})};return ft(e)?r(e):r(String(e).split(t)),n},ri=()=>{},ai=(e,t)=>(e=+e,Number.isFinite(e)?e:t),oi=e=>{const t=new Array(10),n=(r,a)=>{if(Vn(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[a]=r;const o=ft(r)?[]:{};return Pt(r,(s,c)=>{const l=n(s,a+1);!Ot(l)&&(o[c]=l)}),t[a]=void 0,o}}return r};return n(e,0)},C={isArray:ft,isArrayBuffer:da,isBuffer:qo,isFormData:Bo,isArrayBufferView:Po,isString:Ao,isNumber:ua,isBoolean:No,isObject:Vn,isPlainObject:jt,isUndefined:Ot,isDate:Mo,isFile:Do,isBlob:Io,isRegExp:ei,isFunction:Xe,isStream:$o,isURLSearchParams:Fo,isTypedArray:Go,isFileList:Ro,forEach:Pt,merge:Pn,extend:Uo,trim:jo,stripBOM:Ho,inherits:Wo,toFlatObject:Yo,kindOf:Yn,kindOfTest:Pe,endsWith:Vo,toArray:Jo,forEachEntry:Xo,matchAll:Zo,isHTMLForm:Qo,hasOwnProperty:Sr,hasOwnProp:Sr,reduceDescriptors:ha,freezeMethods:ti,toObjectSet:ni,toCamelCase:Ko,noop:ri,toFiniteNumber:ai,findKey:ma,global:pa,isContextDefined:fa,toJSONObject:oi};function W(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a)}C.inherits(W,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:C.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});const va=W.prototype,ga={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{ga[e]={value:e}});Object.defineProperties(W,ga);Object.defineProperty(va,"isAxiosError",{value:!0});W.from=(e,t,n,r,a,o)=>{const s=Object.create(va);return C.toFlatObject(e,s,function(l){return l!==Error.prototype},c=>c!=="isAxiosError"),W.call(s,e.message,t,n,r,a),s.cause=e,s.name=e.name,o&&Object.assign(s,o),s};var kt=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function ii(e){var t=e.default;if(typeof t=="function"){var n=function(){return t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var a=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,a.get?a:{enumerable:!0,get:function(){return e[r]}})}),n}var si=typeof self=="object"?self.FormData:window.FormData;const ci=si;function An(e){return C.isPlainObject(e)||C.isArray(e)}function ya(e){return C.endsWith(e,"[]")?e.slice(0,-2):e}function zr(e,t,n){return e?e.concat(t).map(function(a,o){return a=ya(a),!n&&o?"["+a+"]":a}).join(n?".":""):t}function li(e){return C.isArray(e)&&!e.some(An)}const di=C.toFlatObject(C,{},null,function(t){return/^is[A-Z]/.test(t)});function ui(e){return e&&C.isFunction(e.append)&&e[Symbol.toStringTag]==="FormData"&&e[Symbol.iterator]}function rn(e,t,n){if(!C.isObject(e))throw new TypeError("target must be an object");t=t||new(ci||FormData),n=C.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(v,y){return!C.isUndefined(y[v])});const r=n.metaTokens,a=n.visitor||d,o=n.dots,s=n.indexes,l=(n.Blob||typeof Blob<"u"&&Blob)&&ui(t);if(!C.isFunction(a))throw new TypeError("visitor must be a function");function i(h){if(h===null)return"";if(C.isDate(h))return h.toISOString();if(!l&&C.isBlob(h))throw new W("Blob is not supported. Use a Buffer instead.");return C.isArrayBuffer(h)||C.isTypedArray(h)?l&&typeof Blob=="function"?new Blob([h]):Buffer.from(h):h}function d(h,v,y){let k=h;if(h&&!y&&typeof h=="object"){if(C.endsWith(v,"{}"))v=r?v:v.slice(0,-2),h=JSON.stringify(h);else if(C.isArray(h)&&li(h)||C.isFileList(h)||C.endsWith(v,"[]")&&(k=C.toArray(h)))return v=ya(v),k.forEach(function(g,w){!(C.isUndefined(g)||g===null)&&t.append(s===!0?zr([v],w,o):s===null?v:v+"[]",i(g))}),!1}return An(h)?!0:(t.append(zr(y,v,o),i(h)),!1)}const p=[],u=Object.assign(di,{defaultVisitor:d,convertValue:i,isVisitable:An});function f(h,v){if(!C.isUndefined(h)){if(p.indexOf(h)!==-1)throw Error("Circular reference detected in "+v.join("."));p.push(h),C.forEach(h,function(k,m){(!(C.isUndefined(k)||k===null)&&a.call(t,k,C.isString(m)?m.trim():m,v,u))===!0&&f(k,v?v.concat(m):[m])}),p.pop()}}if(!C.isObject(e))throw new TypeError("data must be an object");return f(e),t}function Er(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function Jn(e,t){this._pairs=[],e&&rn(e,this,t)}const wa=Jn.prototype;wa.append=function(t,n){this._pairs.push([t,n])};wa.toString=function(t){const n=t?function(r){return t.call(this,r,Er)}:Er;return this._pairs.map(function(a){return n(a[0])+"="+n(a[1])},"").join("&")};function mi(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ba(e,t,n){if(!t)return e;const r=n&&n.encode||mi,a=n&&n.serialize;let o;if(a?o=a(t,n):o=C.isURLSearchParams(t)?t.toString():new Jn(t,n).toString(r),o){const s=e.indexOf("#");s!==-1&&(e=e.slice(0,s)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class pi{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){C.forEach(this.handlers,function(r){r!==null&&t(r)})}}const kr=pi,_a={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},fi=typeof URLSearchParams<"u"?URLSearchParams:Jn,hi=FormData,vi=(()=>{let e;return typeof navigator<"u"&&((e=navigator.product)==="ReactNative"||e==="NativeScript"||e==="NS")?!1:typeof window<"u"&&typeof document<"u"})(),gi=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),ze={isBrowser:!0,classes:{URLSearchParams:fi,FormData:hi,Blob},isStandardBrowserEnv:vi,isStandardBrowserWebWorkerEnv:gi,protocols:["http","https","file","blob","url","data"]};function yi(e,t){return rn(e,new ze.classes.URLSearchParams,Object.assign({visitor:function(n,r,a,o){return ze.isNode&&C.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function wi(e){return C.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function bi(e){const t={},n=Object.keys(e);let r;const a=n.length;let o;for(r=0;r<a;r++)o=n[r],t[o]=e[o];return t}function xa(e){function t(n,r,a,o){let s=n[o++];const c=Number.isFinite(+s),l=o>=n.length;return s=!s&&C.isArray(a)?a.length:s,l?(C.hasOwnProp(a,s)?a[s]=[a[s],r]:a[s]=r,!c):((!a[s]||!C.isObject(a[s]))&&(a[s]=[]),t(n,r,a[s],o)&&C.isArray(a[s])&&(a[s]=bi(a[s])),!c)}if(C.isFormData(e)&&C.isFunction(e.entries)){const n={};return C.forEachEntry(e,(r,a)=>{t(wi(r),a,n,0)}),n}return null}const _i={"Content-Type":void 0};function xi(e,t,n){if(C.isString(e))try{return(t||JSON.parse)(e),C.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const an={transitional:_a,adapter:["xhr","http"],transformRequest:[function(t,n){const r=n.getContentType()||"",a=r.indexOf("application/json")>-1,o=C.isObject(t);if(o&&C.isHTMLForm(t)&&(t=new FormData(t)),C.isFormData(t))return a&&a?JSON.stringify(xa(t)):t;if(C.isArrayBuffer(t)||C.isBuffer(t)||C.isStream(t)||C.isFile(t)||C.isBlob(t))return t;if(C.isArrayBufferView(t))return t.buffer;if(C.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let c;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return yi(t,this.formSerializer).toString();if((c=C.isFileList(t))||r.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return rn(c?{"files[]":t}:t,l&&new l,this.formSerializer)}}return o||a?(n.setContentType("application/json",!1),xi(t)):t}],transformResponse:[function(t){const n=this.transitional||an.transitional,r=n&&n.forcedJSONParsing,a=this.responseType==="json";if(t&&C.isString(t)&&(r&&!this.responseType||a)){const s=!(n&&n.silentJSONParsing)&&a;try{return JSON.parse(t)}catch(c){if(s)throw c.name==="SyntaxError"?W.from(c,W.ERR_BAD_RESPONSE,this,null,this.response):c}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ze.classes.FormData,Blob:ze.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};C.forEach(["delete","get","head"],function(t){an.headers[t]={}});C.forEach(["post","put","patch"],function(t){an.headers[t]=C.merge(_i)});const Gn=an,Si=C.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),zi=e=>{const t={};let n,r,a;return e&&e.split(`
`).forEach(function(s){a=s.indexOf(":"),n=s.substring(0,a).trim().toLowerCase(),r=s.substring(a+1).trim(),!(!n||t[n]&&Si[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Cr=Symbol("internals");function bt(e){return e&&String(e).trim().toLowerCase()}function Ut(e){return e===!1||e==null?e:C.isArray(e)?e.map(Ut):String(e)}function Ei(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}function ki(e){return/^[-_a-zA-Z]+$/.test(e.trim())}function Lr(e,t,n,r){if(C.isFunction(r))return r.call(this,t,n);if(!!C.isString(t)){if(C.isString(r))return t.indexOf(r)!==-1;if(C.isRegExp(r))return r.test(t)}}function Ci(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function Li(e,t){const n=C.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(a,o,s){return this[r].call(this,t,a,o,s)},configurable:!0})})}class on{constructor(t){t&&this.set(t)}set(t,n,r){const a=this;function o(c,l,i){const d=bt(l);if(!d)throw new Error("header name must be a non-empty string");const p=C.findKey(a,d);(!p||a[p]===void 0||i===!0||i===void 0&&a[p]!==!1)&&(a[p||l]=Ut(c))}const s=(c,l)=>C.forEach(c,(i,d)=>o(i,d,l));return C.isPlainObject(t)||t instanceof this.constructor?s(t,n):C.isString(t)&&(t=t.trim())&&!ki(t)?s(zi(t),n):t!=null&&o(n,t,r),this}get(t,n){if(t=bt(t),t){const r=C.findKey(this,t);if(r){const a=this[r];if(!n)return a;if(n===!0)return Ei(a);if(C.isFunction(n))return n.call(this,a,r);if(C.isRegExp(n))return n.exec(a);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=bt(t),t){const r=C.findKey(this,t);return!!(r&&(!n||Lr(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let a=!1;function o(s){if(s=bt(s),s){const c=C.findKey(r,s);c&&(!n||Lr(r,r[c],c,n))&&(delete r[c],a=!0)}}return C.isArray(t)?t.forEach(o):o(t),a}clear(){return Object.keys(this).forEach(this.delete.bind(this))}normalize(t){const n=this,r={};return C.forEach(this,(a,o)=>{const s=C.findKey(r,o);if(s){n[s]=Ut(a),delete n[o];return}const c=t?Ci(o):String(o).trim();c!==o&&delete n[o],n[c]=Ut(a),r[c]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return C.forEach(this,(r,a)=>{r!=null&&r!==!1&&(n[a]=t&&C.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(a=>r.set(a)),r}static accessor(t){const r=(this[Cr]=this[Cr]={accessors:{}}).accessors,a=this.prototype;function o(s){const c=bt(s);r[c]||(Li(a,s),r[c]=!0)}return C.isArray(t)?t.forEach(o):o(t),this}}on.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent"]);C.freezeMethods(on.prototype);C.freezeMethods(on);const Oe=on;function xn(e,t){const n=this||Gn,r=t||n,a=Oe.from(r.headers);let o=r.data;return C.forEach(e,function(c){o=c.call(n,o,a.normalize(),t?t.status:void 0)}),a.normalize(),o}function Sa(e){return!!(e&&e.__CANCEL__)}function At(e,t,n){W.call(this,e==null?"canceled":e,W.ERR_CANCELED,t,n),this.name="CanceledError"}C.inherits(At,W,{__CANCEL__:!0});const Ti=null;function Oi(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new W("Request failed with status code "+n.status,[W.ERR_BAD_REQUEST,W.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}const qi=ze.isStandardBrowserEnv?function(){return{write:function(n,r,a,o,s,c){const l=[];l.push(n+"="+encodeURIComponent(r)),C.isNumber(a)&&l.push("expires="+new Date(a).toGMTString()),C.isString(o)&&l.push("path="+o),C.isString(s)&&l.push("domain="+s),c===!0&&l.push("secure"),document.cookie=l.join("; ")},read:function(n){const r=document.cookie.match(new RegExp("(^|;\\s*)("+n+")=([^;]*)"));return r?decodeURIComponent(r[3]):null},remove:function(n){this.write(n,"",Date.now()-864e5)}}}():function(){return{write:function(){},read:function(){return null},remove:function(){}}}();function Pi(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Ai(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}function za(e,t){return e&&!Pi(t)?Ai(e,t):t}const Ni=ze.isStandardBrowserEnv?function(){const t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");let r;function a(o){let s=o;return t&&(n.setAttribute("href",s),s=n.href),n.setAttribute("href",s),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:n.pathname.charAt(0)==="/"?n.pathname:"/"+n.pathname}}return r=a(window.location.href),function(s){const c=C.isString(s)?a(s):s;return c.protocol===r.protocol&&c.host===r.host}}():function(){return function(){return!0}}();function Mi(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Di(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a=0,o=0,s;return t=t!==void 0?t:1e3,function(l){const i=Date.now(),d=r[o];s||(s=i),n[a]=l,r[a]=i;let p=o,u=0;for(;p!==a;)u+=n[p++],p=p%e;if(a=(a+1)%e,a===o&&(o=(o+1)%e),i-s<t)return;const f=d&&i-d;return f?Math.round(u*1e3/f):void 0}}function Tr(e,t){let n=0;const r=Di(50,250);return a=>{const o=a.loaded,s=a.lengthComputable?a.total:void 0,c=o-n,l=r(c),i=o<=s;n=o;const d={loaded:o,total:s,progress:s?o/s:void 0,bytes:c,rate:l||void 0,estimated:l&&s&&i?(s-o)/l:void 0,event:a};d[t?"download":"upload"]=!0,e(d)}}const Ii=typeof XMLHttpRequest<"u",Ri=Ii&&function(e){return new Promise(function(n,r){let a=e.data;const o=Oe.from(e.headers).normalize(),s=e.responseType;let c;function l(){e.cancelToken&&e.cancelToken.unsubscribe(c),e.signal&&e.signal.removeEventListener("abort",c)}C.isFormData(a)&&(ze.isStandardBrowserEnv||ze.isStandardBrowserWebWorkerEnv)&&o.setContentType(!1);let i=new XMLHttpRequest;if(e.auth){const f=e.auth.username||"",h=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";o.set("Authorization","Basic "+btoa(f+":"+h))}const d=za(e.baseURL,e.url);i.open(e.method.toUpperCase(),ba(d,e.params,e.paramsSerializer),!0),i.timeout=e.timeout;function p(){if(!i)return;const f=Oe.from("getAllResponseHeaders"in i&&i.getAllResponseHeaders()),v={data:!s||s==="text"||s==="json"?i.responseText:i.response,status:i.status,statusText:i.statusText,headers:f,config:e,request:i};Oi(function(k){n(k),l()},function(k){r(k),l()},v),i=null}if("onloadend"in i?i.onloadend=p:i.onreadystatechange=function(){!i||i.readyState!==4||i.status===0&&!(i.responseURL&&i.responseURL.indexOf("file:")===0)||setTimeout(p)},i.onabort=function(){!i||(r(new W("Request aborted",W.ECONNABORTED,e,i)),i=null)},i.onerror=function(){r(new W("Network Error",W.ERR_NETWORK,e,i)),i=null},i.ontimeout=function(){let h=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded";const v=e.transitional||_a;e.timeoutErrorMessage&&(h=e.timeoutErrorMessage),r(new W(h,v.clarifyTimeoutError?W.ETIMEDOUT:W.ECONNABORTED,e,i)),i=null},ze.isStandardBrowserEnv){const f=(e.withCredentials||Ni(d))&&e.xsrfCookieName&&qi.read(e.xsrfCookieName);f&&o.set(e.xsrfHeaderName,f)}a===void 0&&o.setContentType(null),"setRequestHeader"in i&&C.forEach(o.toJSON(),function(h,v){i.setRequestHeader(v,h)}),C.isUndefined(e.withCredentials)||(i.withCredentials=!!e.withCredentials),s&&s!=="json"&&(i.responseType=e.responseType),typeof e.onDownloadProgress=="function"&&i.addEventListener("progress",Tr(e.onDownloadProgress,!0)),typeof e.onUploadProgress=="function"&&i.upload&&i.upload.addEventListener("progress",Tr(e.onUploadProgress)),(e.cancelToken||e.signal)&&(c=f=>{!i||(r(!f||f.type?new At(null,e,i):f),i.abort(),i=null)},e.cancelToken&&e.cancelToken.subscribe(c),e.signal&&(e.signal.aborted?c():e.signal.addEventListener("abort",c)));const u=Mi(d);if(u&&ze.protocols.indexOf(u)===-1){r(new W("Unsupported protocol "+u+":",W.ERR_BAD_REQUEST,e));return}i.send(a||null)})},Ht={http:Ti,xhr:Ri};C.forEach(Ht,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const $i={getAdapter:e=>{e=C.isArray(e)?e:[e];const{length:t}=e;let n,r;for(let a=0;a<t&&(n=e[a],!(r=C.isString(n)?Ht[n.toLowerCase()]:n));a++);if(!r)throw r===!1?new W(`Adapter ${n} is not supported by the environment`,"ERR_NOT_SUPPORT"):new Error(C.hasOwnProp(Ht,n)?`Adapter '${n}' is not available in the build`:`Unknown adapter '${n}'`);if(!C.isFunction(r))throw new TypeError("adapter is not a function");return r},adapters:Ht};function Sn(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new At(null,e)}function Or(e){return Sn(e),e.headers=Oe.from(e.headers),e.data=xn.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),$i.getAdapter(e.adapter||Gn.adapter)(e).then(function(r){return Sn(e),r.data=xn.call(e,e.transformResponse,r),r.headers=Oe.from(r.headers),r},function(r){return Sa(r)||(Sn(e),r&&r.response&&(r.response.data=xn.call(e,e.transformResponse,r.response),r.response.headers=Oe.from(r.response.headers))),Promise.reject(r)})}const qr=e=>e instanceof Oe?e.toJSON():e;function st(e,t){t=t||{};const n={};function r(i,d,p){return C.isPlainObject(i)&&C.isPlainObject(d)?C.merge.call({caseless:p},i,d):C.isPlainObject(d)?C.merge({},d):C.isArray(d)?d.slice():d}function a(i,d,p){if(C.isUndefined(d)){if(!C.isUndefined(i))return r(void 0,i,p)}else return r(i,d,p)}function o(i,d){if(!C.isUndefined(d))return r(void 0,d)}function s(i,d){if(C.isUndefined(d)){if(!C.isUndefined(i))return r(void 0,i)}else return r(void 0,d)}function c(i,d,p){if(p in t)return r(i,d);if(p in e)return r(void 0,i)}const l={url:o,method:o,data:o,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:c,headers:(i,d)=>a(qr(i),qr(d),!0)};return C.forEach(Object.keys(e).concat(Object.keys(t)),function(d){const p=l[d]||a,u=p(e[d],t[d],d);C.isUndefined(u)&&p!==c||(n[d]=u)}),n}const Ea="1.2.2",Xn={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Xn[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Pr={};Xn.transitional=function(t,n,r){function a(o,s){return"[Axios v"+Ea+"] Transitional option '"+o+"'"+s+(r?". "+r:"")}return(o,s,c)=>{if(t===!1)throw new W(a(s," has been removed"+(n?" in "+n:"")),W.ERR_DEPRECATED);return n&&!Pr[s]&&(Pr[s]=!0,console.warn(a(s," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,s,c):!0}};function Bi(e,t,n){if(typeof e!="object")throw new W("options must be an object",W.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const o=r[a],s=t[o];if(s){const c=e[o],l=c===void 0||s(c,o,e);if(l!==!0)throw new W("option "+o+" must be "+l,W.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new W("Unknown option "+o,W.ERR_BAD_OPTION)}}const Nn={assertOptions:Bi,validators:Xn},De=Nn.validators;class Vt{constructor(t){this.defaults=t,this.interceptors={request:new kr,response:new kr}}request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=st(this.defaults,n);const{transitional:r,paramsSerializer:a,headers:o}=n;r!==void 0&&Nn.assertOptions(r,{silentJSONParsing:De.transitional(De.boolean),forcedJSONParsing:De.transitional(De.boolean),clarifyTimeoutError:De.transitional(De.boolean)},!1),a!==void 0&&Nn.assertOptions(a,{encode:De.function,serialize:De.function},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let s;s=o&&C.merge(o.common,o[n.method]),s&&C.forEach(["delete","get","head","post","put","patch","common"],h=>{delete o[h]}),n.headers=Oe.concat(s,o);const c=[];let l=!0;this.interceptors.request.forEach(function(v){typeof v.runWhen=="function"&&v.runWhen(n)===!1||(l=l&&v.synchronous,c.unshift(v.fulfilled,v.rejected))});const i=[];this.interceptors.response.forEach(function(v){i.push(v.fulfilled,v.rejected)});let d,p=0,u;if(!l){const h=[Or.bind(this),void 0];for(h.unshift.apply(h,c),h.push.apply(h,i),u=h.length,d=Promise.resolve(n);p<u;)d=d.then(h[p++],h[p++]);return d}u=c.length;let f=n;for(p=0;p<u;){const h=c[p++],v=c[p++];try{f=h(f)}catch(y){v.call(this,y);break}}try{d=Or.call(this,f)}catch(h){return Promise.reject(h)}for(p=0,u=i.length;p<u;)d=d.then(i[p++],i[p++]);return d}getUri(t){t=st(this.defaults,t);const n=za(t.baseURL,t.url);return ba(n,t.params,t.paramsSerializer)}}C.forEach(["delete","get","head","options"],function(t){Vt.prototype[t]=function(n,r){return this.request(st(r||{},{method:t,url:n,data:(r||{}).data}))}});C.forEach(["post","put","patch"],function(t){function n(r){return function(o,s,c){return this.request(st(c||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:s}))}}Vt.prototype[t]=n(),Vt.prototype[t+"Form"]=n(!0)});const Wt=Vt;class Zn{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(a=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](a);r._listeners=null}),this.promise.then=a=>{let o;const s=new Promise(c=>{r.subscribe(c),o=c}).then(a);return s.cancel=function(){r.unsubscribe(o)},s},t(function(o,s,c){r.reason||(r.reason=new At(o,s,c),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}static source(){let t;return{token:new Zn(function(a){t=a}),cancel:t}}}const Fi=Zn;function ji(e){return function(n){return e.apply(null,n)}}function Ui(e){return C.isObject(e)&&e.isAxiosError===!0}const Mn={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Mn).forEach(([e,t])=>{Mn[t]=e});const Hi=Mn;function ka(e){const t=new Wt(e),n=ca(Wt.prototype.request,t);return C.extend(n,Wt.prototype,t,{allOwnKeys:!0}),C.extend(n,t,null,{allOwnKeys:!0}),n.create=function(a){return ka(st(e,a))},n}const ne=ka(Gn);ne.Axios=Wt;ne.CanceledError=At;ne.CancelToken=Fi;ne.isCancel=Sa;ne.VERSION=Ea;ne.toFormData=rn;ne.AxiosError=W;ne.Cancel=ne.CanceledError;ne.all=function(t){return Promise.all(t)};ne.spread=ji;ne.isAxiosError=Ui;ne.mergeConfig=st;ne.AxiosHeaders=Oe;ne.formToJSON=e=>xa(C.isHTMLForm(e)?new FormData(e):e);ne.HttpStatusCode=Hi;ne.default=ne;const Nt=ne,{Axios:Fm,AxiosError:Ca,CanceledError:jm,isCancel:Um,CancelToken:Hm,VERSION:Wm,all:Ym,Cancel:Vm,isAxiosError:Jm,spread:Gm,toFormData:Xm,AxiosHeaders:Zm,HttpStatusCode:Qm,formToJSON:Km,mergeConfig:ep}=Nt,La={},Wi=Object.freeze(Object.defineProperty({__proto__:null,default:La},Symbol.toStringTag,{value:"Module"}));function Yi(){for(var e=document.cookie.split(";"),t=0;t<e.length;t++){var n=e[t],r=n.indexOf("="),a=r>-1?n.substr(0,r):n;a.includes("bbs_token")&&(document.cookie="".concat(a,"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/"))}}var le=function(){return le=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},le.apply(this,arguments)};function F(e,t,n,r){return new(n||(n=Promise))((a,o)=>{function s(i){try{l(r.next(i))}catch(d){o(d)}}function c(i){try{l(r.throw(i))}catch(d){o(d)}}function l(i){var d;i.done?a(i.value):(d=i.value,d instanceof n?d:new n(p=>{p(d)})).then(s,c)}l((r=r.apply(e,t||[])).next())})}function j(e,t){var n,r,a,o,s={label:0,sent:()=>{if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:c(0),throw:c(1),return:c(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function c(l){return i=>function(d){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,d[0]&&(s=0)),s;)try{if(n=1,r&&(a=2&d[0]?r.return:d[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,d[1])).done)return a;switch(r=0,a&&(d=[2&d[0],a.value]),d[0]){case 0:case 1:a=d;break;case 4:return s.label++,{value:d[1],done:!1};case 5:s.label++,r=d[1],d=[0];continue;case 7:d=s.ops.pop(),s.trys.pop();continue;default:if(a=s.trys,!((a=a.length>0&&a[a.length-1])||d[0]!==6&&d[0]!==2)){s=0;continue}if(d[0]===3&&(!a||d[1]>a[0]&&d[1]<a[3])){s.label=d[1];break}if(d[0]===6&&s.label<a[1]){s.label=a[1],a=d;break}if(a&&s.label<a[2]){s.label=a[2],s.ops.push(d);break}a[2]&&s.ops.pop(),s.trys.pop();continue}d=t.call(e,s)}catch(p){d=[6,p],r=0}finally{n=a=0}if(5&d[0])throw d[1];return{value:d[0]?d[1]:void 0,done:!0}}([l,i])}}function fe(e,t,n){if(n||arguments.length===2)for(var r,a=0,o=t.length;a<o;a++)!r&&a in t||(r||(r=Array.prototype.slice.call(t,0,a)),r[a]=t[a]);return e.concat(r||Array.prototype.slice.call(t))}var Qn="amz123-global-user-info",ct=()=>{if(globalThis.document===void 0)throw new Error("\u5F53\u524D\u4E0D\u518D\u5BA2\u6237\u7AEF\u8FD0\u884C\u73AF\u5883")};function lt(){ct(),localStorage.removeItem(Qn),function(){ct();for(var e=document.cookie.split(";"),t=0;t<e.length;t++){var n=e[t],r=n.indexOf("="),a=r>-1?n.substr(0,r):n;a.includes("bbs_token")&&(document.cookie="".concat(a,"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/"))}}()}var Ta=()=>(ct(),document.cookie.replace(/(?:(?:^|.*;\s*)bbs_token\s*\=\s*([^;]*).*$)|^.*$/,"$1"));function Oa(){ct();var e=Kn();return e?e==null?void 0:e.token:void 0}function Kn(){var e;ct();try{var t=localStorage.getItem(Qn);return t?new Date().valueOf()/1e3>((e=JSON.parse(t).expire)!==null&&e!==void 0?e:0)?void lt():JSON.parse(t):void 0}catch{return}}function Vi(e){ct();try{localStorage.setItem(Qn,JSON.stringify(e))}catch{}}var zn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function qa(e,t){return e(t={exports:{}},t.exports),t.exports}var Ji=qa(function(e,t){var n;e.exports=(n=n||function(r,a){var o;if(typeof window<"u"&&window.crypto&&(o=window.crypto),typeof self<"u"&&self.crypto&&(o=self.crypto),typeof globalThis<"u"&&globalThis.crypto&&(o=globalThis.crypto),!o&&typeof window<"u"&&window.msCrypto&&(o=window.msCrypto),!o&&zn!==void 0&&zn.crypto&&(o=zn.crypto),!o)try{o=La}catch{}var s=()=>{if(o){if(typeof o.getRandomValues=="function")try{return o.getRandomValues(new Uint32Array(1))[0]}catch{}if(typeof o.randomBytes=="function")try{return o.randomBytes(4).readInt32LE()}catch{}}throw new Error("Native crypto module could not be used to get secure random number.")},c=Object.create||(()=>{function m(){}return g=>{var w;return m.prototype=g,w=new m,m.prototype=null,w}})(),l={},i=l.lib={},d=i.Base={extend:function(m){var g=c(this);return m&&g.mixIn(m),g.hasOwnProperty("init")&&this.init!==g.init||(g.init=function(){g.$super.init.apply(this,arguments)}),g.init.prototype=g,g.$super=this,g},create:function(){var m=this.extend();return m.init.apply(m,arguments),m},init:()=>{},mixIn:function(m){for(var g in m)m.hasOwnProperty(g)&&(this[g]=m[g]);m.hasOwnProperty("toString")&&(this.toString=m.toString)},clone:function(){return this.init.prototype.extend(this)}},p=i.WordArray=d.extend({init:function(m,g){m=this.words=m||[],this.sigBytes=g!=a?g:4*m.length},toString:function(m){return(m||f).stringify(this)},concat:function(m){var g=this.words,w=m.words,E=this.sigBytes,z=m.sigBytes;if(this.clamp(),E%4)for(var T=0;T<z;T++){var O=w[T>>>2]>>>24-T%4*8&255;g[E+T>>>2]|=O<<24-(E+T)%4*8}else for(var q=0;q<z;q+=4)g[E+q>>>2]=w[q>>>2];return this.sigBytes+=z,this},clamp:function(){var m=this.words,g=this.sigBytes;m[g>>>2]&=4294967295<<32-g%4*8,m.length=r.ceil(g/4)},clone:function(){var m=d.clone.call(this);return m.words=this.words.slice(0),m},random:m=>{for(var g=[],w=0;w<m;w+=4)g.push(s());return new p.init(g,m)}}),u=l.enc={},f=u.Hex={stringify:m=>{for(var g=m.words,w=m.sigBytes,E=[],z=0;z<w;z++){var T=g[z>>>2]>>>24-z%4*8&255;E.push((T>>>4).toString(16)),E.push((15&T).toString(16))}return E.join("")},parse:m=>{for(var g=m.length,w=[],E=0;E<g;E+=2)w[E>>>3]|=parseInt(m.substr(E,2),16)<<24-E%8*4;return new p.init(w,g/2)}},h=u.Latin1={stringify:m=>{for(var g=m.words,w=m.sigBytes,E=[],z=0;z<w;z++){var T=g[z>>>2]>>>24-z%4*8&255;E.push(String.fromCharCode(T))}return E.join("")},parse:m=>{for(var g=m.length,w=[],E=0;E<g;E++)w[E>>>2]|=(255&m.charCodeAt(E))<<24-E%4*8;return new p.init(w,g)}},v=u.Utf8={stringify:m=>{try{return decodeURIComponent(escape(h.stringify(m)))}catch{throw new Error("Malformed UTF-8 data")}},parse:m=>h.parse(unescape(encodeURIComponent(m)))},y=i.BufferedBlockAlgorithm=d.extend({reset:function(){this._data=new p.init,this._nDataBytes=0},_append:function(m){typeof m=="string"&&(m=v.parse(m)),this._data.concat(m),this._nDataBytes+=m.sigBytes},_process:function(m){var g,w=this._data,E=w.words,z=w.sigBytes,T=this.blockSize,O=z/(4*T),q=(O=m?r.ceil(O):r.max((0|O)-this._minBufferSize,0))*T,D=r.min(4*q,z);if(q){for(var A=0;A<q;A+=T)this._doProcessBlock(E,A);g=E.splice(0,q),w.sigBytes-=D}return new p.init(g,D)},clone:function(){var m=d.clone.call(this);return m._data=this._data.clone(),m},_minBufferSize:0});i.Hasher=y.extend({cfg:d.extend(),init:function(m){this.cfg=this.cfg.extend(m),this.reset()},reset:function(){y.reset.call(this),this._doReset()},update:function(m){return this._append(m),this._process(),this},finalize:function(m){return m&&this._append(m),this._doFinalize()},blockSize:16,_createHelper:m=>(g,w)=>new m.init(w).finalize(g),_createHmacHelper:m=>(g,w)=>new k.HMAC.init(m,w).finalize(g)});var k=l.algo={};return l}(Math),n)}),er=qa(function(e,t){var n;e.exports=(n=Ji,function(r){var a=n,o=a.lib,s=o.WordArray,c=o.Hasher,l=a.algo,i=[];(()=>{for(var v=0;v<64;v++)i[v]=4294967296*r.abs(r.sin(v+1))|0})();var d=l.MD5=c.extend({_doReset:function(){this._hash=new s.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(v,y){for(var k=0;k<16;k++){var m=y+k,g=v[m];v[m]=16711935&(g<<8|g>>>24)|4278255360&(g<<24|g>>>8)}var w=this._hash.words,E=v[y+0],z=v[y+1],T=v[y+2],O=v[y+3],q=v[y+4],D=v[y+5],A=v[y+6],P=v[y+7],B=v[y+8],R=v[y+9],I=v[y+10],Y=v[y+11],U=v[y+12],G=v[y+13],oe=v[y+14],K=v[y+15],b=w[0],S=w[1],_=w[2],x=w[3];b=p(b,S,_,x,E,7,i[0]),x=p(x,b,S,_,z,12,i[1]),_=p(_,x,b,S,T,17,i[2]),S=p(S,_,x,b,O,22,i[3]),b=p(b,S,_,x,q,7,i[4]),x=p(x,b,S,_,D,12,i[5]),_=p(_,x,b,S,A,17,i[6]),S=p(S,_,x,b,P,22,i[7]),b=p(b,S,_,x,B,7,i[8]),x=p(x,b,S,_,R,12,i[9]),_=p(_,x,b,S,I,17,i[10]),S=p(S,_,x,b,Y,22,i[11]),b=p(b,S,_,x,U,7,i[12]),x=p(x,b,S,_,G,12,i[13]),_=p(_,x,b,S,oe,17,i[14]),b=u(b,S=p(S,_,x,b,K,22,i[15]),_,x,z,5,i[16]),x=u(x,b,S,_,A,9,i[17]),_=u(_,x,b,S,Y,14,i[18]),S=u(S,_,x,b,E,20,i[19]),b=u(b,S,_,x,D,5,i[20]),x=u(x,b,S,_,I,9,i[21]),_=u(_,x,b,S,K,14,i[22]),S=u(S,_,x,b,q,20,i[23]),b=u(b,S,_,x,R,5,i[24]),x=u(x,b,S,_,oe,9,i[25]),_=u(_,x,b,S,O,14,i[26]),S=u(S,_,x,b,B,20,i[27]),b=u(b,S,_,x,G,5,i[28]),x=u(x,b,S,_,T,9,i[29]),_=u(_,x,b,S,P,14,i[30]),b=f(b,S=u(S,_,x,b,U,20,i[31]),_,x,D,4,i[32]),x=f(x,b,S,_,B,11,i[33]),_=f(_,x,b,S,Y,16,i[34]),S=f(S,_,x,b,oe,23,i[35]),b=f(b,S,_,x,z,4,i[36]),x=f(x,b,S,_,q,11,i[37]),_=f(_,x,b,S,P,16,i[38]),S=f(S,_,x,b,I,23,i[39]),b=f(b,S,_,x,G,4,i[40]),x=f(x,b,S,_,E,11,i[41]),_=f(_,x,b,S,O,16,i[42]),S=f(S,_,x,b,A,23,i[43]),b=f(b,S,_,x,R,4,i[44]),x=f(x,b,S,_,U,11,i[45]),_=f(_,x,b,S,K,16,i[46]),b=h(b,S=f(S,_,x,b,T,23,i[47]),_,x,E,6,i[48]),x=h(x,b,S,_,P,10,i[49]),_=h(_,x,b,S,oe,15,i[50]),S=h(S,_,x,b,D,21,i[51]),b=h(b,S,_,x,U,6,i[52]),x=h(x,b,S,_,O,10,i[53]),_=h(_,x,b,S,I,15,i[54]),S=h(S,_,x,b,z,21,i[55]),b=h(b,S,_,x,B,6,i[56]),x=h(x,b,S,_,K,10,i[57]),_=h(_,x,b,S,A,15,i[58]),S=h(S,_,x,b,G,21,i[59]),b=h(b,S,_,x,q,6,i[60]),x=h(x,b,S,_,Y,10,i[61]),_=h(_,x,b,S,T,15,i[62]),S=h(S,_,x,b,R,21,i[63]),w[0]=w[0]+b|0,w[1]=w[1]+S|0,w[2]=w[2]+_|0,w[3]=w[3]+x|0},_doFinalize:function(){var v=this._data,y=v.words,k=8*this._nDataBytes,m=8*v.sigBytes;y[m>>>5]|=128<<24-m%32;var g=r.floor(k/4294967296),w=k;y[15+(m+64>>>9<<4)]=16711935&(g<<8|g>>>24)|4278255360&(g<<24|g>>>8),y[14+(m+64>>>9<<4)]=16711935&(w<<8|w>>>24)|4278255360&(w<<24|w>>>8),v.sigBytes=4*(y.length+1),this._process();for(var E=this._hash,z=E.words,T=0;T<4;T++){var O=z[T];z[T]=16711935&(O<<8|O>>>24)|4278255360&(O<<24|O>>>8)}return E},clone:function(){var v=c.clone.call(this);return v._hash=this._hash.clone(),v}});function p(v,y,k,m,g,w,E){var z=v+(y&k|~y&m)+g+E;return(z<<w|z>>>32-w)+y}function u(v,y,k,m,g,w,E){var z=v+(y&m|k&~m)+g+E;return(z<<w|z>>>32-w)+y}function f(v,y,k,m,g,w,E){var z=v+(y^k^m)+g+E;return(z<<w|z>>>32-w)+y}function h(v,y,k,m,g,w,E){var z=v+(k^(y|~m))+g+E;return(z<<w|z>>>32-w)+y}a.MD5=c._createHelper(d),a.HmacMD5=c._createHmacHelper(d)}(Math),n.MD5)}),Dn=Nt.create({timeout:6e3,withCredentials:!0,headers:{"Content-Type":"application/json","Access-Control-Allow-Origin":"*"}}),Ar=[110,301,302,304,305,306,11001,11002,11003,10314,10300,10301,10308,10315,10317,10300];Dn.interceptors.request.use(e=>{var t,n,r,a,o;e===void 0&&(e={});var s=(new Date().valueOf()/1e3).toFixed();e.headers.timestamp=s;var c=Gi(e.data,{timestamp:s,norce_str:"dzQhR48W"});return e.headers.sign=er(c).toString(),e.headers.Authorization=(r=(n=(t=e==null?void 0:e.headers)===null||t===void 0?void 0:t.Authorization)!==null&&n!==void 0?n:Ta())!==null&&r!==void 0?r:"",e.headers=le(le({},e.headers),{"APP-ID":((a=globalThis==null?void 0:globalThis.__navApp__)===null||a===void 0?void 0:a.appId)||3,"PROJECT-ID":((o=globalThis==null?void 0:globalThis.__navApp__)===null||o===void 0?void 0:o.projectId)||""}),e}),Dn.interceptors.response.use(e=>{if(le({},e.config.headers).ignoreError)return Promise.resolve(e.data);var t=e.data;return e.status,(t==null?void 0:t.status)!==0?t.status===10313?(lt(),Promise.reject()):(t.info&&Ar.includes(t.status),Ar.includes(t.status)?Promise.resolve({status:t.status,info:t.info}):Promise.reject()):Promise.resolve(t.data)},function(e){return F(void 0,void 0,void 0,function(){var t,n;return j(this,r=>(e instanceof Ca&&((t=e.response)===null||t===void 0?void 0:t.status)===401&&(lt(),window&&((n=window==null?void 0:window.location)===null||n===void 0||n.reload())),[2,Promise.reject()]))})});var ue=(e,t,n)=>(t===void 0&&(t={}),n===void 0&&(n={}),e=location.href.includes("https://www.amz123.com/")?"https://api.amz123.com"+e:"/api"+e,Dn.post(e,t,n)),Gi=(e,t)=>{if(e===void 0&&(e={}),typeof e!="object")throw new Error("\u{1F916} \u65E0\u6548\u7684payload\u6570\u636E");var n=le(le({},e),t),r=Object.keys(n);r.sort();var a="";return r.forEach(o=>{a+="".concat(o).concat(n[o])}),a},Pa=()=>{var e=localStorage.getItem("nav-public-closed");return typeof e=="string"?JSON.parse(e):{}},Aa=e=>Pa()[e]!==void 0,Na=e=>{var t,n=Pa(),r=new Date().valueOf();Object.entries(n).forEach(a=>{var o=a[0],s=a[1];r>s+5184e6&&(n[o]=void 0)}),localStorage.setItem("nav-public-closed",JSON.stringify(le(le({},n),((t={})[e]=r,t))))},Xi=e=>{var t,n,r,a,o,s=window.location.href,c=e==null?void 0:e.find(f=>!f.ad_data.disable_path.includes(s)&&(f.ad_data.path.length===0||f.ad_data.path.includes(s)));if(c&&!Aa(c.ad_data.pic_url)){var l=document.createElement("div"),i=()=>{l.remove()};l.classList.add("_beautiful_msg_"),l.innerHTML=`
        <div class="_msg_box_">
        <img class="_msg_" alt="" src="`.concat(c.ad_data.pic_url,`"/>
        <img id="_close_msg_" alt="" src="https://img.amz123.com/upload/index/index_close.svg" />
        </div>
      `),document.body.append(l);var d=(t=c.ad_data)===null||t===void 0?void 0:t.url,p=c.ad_data.title,u=String(c.ad_partner_id);return(n=document.querySelector("._beautiful_msg_ img._msg_"))===null||n===void 0||n.addEventListener("load",()=>{l.style.left="0px",l.style.zIndex="999999"},{once:!0}),(r=document.querySelector("._beautiful_msg_ img._msg_"))===null||r===void 0||r.addEventListener("error",()=>{i()},{once:!0}),(a=document.querySelector("._beautiful_msg_ img._msg_"))===null||a===void 0||a.addEventListener("click",()=>{i(),window.open(c.ad_data.url),globalThis.__batchReport__([{resource_position:"0-5",resource_id:p,partner_id:u,resource_url:d,path:location.pathname,event_type:"click"}],!0)}),(o=document.querySelector("#_close_msg_"))===null||o===void 0||o.addEventListener("click",()=>{i(),Na(c.ad_data.pic_url)}),{resource_position:"0-5",resource_id:p,partner_id:u,resource_url:d,path:location.pathname,event_type:"show"}}},Zi=e=>{var t,n,r,a,o=window.location.href,s=e==null?void 0:e.filter(u=>!Aa(u.ad_data.introduction)),c=null;if(s&&s.length>0&&(c=s==null?void 0:s.find(u=>{var f;return!(!((f=u==null?void 0:u.ad_data)===null||f===void 0)&&f.disable_path.includes(o))&&(u.ad_data.path.length===0||u.ad_data.path.includes(o))})),c){var l=document.createElement("div");Object.assign(l.style,{position:"fixed",right:"10px",top:"80px",width:"360px",background:"white",borderRadius:"6px",padding:"12px",cursor:"pointer",zIndex:"100"});var i=(t=c.ad_data)===null||t===void 0?void 0:t.url,d=String(c.ad_partner_id),p=c.ad_data.title||"";return l.innerHTML=`
        <div style="display: flex;gap: 10px">
        <svg width="24" height="24" viewBox="0 0 1024 1024"><path fill="#12d312" d="M512 64a448 448 0 1 1 0 896a448 448 0 0 1 0-896zm-55.808 536.384l-99.52-99.584a38.4 38.4 0 1 0-54.336 54.336l126.72 126.72a38.272 38.272 0 0 0 54.336 0l262.4-262.464a38.4 38.4 0 1 0-54.272-54.336L456.192 600.384z"/></svg>
        <span style="fontWeight: bold; fontSize: 16px;flex-grow: 1;padding:0 6px">`.concat(p,`</span>
        <svg class="_amz_notify_ad_close_" width="24" height="24" viewBox="0 0 24 24"><path fill="#959796" d="m12 13.4l-4.9 4.9q-.275.275-.7.275q-.425 0-.7-.275q-.275-.275-.275-.7q0-.425.275-.7l4.9-4.9l-4.9-4.9q-.275-.275-.275-.7q0-.425.275-.7q.275-.275.7-.275q.425 0 .7.275l4.9 4.9l4.9-4.9q.275-.275.7-.275q.425 0 .7.275q.275.275.275.7q0 .425-.275.7L13.4 12l4.9 4.9q.275.275.275.7q0 .425-.275.7q-.275.275-.7.275q-.425 0-.7-.275Z"/></svg>
        </div>
        <a target="_blank" href="`).concat(i,'" rel="nofollow" class="amz-notify-intro" style="color: #2986f7;font-size: 14px;padding: 20px 40px;display: block;word-break: break-all;">').concat((n=c.ad_data)===null||n===void 0?void 0:n.introduction,`</a>
      `),document.body.appendChild(l),(r=l.querySelector("._amz_notify_ad_close_"))===null||r===void 0||r.addEventListener("click",()=>{l.remove(),Na(c.ad_data.introduction),globalThis.__batchReport__([{resource_position:"1-4",resource_id:p,partner_id:d,resource_url:i,path:location.pathname,event_type:"click"}],!0)},{once:!0}),(a=l.querySelector(".amz-notify-intro"))===null||a===void 0||a.addEventListener("click",()=>{globalThis.__batchReport__([{resource_position:"1-4",resource_id:p,partner_id:d,resource_url:i,path:location.pathname,event_type:"click"}],!0)}),{resource_position:"1-4",resource_id:p,partner_id:d,resource_url:i,path:location.pathname,event_type:"show"}}},Qi=e=>{var t,n,r,a,o=window.location.href,s=null;if(e.length>0){var c=e==null?void 0:e.find(h=>{var v;return!(!((v=h==null?void 0:h.ad_data)===null||v===void 0)&&v.disable_path.includes(o))&&(h.ad_data.path.length===0||h.ad_data.path.includes(o))});c&&(s=c)}var l=null,i=Array.from(document.querySelectorAll(".amz-tab"));if(i&&(i==null?void 0:i.length)>0&&(l=i[0]),s&&l){(t=l.querySelector(".amz-extra-link"))===null||t===void 0||t.remove();var d=document.createElement("a");Object.assign(d.style,{color:"#EE4036",marginLeft:"auto",paddingRight:"15px",fontWeight:"bold",position:"relative",top:"2px",fontSize:"14px"});var p=(n=s.ad_data)===null||n===void 0?void 0:n.url,u=String(s.ad_partner_id),f=s.ad_data.title||"";return d.href=(a=(r=s.ad_data)===null||r===void 0?void 0:r.url)!==null&&a!==void 0?a:"#",d.target="_blank",d.dataset.opacity="2",d.rel="nofollow",d.textContent=f,l.appendChild(d),d.addEventListener("click",()=>{globalThis.__batchReport__([{resource_position:"1-2",resource_id:f,partner_id:u,resource_url:p,path:location.pathname,event_type:"click"}],!0)}),{resource_position:"1-2",resource_id:f,partner_id:u,resource_url:p,path:location.pathname,event_type:"show"}}},Ki=typeof global=="object"&&global&&global.Object===Object&&global,es=typeof self=="object"&&self&&self.Object===Object&&self,Ma=Ki||es||Function("return this")(),Jt=Ma.Symbol,Da=Object.prototype,ts=Da.hasOwnProperty,ns=Da.toString,_t=Jt?Jt.toStringTag:void 0,rs=Object.prototype.toString,as="[object Null]",os="[object Undefined]",Nr=Jt?Jt.toStringTag:void 0;function is(e){return e==null?e===void 0?os:as:Nr&&Nr in Object(e)?function(t){var n=ts.call(t,_t),r=t[_t];try{t[_t]=void 0;var a=!0}catch{}var o=ns.call(t);return a&&(n?t[_t]=r:delete t[_t]),o}(e):function(t){return rs.call(t)}(e)}var ss="[object Symbol]",cs=/\s/,ls=/^\s+/;function ds(e){return e&&e.slice(0,function(t){for(var n=t.length;n--&&cs.test(t.charAt(n)););return n}(e)+1).replace(ls,"")}function In(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var Mr=NaN,us=/^[-+]0x[0-9a-f]+$/i,ms=/^0b[01]+$/i,ps=/^0o[0-7]+$/i,fs=parseInt;function Dr(e){if(typeof e=="number")return e;if(function(r){return typeof r=="symbol"||(a=>a!=null&&typeof a=="object")(r)&&is(r)==ss}(e))return Mr;if(In(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=In(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=ds(e);var n=ms.test(e);return n||ps.test(e)?fs(e.slice(2),n?2:8):us.test(e)?Mr:+e}var En=()=>Ma.Date.now(),hs="Expected a function",vs=Math.max,gs=Math.min;function Ia(e,t,n){var r,a,o,s,c,l,i=0,d=!1,p=!1,u=!0;if(typeof e!="function")throw new TypeError(hs);function f(m){var g=r,w=a;return r=a=void 0,i=m,s=e.apply(w,g)}function h(m){var g=m-l;return l===void 0||g>=t||g<0||p&&m-i>=o}function v(){var m=En();if(h(m))return y(m);c=setTimeout(v,function(g){var w=t-(g-l);return p?gs(w,o-(g-i)):w}(m))}function y(m){return c=void 0,u&&r?f(m):(r=a=void 0,s)}function k(){var m=En(),g=h(m);if(r=arguments,a=this,l=m,g){if(c===void 0)return function(w){return i=w,c=setTimeout(v,t),d?f(w):s}(l);if(p)return clearTimeout(c),c=setTimeout(v,t),f(l)}return c===void 0&&(c=setTimeout(v,t)),s}return t=Dr(t)||0,In(n)&&(d=!!n.leading,o=(p="maxWait"in n)?vs(Dr(n.maxWait)||0,t):o,u="trailing"in n?!!n.trailing:u),k.cancel=function(){c!==void 0&&clearTimeout(c),i=0,r=l=a=c=void 0},k.flush=function(){return c===void 0?s:y(En())},k}var qe,Gt,Lt,Ge,Ir,ys=()=>{ws();var e=document.querySelectorAll("#amz-aside-bar .amz-aside-bar_more .amz-aside-bar_more-box .amz-aside-bar_more-tabs span"),t=document.querySelectorAll("#amz-aside-bar .amz-aside-bar_more .amz-aside-bar_more-box .amz-aside-bar_more-content"),n=document.querySelector("#amz-aside-bar .amz-aside-bar_more");e.forEach((c,l)=>{c.addEventListener("click",()=>{var i,d;e.forEach(p=>{p.setAttribute("style","border-bottom-right-radius:0;border-bottom-left-radius:0;"),p.classList.contains("active")&&p.classList.remove("active")}),c.className="active",l-1>=0&&((i=e[l-1])===null||i===void 0||i.setAttribute("style","border-bottom-right-radius:8px;")),l+1<=e.length&&((d=e[l+1])===null||d===void 0||d.setAttribute("style","border-bottom-left-radius:8px;")),t.forEach((p,u)=>{p.className=u===l?"amz-aside-bar_more-content active":"amz-aside-bar_more-content"})})});var r=document.querySelector("#amz-aside-bar .amz-aside-bar_scroll"),a=document.querySelectorAll("#amz-aside-bar .amz-aside-bar_divider"),o=document.querySelector("#amz-aside-bar");if(r){r.addEventListener("click",()=>{window.scrollTo({top:0,behavior:"smooth"})});var s=Ia(()=>{var c,l,i=Array.from(o==null?void 0:o.children).filter(p=>p.tagName==="SECTION"),d=i==null?void 0:i[i.length-2];document.documentElement.scrollTop>=150?(r.classList.add("amz-show-scroll"),(c=a[2])===null||c===void 0||c.classList.remove("amz-hidden"),n==null||n.classList.remove("more_rounded"),d&&(d.style["border-bottom-left-radius"]="0",d.classList.add("have-after-line"))):(r.classList.remove("amz-show-scroll"),(l=a[2])===null||l===void 0||l.classList.add("amz-hidden"),n==null||n.classList.add("more_rounded"),d&&(d.style["border-bottom-left-radius"]="6px",d.classList.remove("have-after-line")))},100);window.addEventListener("scroll",s)}setTimeout(()=>{for(var c=document.querySelectorAll(".amz-aside-bar_msg"),l=p=>{var u=c[p].querySelector(".amz-aside-bar_msg_box");if(!u)return{value:void 0};var f=u.querySelector(".amz-aside-bar_msg_box-content-close");u&&(c[p].addEventListener("mouseover",()=>{u.style.display="flex"}),c[p].addEventListener("mouseout",()=>{u.style.display="none"}),c[p].addEventListener("click",h=>{f&&f.contains(h.target)&&(u.style.display="none")}))},i=0;i<c.length;i++){var d=l(i);if(typeof d=="object")return d.value}},100)},ws=()=>{for(var e=document.querySelectorAll("#amz-aside-bar section"),t=0;t<e.length;t++){var n=e[t].getAttribute("data-amz-show-path");if(n){var r=n.split(",");r.length>0&&!r.includes(location.href)&&(e[t].style.display="none")}}},bs=()=>{try{var e=document.body,t=document.querySelector(".header-search-icon_empty"),n=document.querySelector(".header-search-icon_empty .icon-mark"),r=document.getElementById("header_search_input"),a=document.querySelector(".header-search-icon"),o=document.querySelector(".amz-header-vip"),s=document.querySelector(".no-show-vip");t.addEventListener("click",()=>{if(a.className==="header-search-icon active"){var c=r.value.trim();c!==""?window.open("/search?keyword=".concat(c),"_blank"):window.open("/search?keyword=".concat(r.placeholder),"_blank")}else{a.className="header-search-icon active",t.className="header-search-icon_empty active",o==null||o.setAttribute("class","amz-header-vip active");var l=n.getBoundingClientRect(),i=l.x,d=l.width,p=e.getBoundingClientRect().width,u=p-i-d-18;s&&(u+=p>1340?100:50),a.setAttribute("style","right:".concat(u,"px")),r.focus()}}),r.addEventListener("keyup",c=>{if(c.key==="Enter"){var l=r.value.trim();l.length!==0?window.open("/search?keyword=".concat(l),"_blank"):window.open("/search?keyword=".concat(r.placeholder),"_blank")}}),document.addEventListener("click",c=>(l=>{l.stopPropagation();var i=document.querySelector(".header-search-icon_empty"),d=document.querySelector(".header-search-icon");if(d.className==="header-search-icon active"&&!d.contains(l.target)&&!i.contains(l.target)){d.className="header-search-icon",i.className="header-search-icon_empty re_back";var p=document.querySelector(".amz-header-vip");p==null||p.setAttribute("class","amz-header-vip re_back")}})(c)),window.addEventListener("resize",()=>{if(a.className==="header-search-icon active"){a.className="header-search-icon";var c=document.querySelector(".amz-header-vip");c==null||c.setAttribute("class","amz-header-vip re_back")}})}catch{}},tr={2:"https://img.brandark.com/static/logo.jpg",3:"https://img.amz123.com/static/images/header-logo.svg",5:"https://cdn.zfty.work/space_5/static/images/logo/login_logo.svg",8:"https://img.dny123.net/static/images/dny123.png"},nr={2:"\u54C1\u724C\u65B9\u821FBrandArk-\u8BA9\u4E16\u754C\u770B\u89C1\u4E2D\u56FD\u54C1\u724C\u529B\u91CF",3:"\u4E0E\u767E\u4E07\u8DE8\u5883\u4EBA\u4E00\u8D77\u6210\u957F",5:"\u4E0E\u767E\u4E07\u8DE8\u5883\u4EBA\u4E00\u8D77\u6210\u957F",8:"\u51FA\u6D77\u4E1C\u5357\u4E9A\uFF0C\u5C31\u4E0ADNY123"},Rn=(e,t,n)=>{var r,a;e===void 0&&(e=!1),t===void 0&&(t=!1),n===void 0&&(n=!1);var o=((r=globalThis==null?void 0:globalThis.__navApp__)===null||r===void 0?void 0:r.appId)||3,s=`<div class="amz-header-login-modal">
  <div class="animate-open "
    style="width: 408px; padding: 56px 42px 36px 42px; position: relative; display: flex; flex-direction: column; background: rgb(255, 255, 255); border-radius: 12px;box-sizing: border-box;">
    <div class="amz-login-modal-body">
      <div class="amz-login-form">
        <div class="amz-login-form-options"><span class="amz-login-form-close antialiased"><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M9.3335 9.33333L22.6668 22.6667" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M9.3335 22.6667L22.6668 9.33333" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        </span></div>
        <div class="amz-login-logo">
         <span class="amz-success-tip" style="opacity: 0;"></span>
         <img src="`.concat(tr[o],`"alt="logo">
         <span>`).concat(nr[o],`</span>
        </div>
        <div style="" class="amz-login-container">
        <span
            class="amz-back-wx-login" id="back-wx-login" style="display: `).concat(e?"block":"none",`"><svg width="32" height="32" viewBox="0 0 32 32" fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <path d="M20 23L13 16L20 9" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                stroke-linejoin="round" />
            </svg>
          </span>
          <span style="display: none;" class="amz-login-form-back antialiased"><svg width="32" height="32" viewBox="0 0 32 32" fill="none"
          xmlns="http://www.w3.org/2000/svg">
          <path d="M20 23L13 16L20 9" stroke="currentColor" stroke-width="2" stroke-linecap="round"
            stroke-linejoin="round" />
        </svg></span>
 
          <div class="amz-login-tabs-wrapper">
            <div class="amz-login-tabs">
              <div class="amz-login-tab amz-login-tab-code amz-login-tab-active">\u624B\u673A\u53F7\u767B\u5F55</div>
              <div class="amz-login-tab amz-login-tab-pwd">\u8D26\u53F7\u5BC6\u7801\u767B\u5F55</div>
            </div>
          </div>
          <div class="amz-mobile-login-danger-tip">\u5982\u6709\u4EFB\u4F55\u767B\u5F55\u95EE\u9898 \u8BF7\u6DFB\u52A0\u5B98\u65B9\u5BA2\u670D\u5FAE\u4FE1orlcom</div>
          <div style="" class="amz-login-input-container">
            <div class="amz-login-input">
              <div class="amz-login-id"><input class="amz-login-username-input" placeholder="\u624B\u673A\u53F7"><span
                  class="amz-login-input-tip amz-login-id-tip" style="opacity: 0;"></span></div>
              <div class="amz-login-password" style="display:none">
                <input class="amz-login-password-input" type="password" placeholder="\u5BC6\u7801">
                <div class="amz-reset-password">\u5FD8\u8BB0\u5BC6\u7801?</div>
                <span class="amz-login-input-tip amz-login-password-tip" style="opacity: 0;"></span>
              </div>
              <div class="amz-login-code">
                <input class="amz-login-code-input" maxlength="6" type="text" placeholder="\u9A8C\u8BC1\u7801">
                <div class="amz-login-get-code">
                  <div class="amz-login-get-code-inner">\u83B7\u53D6\u9A8C\u8BC1\u7801</div>
                </div>
                <span class="amz-login-input-tip amz-login-code-tip" style="opacity: 0;"></span>
              </div>
            </div>
               <div class="amz-login-btn"><button id="amz-login-on-login">`).concat(n?"\u7ED1\u5B9A\u6B64\u8D26\u53F7":"\u767B\u5F55",`</button></div>
            <div class="amz-login-tip">
              \u672A\u6CE8\u518C\u8FC7\u7684\u624B\u673A\u53F7\u5C06\u81EA\u52A8\u521B\u5EFA\u8D26\u53F7\uFF0C\u767B\u5F55\u5373\u8868\u793A\u9605\u8BFB\u5E76\u540C\u610F<span class="amz-login-tip-privacy">\u300A\u7528\u6237\u534F\u8BAE\u53CA\u9690\u79C1\u653F\u7B56\u300B</span>
            </div>

            <div class="amz-login-danger-tip">\u5982\u6709\u4EFB\u4F55\u767B\u5F55\u95EE\u9898\xA0 \u8BF7\u70B9\u51FB\u9996\u9875\u53F3\u4FA7\u4E8C\u7EF4\u7801\u8054\u7CFB\u5B98\u65B9\u5BA2\u670D</div>
     
            <div class="amz-login-social `).concat(t||n?"amz-login-social-hidden":"",`">
              <div><i></i><span class="antialiased">\u66F4\u591A\u767B\u5F55\u65B9\u5F0F</span><i></i></div>
              <div>
                <button id="amz-login-weChat-button">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M20.314 18.5897C21.647 17.6217 22.5 16.1927 22.5 14.6037C22.5 11.6937 19.667 9.33369 16.175 9.33369C12.681 9.33369 9.85 11.6937 9.85 14.6037C9.85 17.5147 12.681 19.8747 16.175 19.8747C16.873 19.8757 17.568 19.7787 18.239 19.5867L18.425 19.5577C18.547 19.5577 18.657 19.5957 18.761 19.6547L20.147 20.4547L20.267 20.4947C20.2948 20.495 20.3224 20.4897 20.3482 20.4792C20.374 20.4687 20.3974 20.4532 20.4171 20.4335C20.4369 20.4139 20.4525 20.3906 20.4631 20.3648C20.4737 20.3391 20.4791 20.3115 20.479 20.2837L20.445 20.1297L20.16 19.0667L20.137 18.9317C20.1369 18.8645 20.153 18.7983 20.1838 18.7387C20.2146 18.679 20.2592 18.6286 20.314 18.5897ZM9.09 3.5127C4.9 3.5137 1.5 6.3457 1.5 9.8397C1.5 11.7447 2.522 13.4617 4.122 14.6207C4.18784 14.6674 4.24154 14.7291 4.2786 14.8008C4.31566 14.8725 4.335 14.952 4.335 15.0327L4.309 15.1927L3.966 16.4687L3.926 16.6537C3.926 16.7937 4.039 16.9077 4.178 16.9077L4.324 16.8607L5.987 15.9007C6.10849 15.8266 6.24769 15.7865 6.39 15.7847L6.612 15.8167C7.418 16.0477 8.252 16.1647 9.09 16.1647L9.507 16.1547C9.33899 15.6552 9.25288 15.1317 9.252 14.6047C9.252 11.4187 12.352 8.8347 16.175 8.8347L16.586 8.8457C16.016 5.8257 12.876 3.5137 9.092 3.5137L9.09 3.5127ZM14.066 13.7607C13.9529 13.7646 13.8402 13.7457 13.7346 13.7051C13.629 13.6645 13.5326 13.6031 13.4512 13.5245C13.3699 13.4458 13.3051 13.3517 13.2609 13.2475C13.2167 13.1433 13.194 13.0313 13.194 12.9182C13.194 12.805 13.2167 12.6931 13.2609 12.5889C13.3051 12.4847 13.3699 12.3905 13.4512 12.3119C13.5326 12.2333 13.629 12.1719 13.7346 12.1313C13.8402 12.0907 13.9529 12.0718 14.066 12.0757C14.2822 12.0862 14.4861 12.1795 14.6354 12.3363C14.7847 12.493 14.868 12.7012 14.868 12.9177C14.868 13.1342 14.7847 13.3423 14.6354 13.4991C14.4861 13.6558 14.2822 13.7502 14.066 13.7607ZM18.283 13.7607C18.1699 13.7646 18.0572 13.7457 17.9516 13.7051C17.846 13.6645 17.7496 13.6031 17.6682 13.5245C17.5869 13.4458 17.5221 13.3517 17.4779 13.2475C17.4337 13.1433 17.411 13.0313 17.411 12.9182C17.411 12.805 17.4337 12.6931 17.4779 12.5889C17.5221 12.4847 17.5869 12.3905 17.6682 12.3119C17.7496 12.2333 17.846 12.1719 17.9516 12.1313C18.0572 12.0907 18.1699 12.0718 18.283 12.0757C18.4992 12.0862 18.7031 12.1795 18.8524 12.3363C19.0017 12.493 19.085 12.7012 19.085 12.9177C19.085 13.1342 19.0017 13.3423 18.8524 13.4991C18.7031 13.6558 18.4992 13.7502 18.283 13.7607ZM6.561 8.82669C6.42549 8.83096 6.29051 8.80795 6.16407 8.75903C6.03762 8.71012 5.9223 8.63629 5.82494 8.54195C5.72757 8.4476 5.65016 8.33465 5.5973 8.20981C5.54443 8.08496 5.51719 7.95077 5.51719 7.8152C5.51719 7.67962 5.54443 7.54543 5.5973 7.42058C5.65016 7.29574 5.72757 7.18279 5.82494 7.08844C5.9223 6.9941 6.03762 6.92027 6.16407 6.87136C6.29051 6.82244 6.42549 6.79943 6.561 6.8037C6.82376 6.81196 7.073 6.92215 7.25594 7.11094C7.43889 7.29973 7.54119 7.5523 7.54119 7.8152C7.54119 8.07809 7.43889 8.33066 7.25594 8.51945C7.073 8.70824 6.82376 8.81843 6.561 8.82669ZM11.622 8.82669C11.4865 8.83096 11.3515 8.80795 11.2251 8.75903C11.0986 8.71012 10.9833 8.63629 10.8859 8.54195C10.7886 8.4476 10.7112 8.33465 10.6583 8.20981C10.6054 8.08496 10.5782 7.95077 10.5782 7.8152C10.5782 7.67962 10.6054 7.54543 10.6583 7.42058C10.7112 7.29574 10.7886 7.18279 10.8859 7.08844C10.9833 6.9941 11.0986 6.92027 11.2251 6.87136C11.3515 6.82244 11.4865 6.79943 11.622 6.8037C11.8848 6.81196 12.134 6.92215 12.3169 7.11094C12.4999 7.29973 12.6022 7.5523 12.6022 7.8152C12.6022 8.07809 12.4999 8.33066 12.3169 8.51945C12.134 8.70824 11.8848 8.81843 11.622 8.82669Z" fill="currentColor"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>
          <div class="amz-login-qr-code amz-login-qrcode-container" style="display: none;">
            <div class="amz-login-qr-code-img"><img class="amz-login-qr-code-img-inner"
                src="https://img.amz123.com/static/images/jinrikuajing.jpg" alt=""></div>
            <div class="amz-login-qr-code-tip">\u4F7F\u7528\u5FAE\u4FE1\u626B\u4E00\u626B</div>
          </div>
        </div>
        <div style="display: none;" class="amz-register-container"><span
            class="amz-register-form-back antialiased"><svg width="32" height="32" viewBox="0 0 32 32" fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <path d="M20 23L13 16L20 9" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                stroke-linejoin="round" />
            </svg>
          </span>
          <div class="amz-mobile-register-tabs">
            <div class="amz-mobile-register-tabs-bar"></div>
            <div class="amz-mobile-register-tab amz-mobile-register-tab-active">\u8D26\u53F7\u6CE8\u518C</div>
          </div>
          <div class="amz-register-first-step" style="">
            <div class="amz-register-input">
              <div class="amz-register-id"><input class="amz-register-phone-input" placeholder="\u624B\u673A\u53F7"><span
                  class="amz-register-input-tip amz-register-id-tip" style="opacity: 0;"></span>
              </div>
              
              <div class="amz-register-password"><input class="amz-register-password-input" type="password"
              placeholder="\u5BC6\u7801"><span
              class="amz-register-input-tip amz-register-password-tip amz-register-input-next-step"
              style="opacity: 0;"></span></div>
          <div class="amz-register-password-twice"><input class="amz-register-password-twice-input"
              type="password" placeholder="\u786E\u8BA4\u5BC6\u7801"><span
              class="amz-register-input-tip amz-register-password-twice-tip" style="opacity: 0;"></span></div>

              <div class="amz-register-password amz-register-code"><input class="amz-register-code-input" maxlength="6" type="text" placeholder="\u9A8C\u8BC1\u7801">
                <div class="amz-register-get-code">
                  <div class="amz-register-get-code-inner">\u83B7\u53D6\u9A8C\u8BC1\u7801</div>
                  <div class="amz-register-count-down" style="display: none;"> \u53D1\u9001\u6210\u529F <span id="countdown"
                      class="inline-block min-w-[16px]">60</span>S </div>
                </div><span class="amz-register-input-tip amz-register-code-tip" style="opacity: 0;"></span>
              </div>
            </div>
            
            <div class="amz-register-btn"><button id="amz-register-on-register">\u6CE8\u518C</button></div>
           <!-- <div class="amz-register-btn"><button id="amz-register-on-next-step">\u4E0B\u4E00\u6B65</button></div> -->
          </div><!-- \u6CE8\u518C \u7B2C\u4E8C\u6B65 -->
          <div class="amz-register-to-login-container"><span>\u5DF2\u6709\u8D26\u53F7?</span><button
              class="amz-register-to-login">\u7ACB\u5373\u767B\u5F55</button>
          </div>
          <div class="amz-mobile-register-to-login">
            <span>\u5DF2\u6709\u8D26\u53F7?</span>
            <div class="amz-mobile-register-to-login-inner">\u7ACB\u5373\u767B\u5F55</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>`);(a=document.body)===null||a===void 0||a.insertAdjacentHTML("afterend",s)},Ra=()=>{var e,t,n=((e=globalThis==null?void 0:globalThis.__navApp__)===null||e===void 0?void 0:e.appId)||3,r=`<div class="amz-header-login-modal">
  <div
    class="animate-open"
    style="
      width: 408px;
      padding: 56px 42px;
      position: relative;
      display: flex;
      flex-direction: column;
      background: rgb(255, 255, 255);
      border-radius: 12px;
      box-sizing: border-box;
    "
  >
    <div class="amz-login-modal-body">
      <div class="amz-login-form">
        <div class="amz-login-form-options">
          <span class="amz-login-form-close antialiased">
            <svg
              width="32"
              height="32"
              viewBox="0 0 32 32"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9.3335 9.33333L22.6668 22.6667"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M9.3335 22.6667L22.6668 9.33333"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </span>
        </div>

        <div class="amz-login-logo">
          <span class="amz-success-tip" style="opacity: 0"></span>
          <img src="`.concat(tr[n],`"alt="logo">
          <span>`).concat(nr[n],`</span>
        </div>

        <div class="wx-login-check" id="amz-wx-login">
          <div id="amz-login-weChat-button"><svg width="30" height="30" viewBox="0 0 30 30" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_752_6874)">
              <path
                d="M15 0.0546875C6.72223 0.0546875 0 6.72137 0 14.9991C0 23.2769 6.72223 29.9436 15 29.9436C23.2778 29.9436 29.9445 23.2769 29.9445 14.9991C29.9445 6.72137 23.2778 0.0546875 15 0.0546875ZM12.3333 18.4991C11.5556 18.4991 10.8889 18.3325 10.1111 18.1658L7.88889 19.2769L8.55557 17.3325C6.94444 16.2214 6 14.7769 6 12.9991C6 9.94358 8.88888 7.55469 12.3889 7.55469C15.5556 7.55469 18.2778 9.44357 18.8333 12.0547C18.6111 12.0547 18.4444 11.9991 18.2222 11.9991C15.1667 11.9991 12.7778 14.2769 12.7778 17.0547C12.7778 17.4991 12.8333 17.9436 13 18.388C12.7778 18.4992 12.5556 18.4991 12.3333 18.4991ZM21.7778 20.7769L22.2778 22.388L20.5556 21.388C19.8889 21.5547 19.2778 21.7214 18.6111 21.7214C15.5556 21.7214 13.1667 19.6658 13.1667 17.1102C13.1667 14.5547 15.5556 12.4436 18.6111 12.4436C21.5 12.4436 24.0556 14.4991 24.0556 17.1103C24.0556 18.4992 23.1111 19.7769 21.7778 20.7769Z"
                fill="#46BB36"></path>
              <path
                d="M9.33398 11.1116C9.33398 11.6115 9.72287 12.0004 10.2229 12.0004C10.7228 12.0004 11.1117 11.6115 11.1117 11.1116C11.1117 10.6116 10.7229 10.2227 10.2229 10.2227C9.7229 10.2227 9.33398 10.6115 9.33398 11.1115V11.1116ZM16.2229 15.7782C16.2229 16.1671 16.5562 16.5004 16.9451 16.5004C17.334 16.5004 17.6673 16.1671 17.6673 15.7782C17.6673 15.3893 17.334 15.056 16.9451 15.056C16.5562 15.0004 16.2229 15.3338 16.2229 15.7782ZM13.834 11.1115C13.834 11.6115 14.2229 12.0004 14.7229 12.0004C15.2229 12.0004 15.6117 11.6115 15.6117 11.1116C15.6117 10.6115 15.2229 10.2227 14.7229 10.2227C14.2229 10.2227 13.834 10.6115 13.834 11.1115ZM19.7784 15.7783C19.7784 16.1671 20.1117 16.5004 20.5006 16.5004C20.8895 16.5004 21.2228 16.1671 21.2228 15.7782C21.2228 15.3893 20.8895 15.056 20.5006 15.056C20.1117 15.0004 19.7784 15.3338 19.7784 15.7783Z"
                fill="#46BB36"></path>
            </g>
            <defs>
              <clipPath id="clip0_752_6874">
                <rect width="30" height="30" fill="white"></rect>
              </clipPath>
            </defs>
          </svg></div>
          <div class="wx-login-check-text">\u5FAE\u4FE1\u767B\u5F55</div>
        </div>
        <div class="other-login-check" id="amz-other-login-check">
          <div>\u5176\u4ED6\u767B\u5F55/\u6CE8\u518C\u65B9\u5F0F</div>
        </div>
      </div>
    </div>
  </div>
</div>`);(t=document.body)===null||t===void 0||t.insertAdjacentHTML("afterend",r)};(e=>{e[e.\u6CE8\u518C\u53D1\u9001\u624B\u673A\u9A8C\u8BC1\u7801=1]="\u6CE8\u518C\u53D1\u9001\u624B\u673A\u9A8C\u8BC1\u7801",e[e.\u6362\u7ED1\u53D1\u9001\u624B\u673A\u9A8C\u8BC1\u7801=2]="\u6362\u7ED1\u53D1\u9001\u624B\u673A\u9A8C\u8BC1\u7801",e[e.\u7ED1\u5B9A\u90AE\u7BB1\u53D1\u9001\u90AE\u7BB1\u9A8C\u8BC1\u7801=3]="\u7ED1\u5B9A\u90AE\u7BB1\u53D1\u9001\u90AE\u7BB1\u9A8C\u8BC1\u7801",e[e.\u5FD8\u8BB0\u5BC6\u7801\u53D1\u9001\u9A8C\u8BC1\u7801=4]="\u5FD8\u8BB0\u5BC6\u7801\u53D1\u9001\u9A8C\u8BC1\u7801",e[e.\u624B\u673A\u53F7\u767B\u9646\u53D1\u9001\u9A8C\u8BC1\u7801=5]="\u624B\u673A\u53F7\u767B\u9646\u53D1\u9001\u9A8C\u8BC1\u7801",e[e.\u6362\u7ED1\u6821\u9A8C\u539F\u624B\u673A=6]="\u6362\u7ED1\u6821\u9A8C\u539F\u624B\u673A",e[e.\u627E\u56DE\u5BC6\u7801\u9884\u6821\u9A8C=7]="\u627E\u56DE\u5BC6\u7801\u9884\u6821\u9A8C",e[e.\u5FAE\u4FE1\u7ED1\u5B9A\u8D26\u53F7=8]="\u5FAE\u4FE1\u7ED1\u5B9A\u8D26\u53F7",e[e.\u9A8C\u8BC1\u8868\u5355\u624B\u673A\u53F7=9]="\u9A8C\u8BC1\u8868\u5355\u624B\u673A\u53F7",e[e.\u624B\u673A\u53F7\u9A8C\u8BC1\u5E76\u6CE8\u518C\u767B\u5F55=10]="\u624B\u673A\u53F7\u9A8C\u8BC1\u5E76\u6CE8\u518C\u767B\u5F55",e[e.\u4FEE\u6539\u5BC6\u7801\u53D1\u9001\u9A8C\u8BC1\u7801=11]="\u4FEE\u6539\u5BC6\u7801\u53D1\u9001\u9A8C\u8BC1\u7801"})(qe||(qe={})),(e=>{e[e.\u767B\u9646=1]="\u767B\u9646",e[e.\u7ED1\u5B9A=2]="\u7ED1\u5B9A"})(Gt||(Gt={})),(e=>{e[e.\u7EE7\u7EED\u7B49\u5F85=0]="\u7EE7\u7EED\u7B49\u5F85",e[e.\u767B\u5F55\u6210\u529F=1]="\u767B\u5F55\u6210\u529F",e[e.\u8DF3\u8F6C\u5230\u6CE8\u518C\u754C\u9762=2]="\u8DF3\u8F6C\u5230\u6CE8\u518C\u754C\u9762",e[e.\u7ED1\u5B9A\u6210\u529F=3]="\u7ED1\u5B9A\u6210\u529F"})(Lt||(Lt={})),(e=>{e.\u5BC6\u7801\u767B\u5F55="pwd",e.\u9A8C\u8BC1\u7801\u767B\u5F55="code"})(Ge||(Ge={})),(e=>{e[e.\u627E\u56DE\u5BC6\u7801=1]="\u627E\u56DE\u5BC6\u7801",e[e.\u6CE8\u518C\u8D26\u53F7=2]="\u6CE8\u518C\u8D26\u53F7"})(Ir||(Ir={}));var ye=()=>{var e=document.querySelector(".amz-login-form-back"),t=document.querySelector(".amz-register-form-back"),n=document.querySelector(".amz-login-form-close"),r=document.querySelector(".amz-login-to-register"),a=document.querySelector("#amz-login-weChat-button"),o=document.querySelector("#amz-wx-login"),s=document.querySelector("#back-wx-login"),c=document.querySelector("#amz-other-login-check"),l=document.querySelector(".amz-register-to-login"),i=document.querySelector("#amz-register-on-next-step"),d=document.querySelector(".amz-reset-password"),p=document.querySelector(".amz-header-login-modal"),u=document.querySelector(".amz-login-container"),f=document.querySelector(".amz-login-input-container"),h=document.querySelector(".amz-login-password"),v=document.querySelector(".amz-login-code"),y=document.querySelector(".amz-login-qrcode-container"),k=document.querySelector(".amz-register-container"),m=document.querySelector(".amz-register-first-step"),g=document.querySelector(".amz-register-to-login-container"),w=document.querySelector(".amz-login-tip-privacy"),E=document.querySelector(".amz-login-tabs-wrapper"),z=document.querySelector(".amz-login-tab-pwd"),T=document.querySelector(".amz-login-tab-code"),O=document.querySelector(".amz-login-username-input"),q=document.querySelector(".amz-login-password-input"),D=document.querySelector(".amz-login-code-input"),A=document.querySelector(".amz-register-phone-input"),P=document.querySelector(".amz-register-code-input"),B=document.querySelector(".amz-register-password"),R=document.querySelector(".amz-register-password-input"),I=document.querySelector(".amz-register-password-twice"),Y=document.querySelector(".amz-register-password-twice-input"),U=document.querySelector(".amz-login-id-tip"),G=document.querySelector(".amz-login-password-tip"),oe=document.querySelector(".amz-login-code-tip"),K=document.querySelector(".amz-register-id-tip"),b=document.querySelector(".amz-register-password-tip"),S=document.querySelector(".amz-register-code-tip"),_=document.querySelector(".amz-register-password-twice-tip");return{commonSuccessTip:document.querySelector(".amz-success-tip"),btnLoginBack:e,btnRegisterBack:t,btnClose:n,btnToRegister:r,btnToWeChatLogin:a,btnWxLogin:o,btnToLogin:l,btnToRegisterNextStep:i,modalContainer:p,loginContainer:u,loginInputContainer:f,pwdContainer:h,codeContainer:v,qrCodeContainer:y,registerContainer:k,registerFirstStepContainer:m,loginUsernameInput:O,loginPasswordInput:q,loginCodeInput:D,registerPhoneInput:A,registerCodeInput:P,registerPassword:B,registerPasswordInput:R,registerPasswordTwice:I,registerPasswordTwiceInput:Y,btnLogin:document.querySelector("#amz-login-on-login"),btnLoginGetCode:document.querySelector(".amz-login-get-code-inner"),btnRegisterGetCode:document.querySelector(".amz-register-get-code-inner"),btnRegister:document.querySelector("#amz-register-on-register"),qrCodeImg:document.querySelector(".amz-login-qr-code-img-inner"),btnResetPassword:d,registerToLoginContainer:g,loginUsernameTip:U,loginPasswordTip:G,loginCodeTip:oe,registerPhoneTip:K,registerCodeTip:S,registerPasswordTwiceTip:_,registerPasswordTip:b,toPwdLoginTab:z,toCodeLoginTab:T,btnOtherLogin:c,btnBackCheck:s,loginTabsWrapper:E,btnPrivacy:w}},$n=()=>({btnClose:document.querySelector(".amz-login-form-close"),modalContainer:document.querySelector(".amz-header-login-modal"),wxContainer:document.querySelector(".amz-wx-bind-container"),wxPhoneInput:document.querySelector(".amz-wx-bind-phone-input"),wxCodeInput:document.querySelector(".amz-wx-bind-code-input"),wxPhoneTip:document.querySelector(".amz-wx-bind-id-tip"),wxCodeTip:document.querySelector(".amz-wx-bind-code-tip"),btnBind:document.querySelector("#amz-wx-bind-on-submit"),btnWxGetCode:document.querySelector(".amz-wx-bind-get-code-inner"),btnBackToLogin:document.querySelector("#amz-wx-on-back-login")}),Ae="/user/v1",_s=function(e){return F(void 0,void 0,void 0,function(){return j(this,t=>{switch(t.label){case 0:return[4,ue("".concat(Ae,"/user/login"),e)];case 1:return[2,t.sent()]}})})},xs=function(e){return F(void 0,void 0,void 0,function(){return j(this,t=>{switch(t.label){case 0:return[4,ue("".concat(Ae,"/user/new_register"),e)];case 1:return[2,t.sent()]}})})},Ss=function(e){return F(void 0,void 0,void 0,function(){return j(this,t=>{switch(t.label){case 0:return[4,ue("".concat(Ae,"/user/captcha"),e)];case 1:return[2,t.sent()]}})})},zs=function(){return F(void 0,void 0,void 0,function(){return j(this,e=>{switch(e.label){case 0:return[4,ue("".concat(Ae,"/account/wechat/login"))];case 1:return[2,e.sent()]}})})},$a=function(e){return F(void 0,void 0,void 0,function(){return j(this,t=>{switch(t.label){case 0:return[4,ue("".concat(Ae,"/account/wechat/qrlogin_status"),e)];case 1:return[2,t.sent()]}})})},Es=function(e){return F(void 0,void 0,void 0,function(){return j(this,t=>{switch(t.label){case 0:return[4,ue("".concat(Ae,"/account/reset_password"),e)];case 1:return[2,t.sent()]}})})},ks=function(e){return F(void 0,void 0,void 0,function(){return j(this,t=>{switch(t.label){case 0:return[4,ue("".concat(Ae,"/account/wechat/login_with_code"),{code:e})];case 1:return[2,t.sent()]}})})},Cs=function(e){return F(void 0,void 0,void 0,function(){return j(this,t=>{switch(t.label){case 0:return[4,ue("".concat(Ae,"/account/wechat/bind_phone_with_code"),e)];case 1:return[2,t.sent()]}})})},Ls=function(e){return F(void 0,void 0,void 0,function(){return j(this,t=>{switch(t.label){case 0:return[4,ue("".concat(Ae,"/user/mobile_access"),{mobile:e.phone,code:e.captcha})];case 1:return[2,t.sent()]}})})},Ba=()=>{if(!document.querySelector('script[src="https://turing.captcha.qcloud.com/TCaptcha.js"]')){var e=document.createElement("script");e.src="https://turing.captcha.qcloud.com/TCaptcha.js",document.head.appendChild(e)}},Fa=e=>{new TencentCaptcha("**********",t=>{t.ret===0&&e()}).show()},dt=e=>{for(var t=0;t<e.length;t++){var n=e[t];if(!Se(n.el,n.options))return!1}return!0},Se=(e,t)=>{for(var n=t.value,r=t.tip,a=t.validRules,o=0;o<a.length;o++){var s=a[o],c=s.tip;if(!(0,s.validFn)(n))return he(e,{tip:r||c,opacity:"100%"}),!1;he(e,{tip:"",opacity:"0"})}return!0},he=(e,t)=>{var n=t.tip,r=t.opacity;e.innerText=n,e.style.opacity=r,n!=="\u6CE8\u518C\u6210\u529F"&&n!=="\u91CD\u7F6E\u5BC6\u7801\u6210\u529F"||(e.className="amz-success-tip active",setTimeout(()=>{e.className="amz-success-tip"},1e4))},Ts=/^(?:(?:\+|00)86)?1[3-9]\d{9}$/,Os=/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,16}$/,ja=[{validFn:e=>e.length>0,tip:"\u8BF7\u8F93\u5165\u624B\u673A / Email / \u7528\u6237\u540D"}],$e=[{validFn:e=>Ts.test(e),tip:"\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u624B\u673A\u53F7\u7801"}],Ua=[{validFn:e=>e.length>0,tip:"\u8BF7\u8F93\u5165\u5BC6\u7801"}],Ha=[{validFn:e=>Os.test(e),tip:"\u5BC6\u7801\u957F\u5EA6\u4E3A8-16\u4F4D\uFF0C\u683C\u5F0F\u4E3A\u5B57\u6BCD\u52A0\u6570\u5B57"}],Ze=[{validFn:e=>e.length>0,tip:"\u8BF7\u8F93\u5165\u9A8C\u8BC1\u7801"},{validFn:e=>e.length===6,tip:"\u8BF7\u8F93\u51656\u4F4D\u9A8C\u8BC1\u7801"}],Wa=[{validFn:e=>e[0]===e[1],tip:"\u5BC6\u7801\u4E0D\u4E00\u81F4"}],Z=(e,t)=>{t===void 0&&(t={display:"block",target:""});var n=t.display,r=n===void 0?"block":n,a=t.target;if(a)e.style.display=a;else{var o=e.style.display;e.style.display=o==="none"?r:"none"}},ae=(e,t,n)=>{try{he(t,{tip:n,opacity:"100%"})}catch{}},ut=[301,304,305,306,11001,11002,11003,10314,10301,10315,10300],Ee={10300:"\u62B1\u6B49\uFF0C\u7F51\u7AD9\u51FA\u4E86\u70B9\u5C0F\u95EE\u9898\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5"},Re={301:"\u8BE5\u8D26\u53F7\u4E0D\u5B58\u5728",304:"\u8BE5\u7528\u6237\u5DF2\u5B58\u5728",306:"\u624B\u673A\u53F7\u5DF2\u88AB\u5360\u7528"},qs={305:"\u5BC6\u7801\u9519\u8BEF"},Xt={10315:"\u9A8C\u8BC1\u7801\u53D1\u9001\u5931\u8D25",11001:"\u9A8C\u8BC1\u7801\u9519\u8BEF",11002:"\u9A8C\u8BC1\u9519\u8BEF",11003:"\u9A8C\u8BC1\u7801\u9519\u8BEF",10301:"\u9A8C\u8BC1\u7801\u9519\u8BEF",10314:"\u9A8C\u8BC1\u7801\u9519\u8BEF"},Bn=function(e,t){return F(void 0,void 0,void 0,function(){var n,r,a,o,s,c,l,i,d,p,u,f,h,v,y;return j(this,function(k){return n=ye(),r=n.loginUsernameInput,a=n.loginUsernameTip,o=n.registerPhoneInput,s=n.registerPhoneTip,c=n.loginCodeTip,l=n.registerCodeTip,i=$n(),d=i.wxPhoneInput,p=i.wxPhoneTip,u=i.wxCodeTip,f=null,h=null,v=null,[qe.\u624B\u673A\u53F7\u767B\u9646\u53D1\u9001\u9A8C\u8BC1\u7801,qe.\u624B\u673A\u53F7\u9A8C\u8BC1\u5E76\u6CE8\u518C\u767B\u5F55].includes(e)?(f=r,h=a,v=c):e===qe.\u5FAE\u4FE1\u7ED1\u5B9A\u8D26\u53F7?(f=d,h=p,v=u):(f=o,h=s,v=l),y=Q(f),dt([{el:h,options:{value:y,validRules:$e}}])&&Fa(function(){return F(void 0,void 0,void 0,function(){var m;return j(this,g=>{switch(g.label){case 0:return[4,Ss({location:y,type:e})];case 1:return(m=g.sent())&&(m.status&&ut.includes(m.status)?(Re[Number(m.status)]&&ae(Number(m.status),h,Re[Number(m.status)]),Xt[Number(m.status)]&&ae(Number(m.status),v,m.info||"")):t()),[2]}})})}),[2]})})},Rr=function(e,t,n,r){return F(void 0,void 0,void 0,function(){var a;return j(this,o=>{switch(o.label){case 0:return rr(t),[4,$a({ticket:e,type:Gt.\u7ED1\u5B9A})];case 1:return a=o.sent(),t.status&&ut.includes(t.status)?Ee[Number(t.status)]&&ae(Number(t.status),r,Ee[Number(t.status)]):a.action===3?mt(t,n):ae(Number(t.status),r,"\u8D26\u6237\u7ED1\u5B9A\u5931\u8D25"),[2]}})})},Ps=function(e){return F(void 0,void 0,void 0,function(){var t,n;return j(this,function(r){return pt(),t=e.inputLoginType,n=e.loginOptions,t===Ge.\u5BC6\u7801\u767B\u5F55?function(a){var o=ye(),s=o.loginUsernameInput,c=o.loginPasswordInput,l=o.loginUsernameTip,i=o.loginPasswordTip,d=Q(s),p=Q(c);dt([{el:l,options:{value:d,validRules:ja}},{el:i,options:{value:p,validRules:Ua}}])&&Fa(function(){return F(void 0,void 0,void 0,function(){var u;return j(this,f=>{switch(f.label){case 0:return[4,_s({username:d,password:er(p).toString()})];case 1:return(u=f.sent())?u.status&&ut.includes(u.status)?(Ee[Number(u.status)]&&ae(Number(u.status),l,Ee[Number(u.status)]),(Re[Number(u.status)]||qs[Number(u.status)])&&ae(Number(u.status),l,"\u8D26\u6237\u6216\u5BC6\u7801\u9519\u8BEF"),[3,5]):[3,2]:[3,5];case 2:return a.ticket?[4,Rr(a.ticket,u,a,l)]:[3,4];case 3:return f.sent(),[3,5];case 4:mt(u,a),f.label=5;case 5:return[2]}})})})}(n):function(a){F(void 0,void 0,void 0,function(){var o,s,c,l,i,d,p,u;return j(this,f=>{switch(f.label){case 0:return o=ye(),s=o.loginUsernameInput,c=o.loginCodeInput,l=o.loginUsernameTip,i=o.loginCodeTip,o.loginPasswordTip,d=Q(s),p=Q(c),dt([{el:l,options:{value:d,validRules:$e}},{el:i,options:{value:p,validRules:Ze}}])?[4,Ls({phone:d,captcha:p})]:[3,5];case 1:return(u=f.sent())?u.status&&ut.includes(u.status)?(Ee[Number(u.status)]&&ae(Number(u.status),l,Ee[Number(u.status)]),Re[Number(u.status)]&&ae(Number(u.status),l,"\u8D26\u6237\u6216\u5BC6\u7801\u9519\u8BEF"),Xt[Number(u.status)]&&ae(Number(u.status),i,"\u9A8C\u8BC1\u7801\u9519\u8BEF"),[3,5]):[3,2]:[3,5];case 2:return a.ticket?[4,Rr(a.ticket,u,a,l)]:[3,4];case 3:return f.sent(),[3,5];case 4:mt(u,a),f.label=5;case 5:return[2]}})})}(n),[2]})})},rr=e=>{var t,n,r,a,o,s,c;Vi(e),t="bbs_token",n={value:e.token,expire:2592e3,path:"/"},r=n.value,a=r===void 0?"":r,o=n.expire,s=o===void 0?0:o,c=n.path,document.cookie="".concat(t,"=").concat(a,";max-age=").concat(s,"; ").concat("path=".concat(c))},As=function(e,t){return F(void 0,void 0,void 0,function(){return j(this,function(n){return e?F(void 0,void 0,void 0,function(){var r,a,o,s,c,l,i,d,p,u,f,h,v,y,k,m;return j(this,g=>{switch(g.label){case 0:return r=ye(),a=r.registerPhoneInput,o=r.registerPhoneTip,s=r.registerCodeInput,c=r.registerCodeTip,l=r.btnToLogin,i=r.registerPasswordInput,d=r.registerPasswordTip,p=r.registerPasswordTwiceInput,u=r.registerPasswordTwiceTip,f=r.commonSuccessTip,h=Q(a),v=Q(s),y=Q(i),k=Q(p),dt([{el:o,options:{value:h,validRules:$e}},{el:c,options:{value:v,validRules:Ze}},{el:d,options:{value:y,validRules:Ha}},{el:u,options:{value:[y,k],validRules:Wa}}])?[4,Es({phone:h,captcha:v,new_password:er(y).toString()})]:[3,2];case 1:(m=g.sent())&&(m.status&&ut.includes(m.status)?(Ee[Number(m.status)]&&ae(Number(m.status),o,Ee[Number(m.status)]),Re[Number(m.status)]&&ae(Number(m.status),o,Re[Number(m.status)]),Xt[Number(m.status)]&&ae(Number(m.status),c,"\u9A8C\u8BC1\u7801\u9519\u8BEF")):(l.click(),ae(Number(m.status),f,"\u91CD\u7F6E\u5BC6\u7801\u6210\u529F"))),g.label=2;case 2:return[2]}})}):function(r){F(void 0,void 0,void 0,function(){var a,o,s,c,l,i,d,p,u,f;return j(this,h=>{switch(h.label){case 0:return a=ye(),o=a.registerPhoneInput,s=a.registerPhoneTip,c=a.registerCodeInput,l=a.registerCodeTip,i=a.btnToLogin,d=a.commonSuccessTip,p=Q(o),u=Q(c),dt([{el:s,options:{value:p,validRules:$e}},{el:l,options:{value:u,validRules:Ze}}])?[4,xs({phone:p,captcha:u,ticket:r.ticket?r.ticket:""})]:[3,4];case 1:return(f=h.sent())?f.status&&ut.includes(f.status)?(Ee[Number(f.status)]&&ae(Number(f.status),s,Ee[Number(f.status)]),Re[Number(f.status)]&&ae(Number(f.status),s,Re[Number(f.status)]),Xt[Number(f.status)]&&ae(Number(f.status),l,"\u9A8C\u8BC1\u7801\u9519\u8BEF"),[3,4]):[3,2]:[3,4];case 2:return i.click(),[4,mt(le({role_id_list:[]},f),r)];case 3:h.sent(),ae(Number(f.status),d,"\u6CE8\u518C\u6210\u529F"),h.label=4;case 4:return[2]}})})}(t),[2]})})},Fn=(e,t)=>{var n=t.time,r=t.customClass;e.innerText=n+"";var a=n-1;return e.classList.add(r),e.style.pointerEvents="none",ar=setInterval(()=>{a?(e.innerText="\u53D1\u9001\u6210\u529F ".concat(a,"S"),a--):(e.innerText="\u83B7\u53D6\u9A8C\u8BC1\u7801",pt(),e.classList.remove(r),e.style.pointerEvents="auto")},1e3),()=>{pt()}},Q=e=>e.value,Ya=e=>{e.forEach(t=>{((n,r)=>{r===void 0&&(r=""),n.value=r})(t)})},Va=e=>{e.forEach(t=>{he(t,{tip:"",opacity:"0"})})},$r=()=>{var e=ye(),t=e.loginUsernameInput,n=e.loginCodeInput,r=e.loginPasswordInput,a=e.loginUsernameTip,o=e.loginCodeTip,s=e.loginPasswordTip;Ya([t,n,r]),Va([a,o,s])},kn=()=>{var e=ye(),t=e.registerPhoneInput,n=e.registerCodeInput,r=e.registerPasswordInput,a=e.registerPasswordTwiceInput,o=e.registerCodeTip,s=e.registerPhoneTip,c=e.registerPasswordTip,l=e.registerPasswordTwiceTip;Ya([t,n,r,a]),Va([o,s,c,l])},ot=()=>{var e;pt();var t=ye().modalContainer;(e=t.parentNode)===null||e===void 0||e.removeChild(t)},mt=function(e,t){return F(void 0,void 0,void 0,function(){var n,r,a,o,s;return j(this,c=>{switch(c.label){case 0:return c.trys.push([0,3,,4]),[4,ue("/user/v1/user/user_info",{fields:["amz123_vip","unread_msg_count","sign_info"]},{headers:{Authorization:e.token}})];case 1:return n=c.sent().info,r=le(le({},n),e),rr(r),ot(),a=t.refresh,o=a===void 0||a,[4,(s=t.callback)==null?void 0:s()];case 2:return c.sent(),o&&window.location.reload(),[3,4];case 3:return c.sent(),[3,4];case 4:return[2]}})})},ar=null,pt=()=>{clearInterval(ar)},Ja=()=>{var e=new URLSearchParams(window.location.search);e.delete("code"),e.delete("is_login");var t=e.toString(),n="".concat(window.location.origin).concat(window.location.pathname,"?").concat(t);window.history.replaceState({},"",n)},Tt=function(e){Ba();var t=ye(),n=t.btnLoginBack,r=t.btnRegisterBack,a=t.btnClose,o=t.btnToRegister,s=t.btnToWeChatLogin,c=t.btnToLogin,l=t.btnWxLogin,i=t.loginContainer,d=t.loginInputContainer,p=t.pwdContainer,u=t.codeContainer,f=t.qrCodeContainer,h=t.registerContainer,v=t.registerFirstStepContainer,y=t.btnLogin,k=t.btnLoginGetCode,m=t.btnRegisterGetCode,g=t.btnRegister,w=t.btnResetPassword,E=t.registerToLoginContainer,z=t.loginUsernameInput,T=t.loginUsernameTip,O=t.loginCodeInput,q=t.loginCodeTip,D=t.loginPasswordInput,A=t.loginPasswordTip,P=t.registerPhoneInput,B=t.registerPhoneTip,R=t.registerCodeInput,I=t.registerCodeTip,Y=t.registerPassword,U=t.registerPasswordInput,G=t.registerPasswordTip,oe=t.registerPasswordTwice,K=t.registerPasswordTwiceInput,b=t.registerPasswordTwiceTip,S=t.toPwdLoginTab,_=t.toCodeLoginTab,x=t.btnOtherLogin,je=t.btnBackCheck,tt=t.loginTabsWrapper,me=t.btnPrivacy,ve=1,_e=Ge.\u9A8C\u8BC1\u7801\u767B\u5F55,Ne=!1;a==null||a.addEventListener("click",()=>{ot()}),n==null||n.addEventListener("click",()=>{Z(n),Z(d),tt.style.display="flex",Z(f,{display:"flex"}),pt()}),r==null||r.addEventListener("click",()=>{ve===1?(Z(i),Z(h),kn()):Z(v),ve=Math.min(1,ve--)}),y==null||y.addEventListener("click",()=>{Ps({inputLoginType:_e,loginOptions:e})}),g==null||g.addEventListener("click",()=>{As(Ne,e)}),k==null||k.addEventListener("click",()=>{Bn(qe.\u624B\u673A\u53F7\u9A8C\u8BC1\u5E76\u6CE8\u518C\u767B\u5F55,()=>{Fn(k,{time:60,customClass:"amz-login-count-down"})})}),m==null||m.addEventListener("click",function(){return F(void 0,void 0,void 0,function(){return j(this,H=>(Bn(Ne?qe.\u627E\u56DE\u5BC6\u7801\u9884\u6821\u9A8C:qe.\u6CE8\u518C\u53D1\u9001\u624B\u673A\u9A8C\u8BC1\u7801,()=>{Fn(m,{time:60,customClass:"amz-register-count-down"})}),[2]))})}),s==null||s.addEventListener("click",function(){Z(n),Z(d),tt.style.display="none",Z(f,{display:"flex"}),function(H){F(void 0,void 0,void 0,function(){var $,X,pe,re;return j(this,function(ie){switch(ie.label){case 0:return($=ye().qrCodeImg).style.display="none",[4,zs()];case 1:return X=ie.sent(),(pe=X.qr_code_url)&&($.style.display="block",$.src=pe,re=pe.replace("https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=",""),ar=setInterval(function(){return F(void 0,void 0,void 0,function(){var se,Ce;return j(this,we=>{switch(we.label){case 0:return[4,$a({ticket:re,type:Gt.\u767B\u9646})];case 1:return(se=we.sent()).action!==Lt.\u7EE7\u7EED\u7B49\u5F85&&pt(),se.action===Lt.\u767B\u5F55\u6210\u529F&&mt({app_uid:se.app_uid,avatar:se.avatar,expire:se.expire,token:se.token,username:se.username,role_id_list:se.role_id_list},H),(se==null?void 0:se.action)===Lt.\u8DF3\u8F6C\u5230\u6CE8\u518C\u754C\u9762&&(ot(),Ce=document.getElementById("close-wx-login"),Rn(!1,!!Ce,!0),Tt(le(le({},H),{ticket:re}))),[2]}})})},1e3)),[2]}})})}(e)}),l==null||l.addEventListener("click",()=>{if(window.navigator.userAgent.toLowerCase().includes("micromessenger")){var H=function(ie,se,Ce){var we=new URL(ie),Me=new URLSearchParams(we.search);return Me.has(se)?Me.set(se,Ce):Me.append(se,Ce),we.search=Me.toString(),we.href}(window.location.href,"is_login","true"),$=document.querySelector(".header-login-btn"),X=$==null?void 0:$.getAttribute("data-app-id");if(!X){var pe=window.location.href;X=pe.startsWith("https://www.tt123.com")?"wx52dacb8e5752eb7d":pe.startsWith("https://www.dny123.com")?"wxa018ca4fa3fde5cc":"wx46da02edf61636f9"}var re="https://open.weixin.qq.com/connect/oauth2/authorize?appid=".concat(X,"&redirect_uri=").concat(encodeURIComponent(H),"&response_type=code&scope=snsapi_userinfo#wechat_redirect");window.open(re,"_self")}}),x==null||x.addEventListener("click",()=>{ot(),Rn(!0),Tt(e)}),je==null||je.addEventListener("click",()=>{ot(),Ra(),Tt(e)}),w==null||w.addEventListener("click",()=>{Ne=!0,ve=1,Z(i),Z(h),Z(E,{target:"none"}),Z(Y,{target:"flex"}),Z(oe,{target:"flex"}),Z(v,{target:"block"}),g.innerText="\u91CD\u7F6E\u5BC6\u7801",kn()});var Ue,vt=H=>{H==null||H.addEventListener("click",()=>{if(_e==="pwd"&&H.innerText==="\u624B\u673A\u53F7\u767B\u5F55")_e=Ge.\u9A8C\u8BC1\u7801\u767B\u5F55,(()=>{var $=ye(),X=$.loginUsernameInput,pe=$.toCodeLoginTab,re=$.toPwdLoginTab,ie=$.loginInputContainer;X.placeholder="\u624B\u673A\u53F7",re.classList.remove("amz-login-tab-active"),pe.classList.add("amz-login-tab-active"),ie.classList.add("animate-move-left"),ie.addEventListener("animationend",()=>{ie.classList.remove("animate-move-left")})})();else{if(_e!=="code"||H.innerText!=="\u8D26\u53F7\u5BC6\u7801\u767B\u5F55")return;_e=Ge.\u5BC6\u7801\u767B\u5F55,(()=>{var $=ye(),X=$.loginUsernameInput,pe=$.toCodeLoginTab,re=$.toPwdLoginTab,ie=$.loginInputContainer;X.placeholder="\u624B\u673A / Email / \u7528\u6237\u540D",pe.classList.remove("amz-login-tab-active"),re.classList.add("amz-login-tab-active"),ie.classList.add("animate-move-right"),ie.addEventListener("animationend",()=>{ie.classList.remove("animate-move-right")})})()}$r(),Z(p,{display:"flex"}),Z(u,{display:"flex"})})};vt(_),vt(S),(Ue=o)==null||Ue.addEventListener("click",()=>{ve=1,Ne=!1,Z(i),Z(h),Z(Y,{target:"none"}),Z(oe,{target:"none"}),Z(E,{target:"block"}),Z(v,{target:"block"}),e.ticket?g.innerText="\u6CE8\u518C\u5E76\u7ED1\u5B9A":g.innerText="\u6CE8\u518C",$r()}),(H=>{H==null||H.addEventListener("click",()=>{Z(i),Z(h),kn()})})(c),z==null||z.addEventListener("blur",()=>{Se(T,{value:Q(z),validRules:_e===Ge.\u5BC6\u7801\u767B\u5F55?ja:$e})}),z==null||z.addEventListener("focus",()=>{he(T,{tip:"",opacity:"0"})}),D==null||D.addEventListener("blur",()=>{Se(A,{value:Q(D),validRules:Ua})}),D==null||D.addEventListener("focus",()=>{he(A,{tip:"",opacity:"0"})}),O==null||O.addEventListener("blur",()=>{Se(q,{value:Q(O),validRules:Ze})}),O==null||O.addEventListener("focus",()=>{he(q,{tip:"",opacity:"0"})}),O==null||O.addEventListener("input",H=>{var $,X=H.target;X.value=($=X.value)===null||$===void 0?void 0:$.replace(/\D+/g,"")}),P==null||P.addEventListener("blur",()=>{Se(B,{value:Q(P),validRules:$e})}),P==null||P.addEventListener("focus",()=>{he(B,{tip:"",opacity:"0"})}),R==null||R.addEventListener("blur",()=>{Se(I,{value:Q(R),validRules:Ze})}),R==null||R.addEventListener("focus",()=>{he(I,{tip:"",opacity:"0"})}),R==null||R.addEventListener("input",H=>{var $,X=H.target;X.value=($=X.value)===null||$===void 0?void 0:$.replace(/\D+/g,"")}),U==null||U.addEventListener("blur",()=>{Se(G,{value:Q(U),validRules:Ha})}),U==null||U.addEventListener("focus",()=>{he(G,{tip:"",opacity:"0"})}),K==null||K.addEventListener("blur",()=>{Se(b,{value:[Q(U),Q(K)],validRules:Wa})}),K==null||K.addEventListener("focus",()=>{he(b,{tip:"",opacity:"0"})}),me==null||me.addEventListener("click",()=>{/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator==null?void 0:navigator.userAgent)?window.open("/disclaimer","_self"):window.open("/disclaimer","_blank")})},Ns=function(e){Ba();var t=$n(),n=t.btnClose,r=t.wxPhoneInput,a=t.wxCodeInput,o=t.wxPhoneTip,s=t.wxCodeTip,c=t.btnBind,l=t.btnWxGetCode,i=t.btnBackToLogin;n==null||n.addEventListener("click",()=>{ot()}),r.addEventListener("blur",()=>{Se(o,{value:Q(r),validRules:$e})}),r.addEventListener("focus",()=>{he(o,{tip:"",opacity:"0"})}),a.addEventListener("blur",()=>{Se(s,{value:Q(r),validRules:Ze})}),a.addEventListener("focus",()=>{he(s,{tip:"",opacity:"0"})}),l==null||l.addEventListener("click",function(){return F(void 0,void 0,void 0,function(){return j(this,d=>(Bn(qe.\u5FAE\u4FE1\u7ED1\u5B9A\u8D26\u53F7,()=>{Fn(l,{time:60,customClass:"amz-wx-bind-count-down"})}),[2]))})}),c==null||c.addEventListener("click",function(){(function(d){var p=d.code,u=d.loginOptions;F(void 0,void 0,void 0,function(){var f,h,v,y,k,m,g,w;return j(this,E=>{switch(E.label){case 0:return f=$n(),h=f.wxPhoneInput,v=f.wxCodeInput,y=f.wxPhoneTip,k=f.wxCodeTip,m=Q(h),g=Q(v),dt([{el:y,options:{value:m,validRules:$e}},{el:k,options:{value:g,validRules:Ze}}])?[4,Cs({phone:m,captcha:g,code:p||""})]:[3,2];case 1:(w=E.sent())&&(w.status&&w.status!==0?ae(Number(w.status),k,w.info||""):(Ja(),mt(w,u))),E.label=2;case 2:return[2]}})})})({loginOptions:e,code:e.code||""})}),i==null||i.addEventListener("click",()=>{n.click(),Yt({refresh:!1,callback:()=>{}})})};function Ga(){return window.navigator.userAgent.toLowerCase().includes("micromessenger")}var Yt=e=>{e===void 0&&(e={});var t=document.getElementById("close-wx-login"),n=window.location.href;return n.startsWith("https://www.chat123.ai")||n.startsWith("https://www.amz123.chat")?window.open("https://www.amz123.com/user-login?from=".concat(n),"_self"):Ga()&&!t?(Ra(),void Tt(e)):(Rn(!1,!!t),void Tt(e))},Ms=function(){return F(void 0,void 0,void 0,function(){var e,t,n,r,a,o,s,c,l,i,d,p,u,f,h,v,y,k,m,g;return j(this,w=>{switch(w.label){case 0:e=!1,t=!1,w.label=1;case 1:return w.trys.push([1,4,5,6]),n=(h=Oa())!==null&&h!==void 0?h:Ta(),u=document.querySelector(".header-login-btn"),f=document.getElementById("_login_in_btn_"),r=document.querySelector(".header-login-group"),a=document.querySelector(".mobile-header-user-name"),o=document.querySelector(".mobile-header-user-icon"),s=Kn(),a&&a.addEventListener("click",E=>{a.className!=="mobile-header-user-name login"&&(E.preventDefault(),Yt())}),u&&u.addEventListener("click",E=>{E.preventDefault(),Yt()}),o&&!s&&o.addEventListener("click",E=>{E.preventDefault(),Yt()}),n?(u.className="header-login-btn",f.style.display="flex",document.body.clientWidth<1340?r==null||r.setAttribute("style","margin-right: -28px;"):r==null||r.setAttribute("style","margin-right: -38px;"),[4,ue("/user/v1/user/user_info",{fields:["amz123_vip","unread_msg_count","sign_info"]})]):[3,3];case 2:c=w.sent().info,l=JSON.parse((v=localStorage.getItem("amz123-global-user-info"))!==null&&v!==void 0?v:"{}"),l=le(le({},l),{is_vip:(y=c==null?void 0:c.amz123_vip)===null||y===void 0?void 0:y.is_vip,unread_msg_count:(k=c==null?void 0:c.unread_msg_count)===null||k===void 0?void 0:k.total,sign_info:(m=c==null?void 0:c.sign_info)===null||m===void 0?void 0:m.is_sign}),t=(g=c==null?void 0:c.amz123_vip)===null||g===void 0?void 0:g.is_vip,localStorage.setItem("amz123-global-user-info",JSON.stringify(l)),w.label=3;case 3:return s?(e=!0,document.body.getBoundingClientRect().width<992&&(i=document.querySelector(".logout-icon"),d=document.querySelector(".mobile-header-user-icon img"),p=document.querySelector(".mobile-header-user-name"),i.className="logout-icon active",i.addEventListener("click",()=>{lt(),window.location.href=window.location.href,window.history.go(0)}),d.setAttribute("src",s.avatar),p.className="mobile-header-user-name login",p.innerHTML=s.username)):r==null||r.setAttribute("style",""),[3,6];case 4:return w.sent(),[3,6];case 5:return u=document.querySelector(".header-login-btn"),f=document.getElementById("_login_in_btn_"),u&&f&&(u.className=e?"header-login-btn":"header-login-btn active",t&&(f.className="header-user-icon vip-sign"),f.style.opacity=e?"1":"0"),[7];case 6:return[2]}})})},Ds=function(e){return F(void 0,void 0,void 0,function(){var t;return j(this,function(n){try{(t=document.querySelector("#_daily_sign_in_"))==null||t.addEventListener("click",function(r){return F(void 0,void 0,void 0,function(){return j(this,a=>{switch(a.label){case 0:r.stopPropagation(),r.preventDefault(),a.label=1;case 1:return a.trys.push([1,3,,4]),[4,ue("/user/v1/credits/sign")];case 2:return a.sent(),t.className="sign-in-btn active",t.textContent="\u5DF2\u7B7E\u5230",setTimeout(()=>{window.open("/ax_sign.htm","_self")},1e3),[3,4];case 3:return a.sent(),[3,4];case 4:return[2]}})})})}catch{}return[2]})})};function Is(e){return F(this,void 0,void 0,function(){var t,n,r,a,o,s,c,l,i,d,p,u,f,h=this;return j(this,function(v){switch(v.label){case 0:(y=>{if(y!=null&&y.avatar){var k=Array.from(document.querySelectorAll(".amz-mobile-header .amz-nav-title")),m=k[k.length-1];if(m){m.remove();var g=document.querySelectorAll(".amz-mobile-header-main a"),w=g[g.length-1];w.setAttribute("href","/my"),w.innerHTML=`
        <img src="`.concat(y.avatar,`" style="height: 28px"/>
      `)}}})(e),t=document.getElementById("_login_in_btn_"),v.label=1;case 1:if(v.trys.push([1,5,,6]),!((e==null?void 0:e.username)&&e.username.length>0&&e.role_id_list))return[3,3];if(n=e.avatar,r=e.username,a=e.unread_notices,o=e.is_vip,e.uid,s=e.sign_info,t===null)throw Error("\u65E0\u6548\u7684\u767B\u5F55\u6309\u94AE\u7ED3\u6784");return(c=document.createElement("img")).style.width="26px",c.style.height="26px",c.style.position="relative",c.style.borderRadius="50%",c.setAttribute("src",n),+a>0&&t.classList.add("amz-red-circle"),t.innerHTML="",t.appendChild(c),l=t.getBoundingClientRect().left,i=document.querySelector(".amz-header-vip"),d='<div class="login-down" style="left: '.concat(l-235,`px">
      <div class="login-div">
        <div class="info">
          <div class="info-thumb">
            <a class=`).concat(o?"vip-sign":"",' href="/my"><img src="').concat(n,`"
                style="width: 36px; height: 36px;border-radius:50%;z-index:-1;" /></a>
          </div>
          <a href="/my">
            <section class="user-name">`).concat(r,`</section>
          </a>
          <a id="_daily_sign_in_" class="sign-in-btn`).concat(s?" active":"",'">').concat(s?"\u5DF2\u7B7E\u5230":"\u7B7E\u5230",`</a>
        </div>
        <a data-sdk-position="\u4F1A\u5458banner" data-sdk-report="1" data-sdk-index="1" href="/vip" target="_blank" class="vip`).concat(o?" active":"",'" style="display: ').concat(i?"block":"none",`">
          <div class="vip-btn"><span>\u67E5\u770B\u6743\u76CA</span></div>
        </a>
        <div class="main">
          <div class="main-menu">
            <a class="item" href="/my/article?tab=4">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
                <path
                  d="M8.9985 1.87744L6.70612 6.55654L1.49902 7.3116L5.27111 10.9994L4.36942 16.1274L8.9985 13.6596L13.6285 16.1274L12.7335 10.9994L16.499 7.3116L11.3208 6.55654L8.9985 1.87744Z"
                  stroke="#111827" stroke-width="1.5" stroke-linejoin="round"></path>
              </svg>
              <span>\u6211\u7684\u6536\u85CF</span>
            </a>
            <a class="item" href="/my/article?tab=1">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
                <path d="M15.749 2.25L1.49902 7.55186L8.99902 9.00311L10.876 16.5L15.749 2.25Z" stroke="#111827"
                  stroke-width="1.5" stroke-linejoin="round"></path>
                <path d="M9.00391 9.00143L11.1252 6.88013" stroke="#111827" stroke-width="1.5" stroke-linecap="round"
                  stroke-linejoin="round"></path>
              </svg>
              <span>\u6211\u7684\u53D1\u5E03</span>
            </a>
            <a class="item" href="/my/custom_nav">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
                <path
                  d="M9 17C4.58172 17 1 13.4182 1 9C1 4.58172 4.58172 1 9 1C13.4182 1 17 4.58172 17 9C17 13.4182 13.4182 17 9 17ZM9 15.4C12.5346 15.4 15.4 12.5346 15.4 9C15.4 5.46538 12.5346 2.6 9 2.6C5.46538 2.6 2.6 5.46538 2.6 9C2.6 12.5346 5.46538 15.4 9 15.4ZM5.73548 8.8758C5.4776 8.7791 5.4754 8.41512 5.73208 8.3153L11.5646 6.0471C11.8079 5.9525 12.0475 6.19215 11.953 6.43541L9.68475 12.2693C9.58493 12.526 9.22089 12.5238 9.12422 12.2659L8.24787 9.92772C8.21745 9.84657 8.15343 9.78254 8.07229 9.75211L5.73548 8.8758Z"
                  fill="#111827"></path>
              </svg>
              <span>\u6211\u7684\u5BFC\u822A</span>
            </a>
            <a class="item`).concat(a>0?" amz-red-circle-login":"",`" href="/my/message?tab=0">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
                <path
                  d="M3.75 14.25V6.75C3.75 3.8505 6.1005 1.5 9 1.5C11.8995 1.5 14.25 3.8505 14.25 6.75V14.25M1.5 14.25H16.5"
                  stroke="#111827" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                <path
                  d="M9 16.5C10.0355 16.5 10.875 15.6605 10.875 14.625V14.25H7.125V14.625C7.125 15.6605 7.96448 16.5 9 16.5Z"
                  stroke="#111827" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
              <span>\u6211\u7684\u6D88\u606F</span>
            </a>
            <a class="item" href="/my">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
                <path
                  d="M9 7.50122C10.6569 7.50122 12 6.15807 12 4.50122C12 2.84437 10.6569 1.50122 9 1.50122C7.34315 1.50122 6 2.84437 6 4.50122C6 6.15807 7.34315 7.50122 9 7.50122Z"
                  stroke="#111827" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                <path d="M15.75 16.4988C15.75 12.7709 12.7279 9.74878 9 9.74878C5.27209 9.74878 2.25 12.7709 2.25 16.4988"
                  stroke="#111827" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
              <span>\u4E2A\u4EBA\u4E2D\u5FC3</span>
            </a>
            <a class="item" href="javascript:window.__logout__();">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
                <path d="M8.99689 2.25H2.25V15.75H9" stroke="#111827" stroke-width="1.5" stroke-linecap="round"
                  stroke-linejoin="round"></path>
                <path d="M12.375 12.375L15.75 9L12.375 5.625" stroke="#111827" stroke-width="1.5" stroke-linecap="round"
                  stroke-linejoin="round"></path>
                <path d="M5.99902 8.99658H15.749" stroke="#111827" stroke-width="1.5" stroke-linecap="round"
                  stroke-linejoin="round"></path>
              </svg>
              <span>\u5B89\u5168\u9000\u51FA</span>
            </a>
          </div>
        </div>
      </div>
      </div>`),t==null||t.insertAdjacentHTML("afterend",d),p=Ia(()=>{var y=document.getElementById("_login_in_btn_");l=(y==null?void 0:y.getBoundingClientRect().left)||1e3,document.querySelector(".login-down").style.left="".concat(l-230,"px")},100),window.addEventListener("resize",p),window.__logout__=function(){return F(h,void 0,void 0,function(){return j(this,y=>{switch(y.label){case 0:return[4,ue("/user/v1/user/logout")];case 1:return y.sent(),lt(),window.location.href=window.location.href,window.history.go(0),[2]}})})},u=document.querySelector(".login-down"),t==null||t.addEventListener("mouseenter",()=>{u==null||u.setAttribute("class","login-down active")}),t==null||t.addEventListener("mouseleave",()=>{u==null||u.setAttribute("class","login-down")}),f="ontouchstart"in document.documentElement==1?"touchstart":"click",t==null||t.addEventListener(f,()=>{(u==null?void 0:u.className)==="login-down"?u==null||u.setAttribute("class","login-down active"):u==null||u.setAttribute("class","login-down")}),[4,Ds()];case 2:return v.sent(),[3,4];case 3:Yi(),v.label=4;case 4:return[3,6];case 5:return v.sent(),[3,6];case 6:return[2]}})})}function jn(e){return jn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?t=>typeof t:t=>t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t,jn(e)}function sn(e,t){if(t.length<e)throw new TypeError(e+" argument"+(e>1?"s":"")+" required, but only "+t.length+" present")}function Br(e){sn(1,arguments);var t=Object.prototype.toString.call(e);return e instanceof Date||jn(e)==="object"&&t==="[object Date]"?new Date(e.getTime()):typeof e=="number"||t==="[object Number]"?new Date(e):new Date(NaN)}var Rs=6e4,$s=36e5;function Xa(e,t){return sn(2,arguments),Br(e).getTime()-Br(t).getTime()}var Fr={ceil:Math.ceil,round:Math.round,floor:Math.floor,trunc:function(e){return e<0?Math.ceil(e):Math.floor(e)}},Bs="trunc";function Za(e){return e?Fr[e]:Fr[Bs]}function Qa(e,t,n){sn(2,arguments);var r=Xa(e,t)/$s;return Za(void 0)(r)}function Cn(e){if(e===0||e==null)return"";var t=function(a){if(a===0||a==null)return"";var o=new Date(1e3*a),s=o.getFullYear();return s}(e),n=function(a){if(a===0||a==null)return"";var o=new Date(1e3*a),s=o.getMonth()+1<10?"0"+(o.getMonth()+1):o.getMonth()+1;return s}(e),r=function(a){if(a===0||a==null)return"";var o=new Date(1e3*a),s=o.getDate()<10?"0"+o.getDate():o.getDate();return s}(e);return t+"-"+n+"-"+r}function Rt(e){var t=~~(Qa(1e3*Number(e),new Date)/24);return t<0&&(t=0),t}function $t(e){var t=Qa(1e3*Number(e),new Date)%24;return t<0?t=0:t<10&&(t="0"+t),t}function Bt(e){var t=function(n,r,a){sn(2,arguments);var o=Xa(n,r)/Rs;return Za(void 0)(o)}(1e3*Number(e),new Date)%60;return t<0?t=0:t<10&&(t="0"+t),t}function Ft(e){var t=(Number(e)-Math.round(Number(new Date)/1e3))%60;return t<0?t=0:t<10&&(t="0"+t),t}var xt="AMZ123_SDK",or=e=>{var t=localStorage.getItem(e);if(t)return JSON.parse(t)},Un=(e,t)=>{try{localStorage.setItem(e,JSON.stringify(t))}catch{}},jr=(e,t,n)=>{var r=or(e);return r?r[t]:n},Ur=(e,t)=>{var n=or(e);n&&(delete n[t],Un(e,n))},ir=`<div class="one-activity-style-close">
<svg
  xmlns="http://www.w3.org/2000/svg"
  width="22"
  height="22"
  viewBox="0 0 22 22"
  fill="none"
>
  <path
    d="M6.41666 6.41675L15.5833 15.5834"
    stroke="#ADB5BD"
    stroke-width="1.25"
    stroke-linecap="round"
    stroke-linejoin="round"
  />
  <path
    d="M6.41666 15.5834L15.5833 6.41675"
    stroke="#ADB5BD"
    stroke-width="1.25"
    stroke-linecap="round"
    stroke-linejoin="round"
  />
</svg>
</div>`,it=e=>{var t=1e3*Number(e),n=new Date(t);return n.getFullYear()+"-"+("0"+(n.getMonth()+1)).slice(-2)+"-"+("0"+n.getDate()).slice(-2)},Fs=e=>ir+=`
    <div class="one-activity-style-content">
      <div class="one-activity-style-content__left">
        <a class="one-activity-style-context" data-partner-id="`.concat(e[0].ad_partner_id,'" title="').concat(e[0].title,'" href="').concat(e[0].url,'" target="_blank" rel="nofollow">').concat(e[0].title,`</a>
        <a class="one-activity-style-content__button" href="`).concat(e[0].url,'" target="_blank" title="').concat(e[0].title,`" rel="nofollow">
          <span class="activity-time-text">`).concat(it(e[0].live_time),`</span>
          <span class="one-activity-divide"></span>
          <span class="activity-important-text">\u7ACB\u5373\u9884\u7EA6</span>
        </a>
      </div>

      <a class="one-activity-style-content__right" href="`).concat(e[0].url,'" target="_blank" rel="nofollow" title="').concat(e[0].title,`">
        <img class="one-activity-style-content__image" src="`).concat(e[0].pic_url,`" alt="" />
      </a>
    </div>
  `),js=e=>ir+=`
  <div class="one-activity-style-content">
    <div class="one-activity-style-content__left">
      <a class="one-activity-style-context" data-partner-id="`.concat(e[0].ad_partner_id,'" rel="nofollow" title="').concat(e[0].title,'" href="').concat(e[0].url,`" target="_blank"
        >`).concat(e[0].title,`</a
      >
      <a class="one-activity-style-content__button" rel="nofollow" title="`).concat(e[0].title,'" href="').concat(e[0].url,`" target="_blank">
        <span class="activity-time-text">`).concat(it(e[0].live_time),`</span>
        <span class="one-activity-divide"></span>
        <span class="activity-important-text">\u7ACB\u5373\u9884\u7EA6</span>
      </a>
    </div>

    <a class="one-activity-style-content__right" title="`).concat(e[0].title,'" href="').concat(e[0].url,`" target="_blank" rel="nofollow">
      <img class="one-activity-style-content__image" src="`).concat(e[0].pic_url,`" alt="" />
    </a>
  </div>
  <!-- \u7EBF -->
  `).concat('<div class="activity-line-margin"></div>',`
  <!-- \u7B2C\u4E8C\u4E2A\u6D3B\u52A8 -->
  <div class="one-activity-style-content">
    <div class="one-activity-style-content__left two-activity-content__left">
      <a class="one-activity-style-context" data-partner-id="`).concat(e[1].ad_partner_id,'" title="').concat(e[1].title,'" href="').concat(e[1].url,`" target="_blank" rel="nofollow"
        >`).concat(e[1].title,`</a
      >
      <a class="one-activity-style-content__button two-content__button" title="`).concat(e[1].title,'" href="').concat(e[1].url,`" target="_blank" rel="nofollow">
        <span class="activity-time-text">`).concat(it(e[1].live_time),`</span>
        <span class="one-activity-divide two-activity-divide"></span>
        <span class="activity-important-text">\u7ACB\u5373\u9884\u7EA6</span>
      </a>
    </div>

    <a class="one-activity-style-content__right two-activity-content" title="`).concat(e[1].title,'" href="').concat(e[1].url,`" target="_blank" rel="nofollow">
      <img class="one-activity-style-content__image" src="`).concat(e[1].pic_url,`" alt="">
    </a>
  </div>`),Us=e=>ir+=`
<div class="one-activity-style-content three-space">
  <div class="one-activity-style-content__left">
    <a class="one-activity-style-context" data-partner-id="`.concat(e[0].ad_partner_id,'" title="').concat(e[0].title,'" href="').concat(e[0].url,`" target="_blank" rel="nofollow"
      >`).concat(e[0].title,`</a
    >
    <a class="one-activity-style-content__button" href="`).concat(e[0].url,'" title="').concat(e[0].title,`" target="_blank" rel="nofollow">
      <span class="activity-time-text">`).concat(it(e[0].live_time),`</span>
      <span class="one-activity-divide"></span>
      <span class="activity-important-text">\u7ACB\u5373\u9884\u7EA6</span>
    </a>
  </div>

  <a class="one-activity-style-content__right" href="`).concat(e[0].url,'" title="').concat(e[0].title,`" target="_blank" rel="nofollow">
    <img class="one-activity-style-content__image" src="`).concat(e[0].pic_url,`" alt="" />
  </a>
</div>
<!-- \u7EBF -->
`).concat('<div class="activity-line-margin"></div>',`
<!-- \u7B2C\u4E8C\u4E2A\u6D3B\u52A8 -->
<div class="one-activity-style-content">
  <div class="one-activity-style-content__left two-activity-content__left">
    <a class="one-activity-style-context activity-small-context" data-partner-id="`).concat(e[1].ad_partner_id,'" title="').concat(e[1].title,'" href="').concat(e[1].url,`" target="_blank" rel="nofollow"
      >`).concat(e[1].title,`</a
    >
    <a class="one-activity-style-content__button two-content__button activity-small-button" href="`).concat(e[1].url,'" title="').concat(e[1].title,`" target="_blank" rel="nofollow">
      <span class="activity-time-text">`).concat(it(e[1].live_time),`</span>
      <span class="one-activity-divide two-activity-divide"></span>
      <span class="activity-important-text">\u7ACB\u5373\u9884\u7EA6</span>
    </a>
  </div>

  <a class="one-activity-style-content__right two-activity-content three-activity-margin" href="`).concat(e[1].url,'" title="').concat(e[1].title,`" target="_blank" rel="nofollow">
    <img class="one-activity-style-content__image activity-small-image" src="`).concat(e[1].pic_url,`" alt="" />
  </a>
</div>
<!-- \u7EBF -->
`).concat('<div class="activity-line-margin"></div>',`
<!-- \u7B2C\u4E09\u4E2A\u6D3B\u52A8 -->
<div class="one-activity-style-content">
  <div class="one-activity-style-content__left two-activity-content__left">
    <a class="one-activity-style-context activity-small-context" data-partner-id="`).concat(e[2].ad_partner_id,'" title="').concat(e[2].title,'" href="').concat(e[2].url,`" target="_blank" rel="nofollow"
      >`).concat(e[2].title,`</a
    >
    <a class="one-activity-style-content__button two-content__button activity-small-button" href="`).concat(e[2].url,'" title="').concat(e[2].title,`" target="_blank" rel="nofollow">
      <span class="activity-time-text">`).concat(it(e[2].live_time),`</span>
      <span class="one-activity-divide two-activity-divide"></span>
      <span class="activity-important-text">\u7ACB\u5373\u9884\u7EA6</span>
    </a>
  </div>

  <a class="one-activity-style-content__right two-activity-content three-activity-margin" href="`).concat(e[2].url,'" title="').concat(e[2].title,`" target="_blank" rel="nofollow">
    <img class="one-activity-style-content__image activity-small-image" src="`).concat(e[2].pic_url,`" alt="" />
  </a>
</div>`),Hr=(e,t,n,r)=>{var a=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator==null?void 0:navigator.userAgent),o=0,s=`
  <div class="one-activity-down-box" style="display:none">
  <a 
  class="one-activity-down-left" 
  href="`.concat(e[o].url,`" 
  target="_blank"
  data-sdk-report="1"
  data-sdk-position="0"
  data-sdk-index="`).concat(t==="seven-close"?"7":"8",`"
  data-sdk-url="`).concat(e[o].url,`"
  data-sdk-resource-id="`).concat(e[o].title,`"
  data-sdk-partner-id="`).concat(e[o].ad_partner_id,`"
>
\u6D3B\u52A8
</a>
<a 
  class="one-activity-down-pc-left" 
  href="`).concat(e[o].url,`" 
  target="_blank"
  data-sdk-report="1"
  data-sdk-position="0"
  data-sdk-index="`).concat(t==="seven-close"?"7":"8",`"
  data-sdk-url="`).concat(e[o].url,`"
  data-sdk-resource-id="`).concat(e[o].title,`"
  data-sdk-partner-id="`).concat(e[o].ad_partner_id,`"
>
<img src="https://img.amz123.com/static/images/index/one-activity-down-pc-title.svg" alt="">  
</a>
<a 
class="one-activity-down-context" 
href="`).concat(e[o].url,`" 
target="_blank"
data-sdk-report="1" 
data-sdk-position="0" 
data-sdk-index="`).concat(t==="seven-close"?"7":"8",`" 
data-sdk-url="`).concat(e[o].url,`"
data-sdk-resource-id="`).concat(e[o].title,`"
data-sdk-partner-id="`).concat(e[o].ad_partner_id,`"

>
`).concat(e[o].title,`
</a>
<a 
class="one-activity-down-pc-context" 
href="`).concat(e[o].url,`" 
target="_blank"
data-sdk-report="1" 
data-sdk-position="0" 
data-sdk-index="`).concat(t==="seven-close"?"7":"8",`" 
data-sdk-url="`).concat(e[o].url,`"
data-sdk-resource-id="`).concat(e[o].title,`"
data-sdk-partner-id="`).concat(e[o].ad_partner_id,`"
style="opacity:1"
>
<div style="position:relative;top:0">`).concat(e[o].title,`</div>
</a>
   <div class="one-activity-down-pc-arrow  `).concat(t,`">
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 6L8 10L4 6" stroke="#0F47F1" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
  </div>
<div class="one-activity-down-right `).concat(t,`">
 <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
  <path id="svg-close" d="M6.5 15L12.5 9L18.5 15" stroke="#ADB5BD" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
 </svg>
</div></div>`);n.innerHTML=s;var c=i=>{(()=>{var d=a?".one-activity-down-right":".one-activity-down-pc-arrow",p=document.querySelector(d);p&&(p.onclick=()=>r());var u=document.querySelector(a?".one-activity-down-left":".one-activity-down-pc-left"),f=document.querySelector(a?".one-activity-down-context":".one-activity-down-pc-context");i||(u.href=e[o].url,u["data-sdk-url"]=e[o].url,u["data-sdk-resource-id"]=e[o].title,u["data-sdk-partner-id"]=e[o].ad_partner_id,f.style.transition="opacity 0.5s ease",f.style.opacity="0",f.href=e[o].url,f["data-sdk-url"]=e[o].url,f["data-sdk-resource-id"]=e[o].title,f["data-sdk-partner-id"]=e[o].ad_partner_id),setTimeout(()=>{f!=null&&f.children&&(i||(o=(o+1)%e.length,f.children[0].innerText=e[o].title),setTimeout(()=>{var h,v=f,y=(h=f.children)===null||h===void 0?void 0:h[0];y&&(f.style.opacity="1",y.style.top=0,y.offsetHeight>v.offsetHeight?setTimeout(()=>{l(v,y)},2e3):setTimeout(()=>{c()},1e4))},100))},500)})()};setTimeout(()=>{c(!0);var i=document.querySelector(".one-activity-down");i&&new MutationObserver(d=>{d.forEach(p=>{p.target.style.display!=="none"&&c(!0)})}).observe(i,{attributes:!0,attributeFilter:["style"]})},100);var l=(i,d)=>{var p=0,u=0;(function f(){if((p-=1)<=-d.clientHeight&&(p=i.clientHeight,++u===3))return c();d.style.top="".concat(p,"px"),requestAnimationFrame(f)})()}};function Hs(e){var t,n,r,a,o,s,c,l;return F(this,void 0,void 0,function(){function i(Ve,Te,wt){var L=document.querySelectorAll(Ve);L&&L.forEach((M,J)=>{var ee=M.querySelectorAll("a"),ce=M.querySelector(Te).textContent,te=String(M.querySelector(Te).getAttribute("data-partner-id")),xe=ee[0].href;(!_n||_n&&J===0)&&d.push({resource_position:wt,resource_id:ce,partner_id:te,resource_url:xe,event_type:"show",path:location.pathname}),ee.forEach(Oo=>{Oo.addEventListener("click",()=>{globalThis.__batchReport__([{resource_position:wt,resource_id:ce,partner_id:te,resource_url:xe,path:location.pathname,event_type:"click"}],!0)})})})}var d,p,u,f,h,v,y,k,m,g,w,E,z,T,O,q,D,A,P,B,R,I,Y,U,G,oe,K,b,S,_,x,je,tt,me,ve,_e,Ne,Ue,vt,H,$,X,pe,re,ie,se,Ce,we,Me,hn,pr,Le,fr,hr,vr,gr,yr,wr,br,_r,vn,gn,yn,He,wn,gt,We,bn,Dt,It,nt,rt,yt,xr,Ye,ge,_n;return j(this,function(Ve){switch(Ve.label){case 0:d=[],p=!1,Ve.label=1;case 1:return Ve.trys.push([1,3,4,5]),[4,(wt=e==="/",F(void 0,void 0,void 0,function(){var L,M;return j(this,J=>{switch(J.label){case 0:return L=["0-1","0-2","0-3","0-5","1-2","1-4","0-6","0-7","0-8","0-10"],M=["0-1","0-2","0-3","0-5","0-6","0-7","0-8","0-9","0-10"],[4,ue("/surprise/v2/surprise/list",{positions:wt?L:M})];case 1:return[2,J.sent()]}})}))];case 2:if(!(u=Ve.sent()))return[2];if(window.__surpriseData=u,f=fe(fe(fe(fe(fe(fe(fe(fe([],(t=__surpriseData["0-1"])!==null&&t!==void 0?t:[],!0),(n=__surpriseData["0-2"])!==null&&n!==void 0?n:[],!0),(r=__surpriseData["0-3"])!==null&&r!==void 0?r:[],!0),(a=__surpriseData["0-5"])!==null&&a!==void 0?a:[],!0),(o=__surpriseData["0-6"])!==null&&o!==void 0?o:[],!0),(s=__surpriseData["0-7"])!==null&&s!==void 0?s:[],!0),(c=__surpriseData["0-8"])!==null&&c!==void 0?c:[],!0),(l=__surpriseData["0-10"])!==null&&l!==void 0?l:[],!0).map(L=>{var M={};return Object.entries(L).forEach(J=>{var ee=J[0],ce=J[1];ee!=="ad_data"&&M[ee]===void 0?M[ee]=ce:Object.keys(L[ee]).forEach(te=>{M[te]=L[ee][te]})}),M}),(h=f.filter(L=>L.position==="0-5"))&&h.length>0&&(v=Xi(__surpriseData["0-5"]))&&d.push(v),y=document.querySelector("#amz-header"),k=document.createElement("div"),m=document.createElement("div"),g=document.createElement("div"),w=document.createElement("div"),E=document.createElement("div"),z=document.createElement("div"),T=document.querySelector(".amz-header-nav-list"),!(y&&k&&m&&g&&w&&E&&z&&T))throw Error("\u{1F916} \u7F3A\u5C11DOM\u5143\u7D20");for(k.classList.add("amz-activity-config-one"),m.classList.add("amz-activity-config-two"),g.classList.add("amz-activity-config-three"),w.classList.add("amz-one-small"),E.classList.add("amz-two-small"),z.classList.add("amz-three-small"),O=f.filter(L=>L.position==="0-7"),q=f.filter(L=>L.position==="0-8"),D=f.filter(L=>L.position==="0-10"),A=document.createElement("div"),P=document.createElement("div"),B=null,A.classList.add("one-activity-style"),P.classList.add("one-activity-down"),(R=document.createElement("div")).classList.add("two-activity-style"),(I=document.createElement("div")).classList.add("one-activity-down"),Y=["https://www.amz123.com/fuwu","https://www.amz123.com/haiwaicang","https://www.amz123.com/pingtai","https://www.amz123.com/wuliu","http://amz123.cc/fuwu"],U=window.location.href,G=O.filter(L=>{var M;return!Y.some(J=>U.startsWith(J))&&!(!((M=L==null?void 0:L.disable_path)===null||M===void 0)&&M.includes(U))&&(L.path.length===0||L.path.includes(U))}),oe=()=>{It(),P.removeEventListener("mouseover",b),P.removeEventListener("mouseout",S),A.classList.remove("one-activity-style-hover"),A.style.display="block",B.style.display="none",rt("show")},K=()=>{It(),I.removeEventListener("mouseover",_),I.removeEventListener("mouseout",x),R.classList.remove("one-activity-style-hover"),R.style.display="block",B.style.display="none",rt("show")},b=()=>{A.classList.contains("one-activity-style-hover")||A.classList.add("one-activity-style-hover"),A.style.display!=="block"&&(A.style.display="block")},S=()=>{A.classList.remove("one-activity-style-hover"),B.style.display!=="none"&&(A.style.display="none")},_=()=>{R.classList.contains("one-activity-style-hover")||R.classList.add("one-activity-style-hover"),R.style.display!=="block"&&(R.style.display="block")},x=()=>{R.classList.remove("one-activity-style-hover"),B.style.display!=="none"&&(R.style.display="none")},(G==null?void 0:G.length)>0&&(G.length===2&&A.classList.add("two-number-activity"),G.length===3&&A.classList.add("three-number-activity"),ve=(Te=G).length===1?Fs(Te):Te.length===2?js(Te):Te.length===3?Us(Te):void 0,A.innerHTML=ve,Hr(G,"seven-close",P,oe),P.appendChild(A),y.appendChild(P)),(q==null?void 0:q.length)>0&&(je=window.location.href,me=q==null?void 0:q.find(L=>{var M;return!Y.some(J=>je.startsWith(J))&&!(!((M=L==null?void 0:L.disable_path)===null||M===void 0)&&M.includes(je))&&(L.path.length===0||L.path.includes(je))}),me&&(ve=(L=>`<div class="two-activity-style-circle">
      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none" class="two-activity-style-circle-color">
        <path d="M5.25 5.25L12.75 12.75" stroke="white" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M5.25 12.75L12.75 5.25" stroke="white" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </div>
    <a 
      href="`.concat(L.url,`" 
      target="_blank"
      rel="nofollow"
      data-sdk-report="1" 
      data-sdk-position="0" 
      data-sdk-index="8" 
      data-sdk-url="`).concat(L.url,`"
      data-sdk-resource-id="`).concat(L.title,`"
      data-sdk-partner-id="`).concat(L.ad_partner_id,`"
    >
      <img class="two-activity-style__image" src="`).concat(L.pic_url,`" alt="">
    </a>`))(me),R.innerHTML=ve,Hr([me],"eight-close",I,K),I.appendChild(R),y.appendChild(I),d.push({resource_position:"0-8",resource_id:me.title,partner_id:String(me.ad_partner_id),resource_url:me.url,path:location.pathname,event_type:"show"}))),(D==null?void 0:D.length)>0&&(tt=window.location.href,me=D==null?void 0:D.find(L=>{var M;return!(!((M=L==null?void 0:L.disable_path)===null||M===void 0)&&M.includes(tt))&&(L.path.length===0||L.path.includes(tt))}),me&&(ve=(L=>`
    <img src="`.concat(L==null?void 0:L.possible_pic_url,'" alt="').concat(L.title,`" class="ten-hover-img"></img>
    <div class="activity-ten-hover">
      <a 
        href="`).concat(L.url,`" 
        target="_blank"
        rel="nofollow"
        class="activity-ten-title"
        data-sdk-report="1" 
        data-sdk-position="0" 
        data-sdk-index="10" 
        data-sdk-url="`).concat(L.url,`"
        data-sdk-resource-id="`).concat(L.title,`"
        data-sdk-partner-id="`).concat(L.ad_partner_id,`"
      >
        <span>`).concat(L.title,`</span>
      </a>
      <div class="activity-divide-ten"></div>
      <a 
        href="`).concat(L.url,`" 
        target="_blank"
        rel="nofollow"
        data-sdk-report="1" 
        data-sdk-position="0" 
        data-sdk-index="10" 
        data-sdk-url="`).concat(L.url,`"
        data-sdk-resource-id="`).concat(L.title,`"
        data-sdk-partner-id="`).concat(L.ad_partner_id,`"
        class="activity-ten-image"
      >
        <img class="activity-ten-image" src="`).concat(L.pic_url,'" alt="').concat(L.title,`">
      </a>
    </div>`))(me),_e=document.getElementById("amz-aside-bar"),(Ne=document.createElement("div")).innerHTML=ve,Ne.className="amz-activity-ten",_e==null||_e.appendChild(Ne))),y.appendChild(k),y.appendChild(m),y.appendChild(g),y.appendChild(w),y.appendChild(E),y.appendChild(z),(Ue=document.querySelector("span.amz-tool-feedback"))&&(Ue.addEventListener("click",()=>{var L;(L=document.querySelector("#amz-header"))===null||L===void 0||L.scrollIntoView({behavior:"smooth"})}),vt=()=>{document.documentElement.scrollTop>=150?Ue.classList.remove("amz-hidden"):Ue.classList.add("amz-hidden")},window.addEventListener("scroll",vt)),H=f.filter(L=>L.position==="0-1"),$=f.filter(L=>L.position==="0-2"),X=f.filter(L=>L.position==="0-3"),pe=f.filter(L=>L.position==="0-6"),re=pe.filter(L=>{var M;return!(!((M=L==null?void 0:L.disable_path)===null||M===void 0)&&M.includes(U))&&(L.path.length===0||L.path.includes(U))}),re.length&&(ie=re[0].url,se='<a id="head-surprise" href="'.concat(ie,'" target="_blank" rel="nofollow" style="left: ').concat(T.getBoundingClientRect().right+20,'px;"><img src="').concat(re[0].pic_url,'" /></a>'),y==null||y.insertAdjacentHTML("beforeend",se),(Ce=document.querySelector("#head-surprise"))&&(window.addEventListener("resize",()=>{Ce.setAttribute("style","left: ".concat(T.getBoundingClientRect().right+20,"px;"))}),d.push({resource_position:"0-6",resource_id:re[0].title,partner_id:String(re[0].ad_partner_id),resource_url:ie,path:location.pathname,event_type:"show"}),Ce.addEventListener("click",()=>{globalThis.__batchReport__([{resource_position:"0-6",resource_id:re[0].title,partner_id:String(re[0].ad_partner_id),resource_url:ie,path:location.pathname,event_type:"click"}],!0)}))),we="activityListString",Me="isShowLarge",hn=fe(fe(fe(fe(fe([],H,!0),$,!0),X,!0),G,!0),q,!0).map(L=>L.title).join("_"),pr=jr(xt,we),Le=pr!==hn||jr(xt,Me,!0),p=Le,fr=()=>{if(H.length){for(var L=H[0],M=L.url,J=L.live_time,ee=`
      `.concat(+(L==null?void 0:L.live_time)>new Date().valueOf()/1e3?`<div class="amz-one-timeout"><span class="amz-timeout-text">\u6D3B\u52A8\u65F6\u95F4</span>
      <span class="amz-timeout-day">`.concat(Rt(J),`\u5929</span>
      <span class="amz-timeout-num">`).concat($t(J),`</span>
      <span class="amz-timeout-text"> :</span>
      <span class="amz-timeout-num">`).concat(Bt(J),`</span>
      <span class="amz-timeout-text">:</span>
      <span class="amz-timeout-num">`).concat(Ft(J),"</span></div>"):"",`
      <div class="amz-activity-one-content">
      <a class="amz-activity-one-img" href="`).concat(M,`" target="_blank"
        ><img
          src="`).concat(L.pic_url,`"
          alt=""
      /></a>
      <div class="amz-activity-one-title">
        <a class="amz-activity-one-name" href="`).concat(M,'" target="_blank" data-partner-id="').concat(L.ad_partner_id,`"
          >`).concat(L.title,`</a
        ><a class="amz-activity-one-btn" href="`).concat(M,`" target="_blank"
          >\u7ACB\u5373\u9884\u7EA6</a
        >
      </div>
    </div>`),ce=1;ce<H.length;ce++){var te=H[ce],xe=te==null?void 0:te.url;ee+=`<div class="amz-activity-one-content other">
    <a
      class="amz-activity-one-img amz-one-img-thumb"
      href="`.concat(xe,`"
      target="_blank"
      ><img
        src="`).concat(te.pic_url,`"
        alt=""
        srcset=""
    /></a>
    <div class="amz-activity-one-title amz-one-title-thumb">
      <a class="amz-activity-one-name" href="`).concat(xe,'" target="_blank" data-partner-id="').concat(te.ad_partner_id,`"
        >`).concat(te.title,`</a
      >
      <p>`).concat(Cn(te.live_time),`</p>
    </div>
    <a class="amz-one-btn-thumb" href="`).concat(xe,`" target="_blank"
      >\u7ACB\u5373\u9884\u7EA6</a
    >
  </div>`)}return ee}},hr=()=>{if($.length){for(var L=$[0],M=L.live_time,J=L.url,ee=`<div class="amz-activity-two-count">
    <div class="amz-two-count-text">\u6D3B\u52A8\u65F6\u95F4</div>
    <div class="amz-two-timeout">
      <span class="amz-timeout-day">`.concat(Rt(M),`\u5929</span
      ><span class="amz-timeout-num">`).concat($t(M),`</span><span>:</span
      ><span class="amz-timeout-num">`).concat(Bt(M),`</span><span>:</span
      ><span class="amz-timeout-num">`).concat(Ft(M),`</span>
    </div>
  </div><div class="amz-activity-two-content">
  <a class="amz-activity-two-img" href="`).concat(J,`" target="_blank"
    ><img
      src="`).concat(L.pic_url,`"
      alt=""
      srcset=""
  /></a>
  <div class="amz-activity-two-title">
    <a class="amz-activity-two-name" href="`).concat(J,'" target="_blank" data-partner-id="').concat(L.ad_partner_id,'">').concat(L.title,`</a>
    <a class="amz-activity-two-btn" href="`).concat(J,`" target="_blank">
      \u7ACB\u5373\u9884\u7EA6
    </a>
  </div>
</div>`),ce=1;ce<$.length;ce++){var te=$[ce],xe=te.url;ee+=`<div class="amz-activity-two-content other">
      <a
        class="amz-activity-two-img amz-two-img-thumb"
        href="`.concat(xe,`"
        target="_blank"
        ><img
          src="`).concat(te.pic_url,`"
          alt=""
          srcset=""
      /></a>
      <div class="amz-activity-two-title amz-two-title-thumb">
        <a class="amz-activity-two-name" href="`).concat(xe,'" target="_blank" data-partner-id="').concat(te.ad_partner_id,'">').concat(te.title,`</a>
        <p>`).concat(Cn(te.live_time),`</p>
      </div>
      <a class="amz-two-btn-thumb" href="`).concat(xe,`" target="_blank">
        \u7ACB\u5373\u9884\u7EA6
      </a>
    </div>`)}return ee}},vr=()=>{if(X.length){for(var L="",M=0;M<X.length;M++){var J=X[M],ee=J.url;L+=`<div class="amz-three-box">
     <a class="amz-activity-three-head" href="`.concat(ee,'" target="_blank" data-partner-id="').concat(J.ad_partner_id,'">').concat(J.title,`</a>
     <div class="amz-three-time">
     <div class="amz-three-date">`).concat(Cn(J.live_time),`</div>
     <a class="amz-three-btn" href="`).concat(ee,`" target="_blank">\u7ACB\u5373\u9884\u7EA6</a>
     </div>
     </div>`)}return L}},k.innerHTML=`
  <div class="amz-activity-one-head">
      <div>\u8DE8\u5883\u6D3B\u52A8</div>
      <svg
        viewBox="0 0 1024 1024"
        xmlns="http://www.w3.org/2000/svg"
        class="amz-activity-one-icon"
      >
        <path
          fill="currentColor"
          d="M195.2 195.2a64 64 0 0 1 90.496 0L512 421.504 738.304 195.2a64 64 0 0 1 90.496 90.496L602.496 512 828.8 738.304a64 64 0 0 1-90.496 90.496L512 602.496 285.696 828.8a64 64 0 0 1-90.496-90.496L421.504 512 195.2 285.696a64 64 0 0 1 0-90.496z"
        ></path>
      </svg>
    </div>
    <div class="amz-activity-one-overflow">
    `.concat(fr(),`
    </div>
`),w.innerHTML=`<div class="amz-one-small-pd">
<a href="`.concat(H.length?H[0].url:"",'" target="_blank" class="amz-one-small-title" data-sdk-report="1" data-sdk-position="0" data-sdk-index="1" data-sdk-resource-id="').concat(H.length?H[0].title:"",`"
  >`).concat(H.length?H[0].title:"",`</a
><svg
  viewBox="0 0 1024 1024"
  xmlns="http://www.w3.org/2000/svg"
  class="amz-one-small-icon"
>
  <path
    fill="currentColor"
    d="m488.832 344.32-339.84 356.672a32 32 0 0 0 0 44.16l.384.384a29.44 29.44 0 0 0 42.688 0l320-335.872 319.872 335.872a29.44 29.44 0 0 0 42.688 0l.384-.384a32 32 0 0 0 0-44.16L535.168 344.32a32 32 0 0 0-46.336 0z"
  ></path>
</svg>
</div>`),m.innerHTML=`
  <div class="amz-activity-two-hidden">
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      class="amz-activity-two-icon"
    >
      <path
        fill="currentColor"
        d="M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z"
      ></path>
    </svg>
  </div>
  <div class="amz-activity-two-pd">
      `.concat(hr(),`
</div>`),E.innerHTML=`<div class="amz-two-small-pd">
  <a href="`.concat($.length?$[0].url:"",`" target="_blank" class="amz-two-small-title"
    >`).concat($.length?$[0].title:"",`</a
  ><svg
    viewBox="0 0 1024 1024"
    xmlns="http://www.w3.org/2000/svg"
    class="amz-two-small-icon"
  >
    <path
      fill="currentColor"
      d="m488.832 344.32-339.84 356.672a32 32 0 0 0 0 44.16l.384.384a29.44 29.44 0 0 0 42.688 0l320-335.872 319.872 335.872a29.44 29.44 0 0 0 42.688 0l.384-.384a32 32 0 0 0 0-44.16L535.168 344.32a32 32 0 0 0-46.336 0z"
    ></path>
  </svg>
</div>`),g.innerHTML=`
  <svg
    viewBox="0 0 1024 1024"
    xmlns="http://www.w3.org/2000/svg"
    class="amz-activity-three-icon"
    >
    <path
      fill="currentColor"
      d="M195.2 195.2a64 64 0 0 1 90.496 0L512 421.504 738.304 195.2a64 64 0 0 1 90.496 90.496L602.496 512 828.8 738.304a64 64 0 0 1-90.496 90.496L512 602.496 285.696 828.8a64 64 0 0 1-90.496-90.496L421.504 512 195.2 285.696a64 64 0 0 1 0-90.496z"
    ></path></svg
    >
`.concat(vr(),`
<div>
`),z.innerHTML=`<div class="amz-small-scroll">
<p class="amz-small-scroll-title">
`.concat(X.length?X[0].title:"",`
</p>
</div>`),gr=document.querySelector(".amz-activity-two-icon"),yr=document.querySelector(".amz-activity-one-icon"),wr=document.querySelector(".amz-activity-three-icon"),br=document.querySelector(".amz-two-small-icon"),_r=document.querySelector(".amz-one-small-icon"),vn=document.querySelector(".one-activity-style-close"),gn=document.querySelector(".two-activity-style-circle"),H.length&&(yn=document.querySelector(".amz-one-timeout"),He=H[0].live_time,wn=0,Dt=()=>{yn.innerHTML=`<span class="amz-timeout-text">\u6D3B\u52A8\u65F6\u95F4</span>
        <span class="amz-timeout-day">`.concat(Rt(He),`\u5929</span>
        <span class="amz-timeout-num">`).concat($t(He),`</span>
        <span class="amz-timeout-text"> :</span>
        <span class="amz-timeout-num">`).concat(Bt(He),`</span>
        <span class="amz-timeout-text">:</span>
        <span class="amz-timeout-num">`).concat(Ft(He),"</span>"),He<=new Date().valueOf()/1e3&&(clearInterval(wn),yn.innerHTML="")},He>new Date().valueOf()/1e3&&(wn=setInterval(Dt,1e3))),$.length&&(gt=document.querySelector(".amz-two-timeout"),We=$[0].live_time,bn=0,Dt=()=>{gt==null||(gt.innerHTML='<span class="amz-timeout-day">'.concat(Rt(We),`\u5929</span
        ><span class="amz-timeout-num">`).concat($t(We),`</span><span>:</span
        ><span class="amz-timeout-num">`).concat(Bt(We),`</span><span>:</span
        ><span class="amz-timeout-num">`).concat(Ft(We),"</span>")),We<=new Date().valueOf()/1e3&&(clearInterval(bn),gt==null||(gt.innerHTML=""))},We>new Date().valueOf()/1e3&&(bn=setInterval(Dt,1e3))),It=()=>{Ur(xt,we),Ur(xt,Me)},nt=()=>{((L,M,J)=>{var ee,ce=or(L);if(ce)ce[M]=J,Un(L,ce);else{var te=((ee={})[M]=J,ee);Un(L,te)}})(xt,we,hn)},rt=L=>{document.getElementById("amz-aside-bar")},yr.onclick=()=>{nt(),k.style.display="none",w.style.display="flex"},gr.onclick=()=>{nt(),m.style.display="none",E.style.display="flex"},wr.onclick=()=>{nt(),g.style.display="none",z.style.display="block"},vn==null||(vn.onclick=()=>{nt(),A.style.display="none",B.style.display="flex",rt("hidden"),P.addEventListener("mouseover",b),P.addEventListener("mouseout",S)}),gn==null||(gn.onclick=()=>{nt(),R.style.display="none",B.style.display="flex",I.addEventListener("mouseover",_),I.addEventListener("mouseout",x),rt("hidden")}),yt=[[_r,k,w],[br,m,E],[z,g,z]],xr=L=>{yt[L][0].onclick=()=>{It(),yt[L][1].style.display="block",yt[L][2].style.display="none"}},ge=0;ge<yt.length;ge++)xr(ge);for(Ye=[[H,k,w],[$,m,E],[X,g,z]],ge=0;ge<Ye.length;ge++)Ye[ge][0].length?(Ye[ge][1].style.display=Le?"block":"none",Ye[ge][2].style.display=Le?"none":"block"):(Ye[ge][1].style.display="none",Ye[ge][2].style.display="none");return(B=document.querySelector(".one-activity-down-box")).style.display=Le?"none":"flex",A.style.display=Le?"block":"none",Le||(P.addEventListener("mouseover",b),P.addEventListener("mouseout",S),I.addEventListener("mouseout",x),I.addEventListener("mouseover",_)),R.style.display=Le?"block":"none",rt(Le?"show":"hidden"),[3,5];case 3:return Ve.sent(),[3,5];case 4:return _n=!p,i(".amz-activity-one-content",".amz-activity-one-name","0-1"),i(".amz-activity-two-content",".amz-activity-two-name","0-2"),i(".amz-three-box",".amz-activity-three-head","0-3"),i(".one-activity-style-content",".one-activity-style-context","0-7"),d.length>0&&globalThis.__batchReport__(d),[7];case 5:return[2]}var Te,wt})})}var Ws=e=>{try{ue("/statistics/v1/search/record",e,{headers:{ignoreError:!0}})}catch{}},Ys=function(){return F(void 0,void 0,void 0,function(){var e,t,n,r,a,o,s;return j(this,c=>(e=(s=location.pathname.split("/"))===null||s===void 0?void 0:s.length,t=location.href,(n=location.pathname.split("/")[1]).includes("tools")?(r=document.querySelector(".amz-header-nav-list .amz-nav-title.amz-toolbox"))==null||r.classList.add("active-after"):(a=document.querySelectorAll(".amz-header-nav-list .amz-nav-title"),o=[],a.forEach(l=>{var i=l.innerText,d=l.querySelectorAll("[href]");if(!l.classList.toString().includes("arrow-control")){var p=Array.from(d).map(f=>f.href.includes("apphptovtou")?"apphptovtou":f.href.split("/")[3]),u=Array.from(d).map(f=>(o.push(f.href),f.href));if(p.includes(n)){if(i==="\u751F\u6001\u5C55")return void(u[0]===t&&l.classList.add("active-after"));if(i==="\u6D3B\u52A8")return;l.classList.add("active-after")}}}),a.forEach(l=>{if(l.innerText==="\u6D3B\u52A8"){if(n==="activity"&&e===2)return void l.classList.add("active-after");n==="activity"&&e===3&&(o.includes(t)||l.classList.add("active-after"))}})),[2]))})},Vs=()=>{try{Js(),Gs()}catch{}},Wr=e=>{e.preventDefault()},Zt=e=>{e?(document.body.style.overflow="hidden",document.addEventListener("touchmove",Wr,{passive:!1})):(document.body.style.overflow="scroll",document.removeEventListener("touchmove",Wr))},Js=()=>{var e=document.querySelector(".amz-mobile-drawer"),t=document.querySelector(".amz-mobile-mask"),n=document.querySelector(".amz-mobile-menu-icon"),r=document.querySelector(".amz-mobile-close-icon"),a=document.querySelectorAll(".amz-mobile-drawer .drawer-menu-list section"),o=document.querySelector(".amz-mobile-drawer .amz-header-modal .amz-modal-close"),s=document.querySelector(".amz-mobile-drawer .drawer-menu-list .matrix"),c=document.querySelector(".amz-mobile-drawer .amz-header-modal"),l=()=>{e.className="amz-mobile-drawer",t.className="amz-mobile-mask",n.setAttribute("style","display:block"),r.setAttribute("style","display:none"),Zt(!1)};n.addEventListener("click",()=>{e.className="amz-mobile-drawer active",t.className="amz-mobile-mask drawer",n.setAttribute("style","display:none"),r.setAttribute("style","display:block;width: 22px;height: 22px;"),Zt(!0)}),r.addEventListener("click",()=>{l()}),a.forEach(i=>{i.className.includes("matrix")||(i.querySelector("span").addEventListener("click",()=>{window.open(i.getAttribute("data-url")||"","_blank")}),i.className.includes("expand-mark")&&i.querySelector(".arrow-icon").addEventListener("click",d=>{d.stopPropagation(),i.className==="expand-mark"||i.className==="expand-mark hide"?(a.forEach(p=>{p.classList.contains("active")&&p.classList.replace("active","hide")}),i.className="expand-mark active"):i.className==="expand-mark active"&&(i.className="expand-mark hide")}))}),s==null||s.addEventListener("click",()=>{var i=document.querySelector(".amz-modal-body"),d=i==null?void 0:i.querySelectorAll("img");d&&d.forEach(p=>{var u=p.getAttribute("data-load-img");u&&(p.src=u,p.setAttribute("data-load-img",""))}),c.className="amz-header-modal active"}),o==null||o.addEventListener("click",()=>{c.className="amz-header-modal"}),t==null||t.addEventListener("click",()=>{t.className==="amz-mobile-mask drawer"&&l()})},Gs=()=>{var e=document.querySelector(".amz-mobile-drawer"),t=document.querySelector(".amz-mobile-mask"),n=document.querySelector(".amz-mobile-menu-icon"),r=document.querySelector(".amz-mobile-close-icon");try{var a=document.querySelector(".amz-mobile-search-modal"),o=document.querySelector(".amz-mobile-search-close"),s=document.querySelector(".amz-mobile-search-modal .amz-search-form .amz-hot-search"),c=document.querySelector(".amz-mobile-search-modal .amz-search-form .amz-search-input input"),l=document.querySelector(".amz-mobile-search-icon"),i=document.querySelector(".amz-search-input svg");l==null||l.addEventListener("click",()=>{e.className==="amz-mobile-drawer active"&&(e.className="amz-mobile-drawer",t.className="amz-mobile-mask",n.setAttribute("style","display:block"),r.setAttribute("style","display:none")),a.className="amz-mobile-search-modal active",t.className="amz-mobile-mask search",c.focus(),Zt(!0)}),o==null||o.addEventListener("click",()=>{a.className="amz-mobile-search-modal",t.className="amz-mobile-mask",Zt(!1)}),c.oninput=()=>{c.value.trim()===""?(s.className="amz-hot-search active",i.setAttribute("style","opacity: 0.8;")):(s.className="amz-hot-search",i.setAttribute("style","opacity: 1;"))},c==null||c.addEventListener("keyup",d=>{d.key==="Enter"&&(c.value.trim().length!==0?window.open("","_blank"):window.open("/search?keyword=".concat(c.placeholder),"_blank"))}),i==null||i.addEventListener("click",()=>{var d=c.value.trim();d.length!==0?window.open("/search?keyword=".concat(d),"_blank"):window.open("/search?keyword=".concat(c.placeholder),"_blank")})}catch{}},Xs=function(e){return F(void 0,void 0,void 0,function(){var t,n,r,a,o,s;return j(this,c=>{switch(c.label){case 0:if(c.trys.push([0,8,,9]),!Ga())return[3,7];if(l=window.location.search.slice(1).split("&"),i={},l.forEach(d=>{var p=d.split("="),u=p[0],f=p[1];i[u]=decodeURIComponent(f)}),(t=i).code===void 0||t.is_login!=="true")return[3,7];c.label=1;case 1:return c.trys.push([1,6,,7]),n=e.refresh,r=n===void 0||n,a=e.callback,[4,ks(t.code)];case 2:return(o=c.sent()).status&&o.status===301?((()=>{var d,p,u=((d=globalThis==null?void 0:globalThis.__navApp__)===null||d===void 0?void 0:d.appId)||3,f=`  <div class="amz-header-login-modal">
  <div
    class="animate-open"
    style="
      width: 408px;
      padding: 56px 42px;
      position: relative;
      display: flex;
      flex-direction: column;
      background: rgb(255, 255, 255);
      border-radius: 12px;
      box-sizing: border-box;
    "
  >
    <div class="amz-login-modal-body">
      <div class="amz-login-form">
        <div class="amz-login-form-options">
          <span class="amz-login-form-close antialiased">
          <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M9.3335 9.33333L22.6668 22.6667"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M9.3335 22.6667L22.6668 9.33333"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
  
          </span>
        </div>
        <div class="amz-login-logo">
          <span class="amz-success-tip" style="opacity: 0"></span>
          <img src="`.concat(tr[u],`"alt="logo">
          <span>`).concat(nr[u],`</span>
        </div>

        <div class="amz-wx-bind-container">
            <div class="amz-wx-bind-input">
              <div class="amz-wx-bind-id">
                <input class="amz-wx-bind-phone-input" placeholder="\u624B\u673A\u53F7" />
                <span class="amz-wx-bind-input-tip amz-wx-bind-id-tip" style="opacity: 0"></span>
              </div>
              <div class="amz-wx-bind-password">
                <input class="amz-wx-bind-code-input" maxlength="6" type="text" placeholder="\u9A8C\u8BC1\u7801" />
                <div class="amz-wx-bind-get-code">
                  <div class="amz-wx-bind-get-code-inner">\u83B7\u53D6\u9A8C\u8BC1\u7801</div>
                  <div class="amz-wx-bind-count-down" style="display: none">
                    \u53D1\u9001\u6210\u529F
                    <span id="countdown" class="inline-block min-w-[16px]">60</span>
                    S
                  </div>
                </div>
                <span class="amz-wx-bind-input-tip amz-wx-bind-code-tip" style="opacity: 0"></span>
              </div>
            </div>
            <div class="amz-wx-bind-btn">
              <button id="amz-wx-bind-on-submit">\u7ED1\u5B9A\u624B\u673A\u53F7</button>
            </div>
            <div class="amz-wx-back-login">
              <div id="amz-wx-on-back-login">\u8FD4\u56DE\u767B\u5F55</div>
            </div>
        </div>
      </div>
    </div>
  </div>
</div>`);(p=document.body)===null||p===void 0||p.insertAdjacentHTML("afterend",f)})(),Ns({refresh:r,code:t.code,callback:a}),[3,5]):[3,3];case 3:return Ja(),rr(o),[4,(s=e.callback)===null||s===void 0?void 0:s.call(e)];case 4:c.sent(),r&&window.location.reload(),c.label=5;case 5:return[3,7];case 6:return c.sent(),[3,7];case 7:return[3,9];case 8:return c.sent(),[3,9];case 9:return[2]}var l,i})})},Zs=()=>{document.querySelectorAll(".arrow-control").forEach(e=>{e.addEventListener("mouseenter",()=>{e.querySelectorAll("img").forEach(t=>{var n=t.getAttribute("data-load-img");n&&(t.src=n,t.setAttribute("data-load-img",""))})})})};try{Ys()}catch{}function Be(e){if(e===null||e===!0||e===!1)return NaN;var t=Number(e);return isNaN(t)?t:t<0?Math.ceil(t):Math.floor(t)}function de(e,t){if(t.length<e)throw new TypeError(e+" argument"+(e>1?"s":"")+" required, but only "+t.length+" present")}function ke(e){de(1,arguments);var t=Object.prototype.toString.call(e);return e instanceof Date||typeof e=="object"&&t==="[object Date]"?new Date(e.getTime()):typeof e=="number"||t==="[object Number]"?new Date(e):((typeof e=="string"||t==="[object String]")&&typeof console<"u"&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(new Error().stack)),new Date(NaN))}function Ka(e,t){de(2,arguments);var n=ke(e).getTime(),r=Be(t);return new Date(n+r)}var Qs=36e5;function Ks(e,t){de(2,arguments);var n=Be(t);return Ka(e,n*Qs)}var ec={};function cn(){return ec}function tc(e){var t=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return t.setUTCFullYear(e.getFullYear()),e.getTime()-t.getTime()}function nc(e){return de(1,arguments),e instanceof Date||typeof e=="object"&&Object.prototype.toString.call(e)==="[object Date]"}function rc(e){if(de(1,arguments),!nc(e)&&typeof e!="number")return!1;var t=ke(e);return!isNaN(Number(t))}function ac(e,t){de(2,arguments);var n=Be(t);return Ka(e,-n)}var oc=864e5;function ic(e){de(1,arguments);var t=ke(e),n=t.getTime();t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0);var r=t.getTime(),a=n-r;return Math.floor(a/oc)+1}function Qt(e){de(1,arguments);var t=1,n=ke(e),r=n.getUTCDay(),a=(r<t?7:0)+r-t;return n.setUTCDate(n.getUTCDate()-a),n.setUTCHours(0,0,0,0),n}function eo(e){de(1,arguments);var t=ke(e),n=t.getUTCFullYear(),r=new Date(0);r.setUTCFullYear(n+1,0,4),r.setUTCHours(0,0,0,0);var a=Qt(r),o=new Date(0);o.setUTCFullYear(n,0,4),o.setUTCHours(0,0,0,0);var s=Qt(o);return t.getTime()>=a.getTime()?n+1:t.getTime()>=s.getTime()?n:n-1}function sc(e){de(1,arguments);var t=eo(e),n=new Date(0);n.setUTCFullYear(t,0,4),n.setUTCHours(0,0,0,0);var r=Qt(n);return r}var cc=6048e5;function lc(e){de(1,arguments);var t=ke(e),n=Qt(t).getTime()-sc(t).getTime();return Math.round(n/cc)+1}function Kt(e,t){var n,r,a,o,s,c,l,i;de(1,arguments);var d=cn(),p=Be((n=(r=(a=(o=t==null?void 0:t.weekStartsOn)!==null&&o!==void 0?o:t==null||(s=t.locale)===null||s===void 0||(c=s.options)===null||c===void 0?void 0:c.weekStartsOn)!==null&&a!==void 0?a:d.weekStartsOn)!==null&&r!==void 0?r:(l=d.locale)===null||l===void 0||(i=l.options)===null||i===void 0?void 0:i.weekStartsOn)!==null&&n!==void 0?n:0);if(!(p>=0&&p<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var u=ke(e),f=u.getUTCDay(),h=(f<p?7:0)+f-p;return u.setUTCDate(u.getUTCDate()-h),u.setUTCHours(0,0,0,0),u}function to(e,t){var n,r,a,o,s,c,l,i;de(1,arguments);var d=ke(e),p=d.getUTCFullYear(),u=cn(),f=Be((n=(r=(a=(o=t==null?void 0:t.firstWeekContainsDate)!==null&&o!==void 0?o:t==null||(s=t.locale)===null||s===void 0||(c=s.options)===null||c===void 0?void 0:c.firstWeekContainsDate)!==null&&a!==void 0?a:u.firstWeekContainsDate)!==null&&r!==void 0?r:(l=u.locale)===null||l===void 0||(i=l.options)===null||i===void 0?void 0:i.firstWeekContainsDate)!==null&&n!==void 0?n:1);if(!(f>=1&&f<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var h=new Date(0);h.setUTCFullYear(p+1,0,f),h.setUTCHours(0,0,0,0);var v=Kt(h,t),y=new Date(0);y.setUTCFullYear(p,0,f),y.setUTCHours(0,0,0,0);var k=Kt(y,t);return d.getTime()>=v.getTime()?p+1:d.getTime()>=k.getTime()?p:p-1}function dc(e,t){var n,r,a,o,s,c,l,i;de(1,arguments);var d=cn(),p=Be((n=(r=(a=(o=t==null?void 0:t.firstWeekContainsDate)!==null&&o!==void 0?o:t==null||(s=t.locale)===null||s===void 0||(c=s.options)===null||c===void 0?void 0:c.firstWeekContainsDate)!==null&&a!==void 0?a:d.firstWeekContainsDate)!==null&&r!==void 0?r:(l=d.locale)===null||l===void 0||(i=l.options)===null||i===void 0?void 0:i.firstWeekContainsDate)!==null&&n!==void 0?n:1),u=to(e,t),f=new Date(0);f.setUTCFullYear(u,0,p),f.setUTCHours(0,0,0,0);var h=Kt(f,t);return h}var uc=6048e5;function mc(e,t){de(1,arguments);var n=ke(e),r=Kt(n,t).getTime()-dc(n,t).getTime();return Math.round(r/uc)+1}function V(e,t){for(var n=e<0?"-":"",r=Math.abs(e).toString();r.length<t;)r="0"+r;return n+r}var pc={y:function(e,t){var n=e.getUTCFullYear(),r=n>0?n:1-n;return V(t==="yy"?r%100:r,t.length)},M:function(e,t){var n=e.getUTCMonth();return t==="M"?String(n+1):V(n+1,2)},d:function(e,t){return V(e.getUTCDate(),t.length)},a:function(e,t){var n=e.getUTCHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];case"aaaa":default:return n==="am"?"a.m.":"p.m."}},h:function(e,t){return V(e.getUTCHours()%12||12,t.length)},H:function(e,t){return V(e.getUTCHours(),t.length)},m:function(e,t){return V(e.getUTCMinutes(),t.length)},s:function(e,t){return V(e.getUTCSeconds(),t.length)},S:function(e,t){var n=t.length,r=e.getUTCMilliseconds(),a=Math.floor(r*Math.pow(10,n-3));return V(a,t.length)}};const Ie=pc;var at={am:"am",pm:"pm",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},fc={G:function(e,t,n){var r=e.getUTCFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});case"GGGG":default:return n.era(r,{width:"wide"})}},y:function(e,t,n){if(t==="yo"){var r=e.getUTCFullYear(),a=r>0?r:1-r;return n.ordinalNumber(a,{unit:"year"})}return Ie.y(e,t)},Y:function(e,t,n,r){var a=to(e,r),o=a>0?a:1-a;if(t==="YY"){var s=o%100;return V(s,2)}return t==="Yo"?n.ordinalNumber(o,{unit:"year"}):V(o,t.length)},R:function(e,t){var n=eo(e);return V(n,t.length)},u:function(e,t){var n=e.getUTCFullYear();return V(n,t.length)},Q:function(e,t,n){var r=Math.ceil((e.getUTCMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return V(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(e,t,n){var r=Math.ceil((e.getUTCMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return V(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(e,t,n){var r=e.getUTCMonth();switch(t){case"M":case"MM":return Ie.M(e,t);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(e,t,n){var r=e.getUTCMonth();switch(t){case"L":return String(r+1);case"LL":return V(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(e,t,n,r){var a=mc(e,r);return t==="wo"?n.ordinalNumber(a,{unit:"week"}):V(a,t.length)},I:function(e,t,n){var r=lc(e);return t==="Io"?n.ordinalNumber(r,{unit:"week"}):V(r,t.length)},d:function(e,t,n){return t==="do"?n.ordinalNumber(e.getUTCDate(),{unit:"date"}):Ie.d(e,t)},D:function(e,t,n){var r=ic(e);return t==="Do"?n.ordinalNumber(r,{unit:"dayOfYear"}):V(r,t.length)},E:function(e,t,n){var r=e.getUTCDay();switch(t){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});case"EEEE":default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(e,t,n,r){var a=e.getUTCDay(),o=(a-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(o);case"ee":return V(o,2);case"eo":return n.ordinalNumber(o,{unit:"day"});case"eee":return n.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(a,{width:"short",context:"formatting"});case"eeee":default:return n.day(a,{width:"wide",context:"formatting"})}},c:function(e,t,n,r){var a=e.getUTCDay(),o=(a-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(o);case"cc":return V(o,t.length);case"co":return n.ordinalNumber(o,{unit:"day"});case"ccc":return n.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(a,{width:"narrow",context:"standalone"});case"cccccc":return n.day(a,{width:"short",context:"standalone"});case"cccc":default:return n.day(a,{width:"wide",context:"standalone"})}},i:function(e,t,n){var r=e.getUTCDay(),a=r===0?7:r;switch(t){case"i":return String(a);case"ii":return V(a,t.length);case"io":return n.ordinalNumber(a,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});case"iiii":default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(e,t,n){var r=e.getUTCHours(),a=r/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(a,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(e,t,n){var r=e.getUTCHours(),a;switch(r===12?a=at.noon:r===0?a=at.midnight:a=r/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(a,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(e,t,n){var r=e.getUTCHours(),a;switch(r>=17?a=at.evening:r>=12?a=at.afternoon:r>=4?a=at.morning:a=at.night,t){case"B":case"BB":case"BBB":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(a,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(e,t,n){if(t==="ho"){var r=e.getUTCHours()%12;return r===0&&(r=12),n.ordinalNumber(r,{unit:"hour"})}return Ie.h(e,t)},H:function(e,t,n){return t==="Ho"?n.ordinalNumber(e.getUTCHours(),{unit:"hour"}):Ie.H(e,t)},K:function(e,t,n){var r=e.getUTCHours()%12;return t==="Ko"?n.ordinalNumber(r,{unit:"hour"}):V(r,t.length)},k:function(e,t,n){var r=e.getUTCHours();return r===0&&(r=24),t==="ko"?n.ordinalNumber(r,{unit:"hour"}):V(r,t.length)},m:function(e,t,n){return t==="mo"?n.ordinalNumber(e.getUTCMinutes(),{unit:"minute"}):Ie.m(e,t)},s:function(e,t,n){return t==="so"?n.ordinalNumber(e.getUTCSeconds(),{unit:"second"}):Ie.s(e,t)},S:function(e,t){return Ie.S(e,t)},X:function(e,t,n,r){var a=r._originalDate||e,o=a.getTimezoneOffset();if(o===0)return"Z";switch(t){case"X":return Vr(o);case"XXXX":case"XX":return Je(o);case"XXXXX":case"XXX":default:return Je(o,":")}},x:function(e,t,n,r){var a=r._originalDate||e,o=a.getTimezoneOffset();switch(t){case"x":return Vr(o);case"xxxx":case"xx":return Je(o);case"xxxxx":case"xxx":default:return Je(o,":")}},O:function(e,t,n,r){var a=r._originalDate||e,o=a.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+Yr(o,":");case"OOOO":default:return"GMT"+Je(o,":")}},z:function(e,t,n,r){var a=r._originalDate||e,o=a.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+Yr(o,":");case"zzzz":default:return"GMT"+Je(o,":")}},t:function(e,t,n,r){var a=r._originalDate||e,o=Math.floor(a.getTime()/1e3);return V(o,t.length)},T:function(e,t,n,r){var a=r._originalDate||e,o=a.getTime();return V(o,t.length)}};function Yr(e,t){var n=e>0?"-":"+",r=Math.abs(e),a=Math.floor(r/60),o=r%60;if(o===0)return n+String(a);var s=t||"";return n+String(a)+s+V(o,2)}function Vr(e,t){if(e%60===0){var n=e>0?"-":"+";return n+V(Math.abs(e)/60,2)}return Je(e,t)}function Je(e,t){var n=t||"",r=e>0?"-":"+",a=Math.abs(e),o=V(Math.floor(a/60),2),s=V(a%60,2);return r+o+n+s}const hc=fc;var Jr=function(e,t){switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});case"PPPP":default:return t.date({width:"full"})}},no=function(e,t){switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});case"pppp":default:return t.time({width:"full"})}},vc=function(e,t){var n=e.match(/(P+)(p+)?/)||[],r=n[1],a=n[2];if(!a)return Jr(e,t);var o;switch(r){case"P":o=t.dateTime({width:"short"});break;case"PP":o=t.dateTime({width:"medium"});break;case"PPP":o=t.dateTime({width:"long"});break;case"PPPP":default:o=t.dateTime({width:"full"});break}return o.replace("{{date}}",Jr(r,t)).replace("{{time}}",no(a,t))},gc={p:no,P:vc};const yc=gc;var wc=["D","DD"],bc=["YY","YYYY"];function _c(e){return wc.indexOf(e)!==-1}function xc(e){return bc.indexOf(e)!==-1}function Gr(e,t,n){if(e==="YYYY")throw new RangeError("Use `yyyy` instead of `YYYY` (in `".concat(t,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(e==="YY")throw new RangeError("Use `yy` instead of `YY` (in `".concat(t,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(e==="D")throw new RangeError("Use `d` instead of `D` (in `".concat(t,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(e==="DD")throw new RangeError("Use `dd` instead of `DD` (in `".concat(t,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"))}var Sc={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},zc=function(e,t,n){var r,a=Sc[e];return typeof a=="string"?r=a:t===1?r=a.one:r=a.other.replace("{{count}}",t.toString()),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?"in "+r:r+" ago":r};const Ec=zc;function Ln(e){return function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=t.width?String(t.width):e.defaultWidth,r=e.formats[n]||e.formats[e.defaultWidth];return r}}var kc={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},Cc={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},Lc={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Tc={date:Ln({formats:kc,defaultWidth:"full"}),time:Ln({formats:Cc,defaultWidth:"full"}),dateTime:Ln({formats:Lc,defaultWidth:"full"})};const Oc=Tc;var qc={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Pc=function(e,t,n,r){return qc[e]};const Ac=Pc;function St(e){return function(t,n){var r=n!=null&&n.context?String(n.context):"standalone",a;if(r==="formatting"&&e.formattingValues){var o=e.defaultFormattingWidth||e.defaultWidth,s=n!=null&&n.width?String(n.width):o;a=e.formattingValues[s]||e.formattingValues[o]}else{var c=e.defaultWidth,l=n!=null&&n.width?String(n.width):e.defaultWidth;a=e.values[l]||e.values[c]}var i=e.argumentCallback?e.argumentCallback(t):t;return a[i]}}var Nc={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Mc={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},Dc={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},Ic={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},Rc={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},$c={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},Bc=function(e,t){var n=Number(e),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},Fc={ordinalNumber:Bc,era:St({values:Nc,defaultWidth:"wide"}),quarter:St({values:Mc,defaultWidth:"wide",argumentCallback:function(e){return e-1}}),month:St({values:Dc,defaultWidth:"wide"}),day:St({values:Ic,defaultWidth:"wide"}),dayPeriod:St({values:Rc,defaultWidth:"wide",formattingValues:$c,defaultFormattingWidth:"wide"})};const jc=Fc;function zt(e){return function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=n.width,a=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth],o=t.match(a);if(!o)return null;var s=o[0],c=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth],l=Array.isArray(c)?Hc(c,function(p){return p.test(s)}):Uc(c,function(p){return p.test(s)}),i;i=e.valueCallback?e.valueCallback(l):l,i=n.valueCallback?n.valueCallback(i):i;var d=t.slice(s.length);return{value:i,rest:d}}}function Uc(e,t){for(var n in e)if(e.hasOwnProperty(n)&&t(e[n]))return n}function Hc(e,t){for(var n=0;n<e.length;n++)if(t(e[n]))return n}function Wc(e){return function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=t.match(e.matchPattern);if(!r)return null;var a=r[0],o=t.match(e.parsePattern);if(!o)return null;var s=e.valueCallback?e.valueCallback(o[0]):o[0];s=n.valueCallback?n.valueCallback(s):s;var c=t.slice(a.length);return{value:s,rest:c}}}var Yc=/^(\d+)(th|st|nd|rd)?/i,Vc=/\d+/i,Jc={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},Gc={any:[/^b/i,/^(a|c)/i]},Xc={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},Zc={any:[/1/i,/2/i,/3/i,/4/i]},Qc={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},Kc={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},el={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},tl={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},nl={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},rl={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},al={ordinalNumber:Wc({matchPattern:Yc,parsePattern:Vc,valueCallback:function(e){return parseInt(e,10)}}),era:zt({matchPatterns:Jc,defaultMatchWidth:"wide",parsePatterns:Gc,defaultParseWidth:"any"}),quarter:zt({matchPatterns:Xc,defaultMatchWidth:"wide",parsePatterns:Zc,defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:zt({matchPatterns:Qc,defaultMatchWidth:"wide",parsePatterns:Kc,defaultParseWidth:"any"}),day:zt({matchPatterns:el,defaultMatchWidth:"wide",parsePatterns:tl,defaultParseWidth:"any"}),dayPeriod:zt({matchPatterns:nl,defaultMatchWidth:"any",parsePatterns:rl,defaultParseWidth:"any"})};const ol=al;var il={code:"en-US",formatDistance:Ec,formatLong:Oc,formatRelative:Ac,localize:jc,match:ol,options:{weekStartsOn:0,firstWeekContainsDate:1}};const sl=il;var cl=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,ll=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,dl=/^'([^]*?)'?$/,ul=/''/g,ml=/[a-zA-Z]/;function ro(e,t,n){var r,a,o,s,c,l,i,d,p,u,f,h,v,y,k,m,g,w;de(2,arguments);var E=String(t),z=cn(),T=(r=(a=n==null?void 0:n.locale)!==null&&a!==void 0?a:z.locale)!==null&&r!==void 0?r:sl,O=Be((o=(s=(c=(l=n==null?void 0:n.firstWeekContainsDate)!==null&&l!==void 0?l:n==null||(i=n.locale)===null||i===void 0||(d=i.options)===null||d===void 0?void 0:d.firstWeekContainsDate)!==null&&c!==void 0?c:z.firstWeekContainsDate)!==null&&s!==void 0?s:(p=z.locale)===null||p===void 0||(u=p.options)===null||u===void 0?void 0:u.firstWeekContainsDate)!==null&&o!==void 0?o:1);if(!(O>=1&&O<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var q=Be((f=(h=(v=(y=n==null?void 0:n.weekStartsOn)!==null&&y!==void 0?y:n==null||(k=n.locale)===null||k===void 0||(m=k.options)===null||m===void 0?void 0:m.weekStartsOn)!==null&&v!==void 0?v:z.weekStartsOn)!==null&&h!==void 0?h:(g=z.locale)===null||g===void 0||(w=g.options)===null||w===void 0?void 0:w.weekStartsOn)!==null&&f!==void 0?f:0);if(!(q>=0&&q<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");if(!T.localize)throw new RangeError("locale must contain localize property");if(!T.formatLong)throw new RangeError("locale must contain formatLong property");var D=ke(e);if(!rc(D))throw new RangeError("Invalid time value");var A=tc(D),P=ac(D,A),B={firstWeekContainsDate:O,weekStartsOn:q,locale:T,_originalDate:D},R=E.match(ll).map(function(I){var Y=I[0];if(Y==="p"||Y==="P"){var U=yc[Y];return U(I,T.formatLong)}return I}).join("").match(cl).map(function(I){if(I==="''")return"'";var Y=I[0];if(Y==="'")return pl(I);var U=hc[Y];if(U)return!(n!=null&&n.useAdditionalWeekYearTokens)&&xc(I)&&Gr(I,t,String(e)),!(n!=null&&n.useAdditionalDayOfYearTokens)&&_c(I)&&Gr(I,t,String(e)),U(P,I,T.localize,B);if(Y.match(ml))throw new RangeError("Format string contains an unescaped latin alphabet character `"+Y+"`");return I}).join("");return R}function pl(e){var t=e.match(dl);return t?t[1].replace(ul,"'"):e}const N=(e,...t)=>{localStorage.getItem("debug-mode")},fl=(e=1)=>new Promise(t=>setTimeout(t,e*1e3));window.addEventListener("DOMContentLoaded",()=>{try{var e=e||[],t=document.createElement("script");const r=location.href;let a="https://hm.baidu.com/hm.js?b276b2f97e0eb35a0347f6faeec9c1db";r.startsWith("https://www.tt123.com")&&(a="https://hm.baidu.com/hm.js?f0f478adbffb1b0a2b54cb29b4ea617a"),r.startsWith("https://www.brandark.com")&&(a="https://hm.baidu.com/hm.js?b9f8332c9d40c84800a516808b84e95a"),r.startsWith("https://www.dny123.com")&&(a="https://hm.baidu.com/hm.js?cb03ddeb995e6850f251b4b6d3590004"),t.src=a;var n=document.getElementsByTagName("script")[0];n.parentNode.insertBefore(t,n)}catch(r){N("\u767E\u5EA6\u7EDF\u8BA1sdk\u521D\u59CB\u5316\u5931\u8D25\uFF1A",r)}});const en=(e,t=!1)=>{setTimeout(()=>{e&&(e.dataset.opacity="2",e.querySelectorAll('*[data-opacity="1"]').forEach(n=>{n.dataset.opacity="2"}))},t?0:100)},Xr=(e,t)=>{let n=new Date;n.setHours(0),n.setMinutes(0),n.setSeconds(0),n.setMilliseconds(0);const r=n.getTime(),a=new Date(`${e} 00:00:00`.replace(/-/g,"/")),o=Math.ceil(Math.abs(r.valueOf()-a.valueOf())/(1e3*60*60*24));return r.valueOf()>a.valueOf()?-o:o},hl=()=>{try{const e=document.querySelectorAll("#amz-timezone .amz-timezone-item span[data-tz-offset]");if(e.length>0){const n=()=>{e.forEach(r=>{const a=r.parentElement.dataset.summer==="true"?1:0,o=~~r.dataset.tzOffset+a;r.textContent=ro(Ks(new Date,o),"MM-dd HH:mm:ss")})};setInterval(n,1*1e3),n(),en(document.querySelector("#amz-timezone"),!0)}const t=document.querySelectorAll(".amz-holiday .holiday\u2014countdown .day[data-date]");if(t.length>0){const n=()=>{t.forEach(u=>{const f=u.dataset.date;u.textContent=Xr(f,!0)});const r=document.getElementById("amz-current-year-info");if(r){var a=new Date,o=new Date(a.getFullYear(),0,1),s=o.getDay(),c=1;s!==0&&(c=7-s+1),o=new Date(a.getFullYear(),0,1+c);var l=Math.ceil((a.valueOf()-o.valueOf())/864e5),i=Math.ceil(l/7),d=i+1,p=Xr(`${a.getFullYear()+1}-01-01`,!0);r.innerHTML=`${a.getFullYear()} \u5E74\u7B2C <span style='color:#ff4300'>${d}</span> \u5468\uFF0C\u8DDD${a.getFullYear()+1}\u5E74\u8FD8\u6709 <span style='color:#ff4300'>${p}</span> \u5929`}};n(),en(document.querySelector(".amz-holiday"),!0),setInterval(n,1e3)}}catch(e){N("\u521D\u59CB\u5316\u65E5\u671F\u5012\u8BA1\u65F6\u4FE1\u606F\u5931\u8D25\uFF1A",e)}};window.addEventListener("DOMContentLoaded",hl);window.addEventListener("DOMContentLoaded",()=>{let e=!1;const t=document.querySelector("#amz-peg-menu"),n=(u=0)=>{t==null||t.setAttribute("style",`--amz-peg-menu-offset: ${u}px;`)};n();const r=document.querySelectorAll("#amz-peg-menu>li"),a=document.querySelectorAll("#amz-peg-menu>li>div>span"),o=()=>document.querySelector("#amz-peg-menu>li[data-focus='true']"),s=u=>{a.forEach(f=>{f.style.color="#5e5d5b"}),u.querySelector("span").style.color="#ff5a00"};r.forEach(u=>{u.addEventListener("click",()=>{e=!0;const f=document.getElementById(u.dataset.id),h=u.getAttribute("data-anchor-status"),v=u.getAttribute("data-anchor-name");if(h&&h==="1")window.location.hash=v;else{const g=window.location.href.split("#")[0];window.history.replaceState({},document.title,g)}f.scrollIntoView({behavior:"smooth"});const{y}=o().getBoundingClientRect(),{y:k}=u.getBoundingClientRect(),m=k-y;n(m),s(u)})});const c=()=>{const u=decodeURIComponent(window.location.hash).substring(1);let f=null;u&&r.forEach(h=>{const v=h.getAttribute("data-anchor-status"),y=h.getAttribute("data-anchor-name"),k=u.toLocaleLowerCase()===y.toLocaleLowerCase();v&&v==="1"&&k&&(f=document.getElementById(h.dataset.id),f.scrollIntoView({behavior:"smooth"}))})};c(),window.addEventListener("hashchange",c,!1);const l=[],i=()=>{if(e)return;const u=l.find(f=>{const{top:h,bottom:v}=f.getBoundingClientRect();return h>=0&&v<=window.innerHeight});if(u){const f=[...r].find(k=>k.dataset.id===u.id);f&&s(f);const{y:h}=o().getBoundingClientRect(),{y:v}=f.getBoundingClientRect(),y=v-h;n(y)}},d={threshold:1},p=new IntersectionObserver(i,d);r.forEach(u=>{const f=u.getAttribute("data-id"),h=document.querySelector(`#${f}`);h&&(p.observe(h),l.push(h))}),window.addEventListener("wheel",u=>{e=!1})});const vl=()=>{try{bs()}catch(e){N("\u{1F916} \u521D\u59CB\u5316\u641C\u7D22\u7EC4\u4EF6\u529F\u80FD\u5931\u8D25",e)}};window.addEventListener("DOMContentLoaded",vl);const Hn=(e=!1)=>{const t=document.querySelector(".amz-tag-name.amz-active_search_tag"),n=t==null?void 0:t.dataset.type;return e?+n:n};function gl(){try{const e="\u8BF7\u8F93\u5165\u641C\u7D22\u5185\u5BB9",t=document.querySelector(".amz-input .amz-input-active"),n=document.querySelector(".amz-input .amz-search-icon");if(t===null)throw Error("\u{1F916} \u65E0\u6CD5\u521D\u59CB\u5316\u641C\u7D22\u5207\u6362\u529F\u80FD");const r=document.querySelectorAll(".amz-search-text .amz-tag-name"),a=document.querySelectorAll(".amz-mobile-dropdown .amz-engine-item"),o=document.querySelector(".amz-mobile-input-drop img"),s=document.querySelector(".amz-mobile-dropdown"),c=document.querySelector(".amz-mobile-input-drop");if(c&&s&&c.addEventListener("click",()=>{s.className==="amz-mobile-dropdown active"?s.className="amz-mobile-dropdown":s.className="amz-mobile-dropdown active"}),a.forEach(i=>{const d=i.dataset.placeholder.trim().length===0||!i.dataset.placeholder?e:i.dataset.placeholder,p=i.dataset.url;i.addEventListener("click",()=>{i.classList.toString().includes("active")||(t.placeholder=d,t.dataset.url=p,o.setAttribute("src",i.querySelector("img").src||"https://img.amz123.com/static/images/head_logo.jpg"),a.forEach(u=>u.classList.remove("active")),setTimeout(()=>{i.classList.add("active"),s.className="amz-mobile-dropdown"},50))})}),(r==null?void 0:r.length)>0){const i=Array.from(r).find(d=>d.classList.contains("amz-active_search_tag"));i&&(t.dataset.url=i.dataset.url)}r.forEach(i=>{const d=i.dataset.placeholder.trim().length===0||!i.dataset.placeholder?e:i.dataset.placeholder,p=i.dataset.url;i.addEventListener("click",()=>{i.classList.toString().includes("amz-active_search_tag")||(t.placeholder=d,t.dataset.url=p,r.forEach(u=>u.classList.remove("amz-active_search_tag")),setTimeout(()=>{i.classList.add("amz-active_search_tag")},50))})}),globalThis.__searchHotWordStatistic=i=>{var u,f;const d=[...r].findIndex(h=>h.classList.contains("amz-active_search_tag")),p=(f=(u=[...r][d])==null?void 0:u.textContent)==null?void 0:f.trim();Ws({search_word:`${i!=null?i:t.value}`,search_type_name:p,type:Hn(!0),search_page_id:globalThis.__amz__.page_id,search_page_name:globalThis.__amz__.search_page_name,search_bar:"nav"})};const l=i=>{const d=t.dataset.url.replace("search.htm","black_list")+t.value;globalThis.__searchHotWordStatistic(),window.open(i!=null?i:d)};if(t.addEventListener("keyup",i=>{var d;if(i.key==="Enter"){const p=document.querySelector(".suggestion-item.active");p?l((d=p.parentNode)==null?void 0:d.dataset.name):l()}}),n===null)throw Error("\u{1F916} \u65E0\u6548\u7684\u641C\u7D22Icon");n==null||n.addEventListener("click",()=>l())}catch(e){N("\u{1F645}\u{1F3FB} \u521D\u59CB\u5316\u4E3B\u641C\u7D22\u529F\u80FD\u5931\u8D25\uFF1A",e)}}window.addEventListener("DOMContentLoaded",gl);const yl=()=>{try{[...document.querySelectorAll(".amz-extra-link > span")].forEach(t=>{var r,a,o;const n=(o=(a=(r=t==null?void 0:t.parentElement)==null?void 0:r.parentElement)==null?void 0:a.parentElement)==null?void 0:o.querySelectorAll(".amz-show.common-style.item li.amz-item[data-hidden=true]");n.length&&t.addEventListener("click",()=>{[...n].forEach(s=>{s.dataset.hidden=s.dataset.hidden!=="true"})})})}catch(e){N("\u{1F916} \u526F\u6807\u9898\u6269\u5C55\u529F\u80FD\u5F02\u5E38",e)}};window.addEventListener("DOMContentLoaded",yl);window.addEventListener("DOMContentLoaded",()=>{try{setTimeout(()=>{document.querySelectorAll(".amz-tab-mask").forEach(a=>{a.classList.remove("amz-hidden")}),document.querySelectorAll(".active-tab").forEach(a=>a.classList.remove("active-tab"))},500);const e=document.querySelectorAll(".amz-tab-wrapper"),t=a=>{const o=a.parentElement,{x:s}=o.getBoundingClientRect(),{width:c,x:l}=a.getBoundingClientRect(),i=c,d=l-s+o.scrollLeft,p=o.querySelector(".amz-tab-mask");p.style=`width: ${i}px; left: ${d}px`},n=a=>{const o=a.target;t(o)},r=a=>{var c,l;const o=(l=(c=a.target)==null?void 0:c.parentElement)==null?void 0:l.parentElement;if(!a.target.className.includes("current")&&o){const i=o.querySelector(".current");n({target:i})}};e.forEach(a=>{const o=a.querySelectorAll(".amz-tab-nav .amz-tab-item");if(o.length<2)return;const s=a.querySelectorAll("ul.item");[...o].forEach((c,l)=>{c.parentNode.dataset.style==="2"&&(c.addEventListener("mouseenter",n),c.addEventListener("mouseleave",r)),c.addEventListener("click",()=>{c.classList.contains("amz-show")||([...o].forEach(p=>{p.classList.remove("current"),p.dataset.fontBold=!1}),[...s].forEach(p=>{p.classList.remove("amz-show","common-style"),p.classList.add("amz-hidden")}),s[l].classList.add("amz-show","common-style"),s[l].classList.remove("amz-hidden"),c.dataset.fontBold=!0,c.classList.add("current"))})})})}catch(e){N("\u5207\u6362\u5355\u98CE\u683C\u6A21\u5757\u7684\u591Atab\u529F\u80FD\u521D\u59CB\u5316\u5F02\u5E38\uFF1A",e)}});window.addEventListener("DOMContentLoaded",()=>{try{const e=n=>{const r=n.target,a=document.querySelector("template_elem");if(r){const o=r.classList.contains("amz-item-14");if(r.dataset.tip&&a===null){const s=document.createElement("div");s.onmouseover=d=>d.stopPropagation(),s.classList.value=o?"new_template_elem":"template_elem",s.append(r.dataset.tip);const{x:c,y:l,width:i}=r.getBoundingClientRect();document.body.appendChild(s),o?s.style.top=`${l+62-5}px`:s.style.bottom=`${window.innerHeight-l+6}px`,s.style.width="max-content",s.style.maxWidth="200px",requestAnimationFrame?requestAnimationFrame(()=>{const{width:d}=s.getBoundingClientRect();s.style.left=`${c+i/2-d/2}px`}):setTimeout(()=>{const{width:d}=s.getBoundingClientRect();s.style.left=`${c+i/2-d/2}px`})}}},t=()=>{document.querySelectorAll(".template_elem").forEach(n=>n==null?void 0:n.remove()),document.querySelectorAll(".new_template_elem").forEach(n=>n==null?void 0:n.remove())};document.querySelectorAll(".amz-item a[data-tip], .amz-item .amz-item-title[data-tip]").forEach(n=>{n.addEventListener("mouseenter",e),n.addEventListener("mouseleave",t)})}catch(e){N("\u521D\u59CB\u5316\u5E7F\u544A\u4F4Dtip\u63D0\u793A\u5931\u8D25\uFF1A",e)}});window.addEventListener("DOMContentLoaded",function(){document.querySelectorAll('.common-style[data-style="13"] li a').forEach(t=>{var n,r;((n=t==null?void 0:t.href)==null?void 0:n.replace(/\/$/,""))===((r=location.href)==null?void 0:r.replace(/\/$/,""))&&t.classList.add("current")}),(()=>{document.querySelectorAll('.amz-tab-wrapper ul.common-style[data-style="13"]').forEach(t=>{var n;if(t.querySelector(".dynamic-matrix-container")){const r=Array.from(t.querySelectorAll("li.amz-item a")).find(a=>{var o;return((o=a.getAttribute("href"))==null?void 0:o.replace(/\/$/,""))===location.href.replace(/\/$/,"")});if(r){const a=s=>{s.classList.add("current");const{width:c,x:l,height:i}=s.getBoundingClientRect(),{x:d}=t.getBoundingClientRect();t.style.setProperty("--amz-matrix-width",`${c}px`),t.style.setProperty("--amz-matrix-height",`${i+4}px`),t.style.setProperty("--amz-matrix-x",`${l-d-2}px`)};a(r);const o=Array.from(t.querySelectorAll("li.amz-item"));o.forEach(s=>{s.addEventListener("mouseenter",()=>{o.forEach(l=>{var i;return(i=l.querySelector("a"))==null?void 0:i.classList.remove("current")});const c=s.querySelector("a");c&&a(c)})}),(n=t.querySelector(".dynamic-matrix-container"))==null||n.addEventListener("mouseleave",()=>{o.forEach(s=>{var c;return(c=s.querySelector("a"))==null?void 0:c.classList.remove("current")}),a(r)})}}else t.querySelectorAll("li.amz-item a").forEach(r=>{(r==null?void 0:r.href)===location.href&&r.classList.add("current")})})})()});window.addEventListener("DOMContentLoaded",()=>{try{Zs()}catch{}});window.addEventListener("DOMContentLoaded",()=>{try{const e=document.querySelectorAll(".amz-site-and-topic .link-group"),t=document.querySelectorAll(".amz-site-and-topic ul");e.forEach((n,r)=>{n.addEventListener("click",()=>{e.forEach(a=>{a.classList.contains("active")&&a.classList.remove("active")}),n.className="active",t.forEach((a,o)=>{o===r?a.className="active":a.className=""})})})}catch(e){N("\u521D\u59CB\u5316\u5E95\u90E8\u548C\u6302\u4EF6\u4E8C\u7EF4\u7801\u5207\u6362\u529F\u80FD\u5931\u8D25\uFF1A",e)}});var wl=typeof global=="object"&&global&&global.Object===Object&&global;const bl=wl;var _l=typeof self=="object"&&self&&self.Object===Object&&self,xl=bl||_l||Function("return this")();const ln=xl;var Sl=ln.Symbol;const Fe=Sl;var ao=Object.prototype,zl=ao.hasOwnProperty,El=ao.toString,Et=Fe?Fe.toStringTag:void 0;function kl(e){var t=zl.call(e,Et),n=e[Et];try{e[Et]=void 0;var r=!0}catch{}var a=El.call(e);return r&&(t?e[Et]=n:delete e[Et]),a}var Cl=Object.prototype,Ll=Cl.toString;function Tl(e){return Ll.call(e)}var Ol="[object Null]",ql="[object Undefined]",Zr=Fe?Fe.toStringTag:void 0;function sr(e){return e==null?e===void 0?ql:Ol:Zr&&Zr in Object(e)?kl(e):Tl(e)}function cr(e){return e!=null&&typeof e=="object"}var Pl="[object Symbol]";function dn(e){return typeof e=="symbol"||cr(e)&&sr(e)==Pl}function Al(e,t){for(var n=-1,r=e==null?0:e.length,a=Array(r);++n<r;)a[n]=t(e[n],n,e);return a}var Nl=Array.isArray;const Mt=Nl;var Ml=1/0,Qr=Fe?Fe.prototype:void 0,Kr=Qr?Qr.toString:void 0;function oo(e){if(typeof e=="string")return e;if(Mt(e))return Al(e,oo)+"";if(dn(e))return Kr?Kr.call(e):"";var t=e+"";return t=="0"&&1/e==-Ml?"-0":t}var Dl=/\s/;function Il(e){for(var t=e.length;t--&&Dl.test(e.charAt(t)););return t}var Rl=/^\s+/;function $l(e){return e&&e.slice(0,Il(e)+1).replace(Rl,"")}function Qe(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var ea=0/0,Bl=/^[-+]0x[0-9a-f]+$/i,Fl=/^0b[01]+$/i,jl=/^0o[0-7]+$/i,Ul=parseInt;function ta(e){if(typeof e=="number")return e;if(dn(e))return ea;if(Qe(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=Qe(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=$l(e);var n=Fl.test(e);return n||jl.test(e)?Ul(e.slice(2),n?2:8):Bl.test(e)?ea:+e}function Hl(e){return e}var Wl="[object AsyncFunction]",Yl="[object Function]",Vl="[object GeneratorFunction]",Jl="[object Proxy]";function Gl(e){if(!Qe(e))return!1;var t=sr(e);return t==Yl||t==Vl||t==Wl||t==Jl}var Xl=ln["__core-js_shared__"];const Tn=Xl;var na=function(){var e=/[^.]+$/.exec(Tn&&Tn.keys&&Tn.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function Zl(e){return!!na&&na in e}var Ql=Function.prototype,Kl=Ql.toString;function ed(e){if(e!=null){try{return Kl.call(e)}catch{}try{return e+""}catch{}}return""}var td=/[\\^$.*+?()[\]{}|]/g,nd=/^\[object .+?Constructor\]$/,rd=Function.prototype,ad=Object.prototype,od=rd.toString,id=ad.hasOwnProperty,sd=RegExp("^"+od.call(id).replace(td,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function cd(e){if(!Qe(e)||Zl(e))return!1;var t=Gl(e)?sd:nd;return t.test(ed(e))}function ld(e,t){return e==null?void 0:e[t]}function lr(e,t){var n=ld(e,t);return cd(n)?n:void 0}function dd(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}var ud=800,md=16,pd=Date.now;function fd(e){var t=0,n=0;return function(){var r=pd(),a=md-(r-n);if(n=r,a>0){if(++t>=ud)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}function hd(e){return function(){return e}}var vd=function(){try{var e=lr(Object,"defineProperty");return e({},"",{}),e}catch{}}();const tn=vd;var gd=tn?function(e,t){return tn(e,"toString",{configurable:!0,enumerable:!1,value:hd(t),writable:!0})}:Hl;const yd=gd;var wd=fd(yd);const bd=wd;var _d=9007199254740991,xd=/^(?:0|[1-9]\d*)$/;function io(e,t){var n=typeof e;return t=t==null?_d:t,!!t&&(n=="number"||n!="symbol"&&xd.test(e))&&e>-1&&e%1==0&&e<t}function Sd(e,t,n){t=="__proto__"&&tn?tn(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function so(e,t){return e===t||e!==e&&t!==t}var zd=Object.prototype,Ed=zd.hasOwnProperty;function kd(e,t,n){var r=e[t];(!(Ed.call(e,t)&&so(r,n))||n===void 0&&!(t in e))&&Sd(e,t,n)}var ra=Math.max;function Cd(e,t,n){return t=ra(t===void 0?e.length-1:t,0),function(){for(var r=arguments,a=-1,o=ra(r.length-t,0),s=Array(o);++a<o;)s[a]=r[t+a];a=-1;for(var c=Array(t+1);++a<t;)c[a]=r[a];return c[t]=n(s),dd(e,this,c)}}var Ld=9007199254740991;function Td(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=Ld}var Od="[object Arguments]";function aa(e){return cr(e)&&sr(e)==Od}var co=Object.prototype,qd=co.hasOwnProperty,Pd=co.propertyIsEnumerable,Ad=aa(function(){return arguments}())?aa:function(e){return cr(e)&&qd.call(e,"callee")&&!Pd.call(e,"callee")};const lo=Ad;var Nd=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Md=/^\w*$/;function Dd(e,t){if(Mt(e))return!1;var n=typeof e;return n=="number"||n=="symbol"||n=="boolean"||e==null||dn(e)?!0:Md.test(e)||!Nd.test(e)||t!=null&&e in Object(t)}var Id=lr(Object,"create");const qt=Id;function Rd(){this.__data__=qt?qt(null):{},this.size=0}function $d(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var Bd="__lodash_hash_undefined__",Fd=Object.prototype,jd=Fd.hasOwnProperty;function Ud(e){var t=this.__data__;if(qt){var n=t[e];return n===Bd?void 0:n}return jd.call(t,e)?t[e]:void 0}var Hd=Object.prototype,Wd=Hd.hasOwnProperty;function Yd(e){var t=this.__data__;return qt?t[e]!==void 0:Wd.call(t,e)}var Vd="__lodash_hash_undefined__";function Jd(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=qt&&t===void 0?Vd:t,this}function Ke(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}Ke.prototype.clear=Rd;Ke.prototype.delete=$d;Ke.prototype.get=Ud;Ke.prototype.has=Yd;Ke.prototype.set=Jd;function Gd(){this.__data__=[],this.size=0}function un(e,t){for(var n=e.length;n--;)if(so(e[n][0],t))return n;return-1}var Xd=Array.prototype,Zd=Xd.splice;function Qd(e){var t=this.__data__,n=un(t,e);if(n<0)return!1;var r=t.length-1;return n==r?t.pop():Zd.call(t,n,1),--this.size,!0}function Kd(e){var t=this.__data__,n=un(t,e);return n<0?void 0:t[n][1]}function eu(e){return un(this.__data__,e)>-1}function tu(e,t){var n=this.__data__,r=un(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this}function ht(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}ht.prototype.clear=Gd;ht.prototype.delete=Qd;ht.prototype.get=Kd;ht.prototype.has=eu;ht.prototype.set=tu;var nu=lr(ln,"Map");const ru=nu;function au(){this.size=0,this.__data__={hash:new Ke,map:new(ru||ht),string:new Ke}}function ou(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}function mn(e,t){var n=e.__data__;return ou(t)?n[typeof t=="string"?"string":"hash"]:n.map}function iu(e){var t=mn(this,e).delete(e);return this.size-=t?1:0,t}function su(e){return mn(this,e).get(e)}function cu(e){return mn(this,e).has(e)}function lu(e,t){var n=mn(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this}function et(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}et.prototype.clear=au;et.prototype.delete=iu;et.prototype.get=su;et.prototype.has=cu;et.prototype.set=lu;var du="Expected a function";function dr(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(du);var n=function(){var r=arguments,a=t?t.apply(this,r):r[0],o=n.cache;if(o.has(a))return o.get(a);var s=e.apply(this,r);return n.cache=o.set(a,s)||o,s};return n.cache=new(dr.Cache||et),n}dr.Cache=et;var uu=500;function mu(e){var t=dr(e,function(r){return n.size===uu&&n.clear(),r}),n=t.cache;return t}var pu=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,fu=/\\(\\)?/g,hu=mu(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace(pu,function(n,r,a,o){t.push(a?o.replace(fu,"$1"):r||n)}),t});const vu=hu;function gu(e){return e==null?"":oo(e)}function pn(e,t){return Mt(e)?e:Dd(e,t)?[e]:vu(gu(e))}var yu=1/0;function ur(e){if(typeof e=="string"||dn(e))return e;var t=e+"";return t=="0"&&1/e==-yu?"-0":t}function wu(e,t){t=pn(t,e);for(var n=0,r=t.length;e!=null&&n<r;)e=e[ur(t[n++])];return n&&n==r?e:void 0}function bu(e,t){for(var n=-1,r=t.length,a=e.length;++n<r;)e[a+n]=t[n];return e}var oa=Fe?Fe.isConcatSpreadable:void 0;function _u(e){return Mt(e)||lo(e)||!!(oa&&e&&e[oa])}function uo(e,t,n,r,a){var o=-1,s=e.length;for(n||(n=_u),a||(a=[]);++o<s;){var c=e[o];t>0&&n(c)?t>1?uo(c,t-1,n,r,a):bu(a,c):r||(a[a.length]=c)}return a}function xu(e){var t=e==null?0:e.length;return t?uo(e,1):[]}function Su(e){return bd(Cd(e,void 0,xu),e+"")}function zu(e,t){return e!=null&&t in Object(e)}function Eu(e,t,n){t=pn(t,e);for(var r=-1,a=t.length,o=!1;++r<a;){var s=ur(t[r]);if(!(o=e!=null&&n(e,s)))break;e=e[s]}return o||++r!=a?o:(a=e==null?0:e.length,!!a&&Td(a)&&io(s,a)&&(Mt(e)||lo(e)))}function ku(e,t){return e!=null&&Eu(e,t,zu)}var Cu=function(){return ln.Date.now()};const On=Cu;var Lu="Expected a function",Tu=Math.max,Ou=Math.min;function mo(e,t,n){var r,a,o,s,c,l,i=0,d=!1,p=!1,u=!0;if(typeof e!="function")throw new TypeError(Lu);t=ta(t)||0,Qe(n)&&(d=!!n.leading,p="maxWait"in n,o=p?Tu(ta(n.maxWait)||0,t):o,u="trailing"in n?!!n.trailing:u);function f(z){var T=r,O=a;return r=a=void 0,i=z,s=e.apply(O,T),s}function h(z){return i=z,c=setTimeout(k,t),d?f(z):s}function v(z){var T=z-l,O=z-i,q=t-T;return p?Ou(q,o-O):q}function y(z){var T=z-l,O=z-i;return l===void 0||T>=t||T<0||p&&O>=o}function k(){var z=On();if(y(z))return m(z);c=setTimeout(k,v(z))}function m(z){return c=void 0,u&&r?f(z):(r=a=void 0,s)}function g(){c!==void 0&&clearTimeout(c),i=0,r=l=a=c=void 0}function w(){return c===void 0?s:m(On())}function E(){var z=On(),T=y(z);if(r=arguments,a=this,l=z,T){if(c===void 0)return h(l);if(p)return clearTimeout(c),c=setTimeout(k,t),f(l)}return c===void 0&&(c=setTimeout(k,t)),s}return E.cancel=g,E.flush=w,E}function qu(e,t,n,r){if(!Qe(e))return e;t=pn(t,e);for(var a=-1,o=t.length,s=o-1,c=e;c!=null&&++a<o;){var l=ur(t[a]),i=n;if(l==="__proto__"||l==="constructor"||l==="prototype")return e;if(a!=s){var d=c[l];i=r?r(d,l,c):void 0,i===void 0&&(i=Qe(d)?d:io(t[a+1])?[]:{})}kd(c,l,i),c=c[l]}return e}function Pu(e,t,n){for(var r=-1,a=t.length,o={};++r<a;){var s=t[r],c=wu(e,s);n(c,s)&&qu(o,pn(s,e),c)}return o}function Au(e,t){return Pu(e,t,function(n,r){return ku(e,r)})}var Nu=Su(function(e,t){return e==null?{}:Au(e,t)});const Mu=Nu,po=e=>{var a;(a=document.getElementById("_dialog_wrapper_"))==null||a.remove();const t=document.createElement("span");t.id="_dialog_wrapper_";const{right:n,bottom:r}=e.getBoundingClientRect();return t.style.right=`${document.body.clientWidth-n-3}px`,t.style.top=`${r+12}px`,document.body.appendChild(t),document.body.addEventListener("click",()=>{t.remove()},{once:!0}),t},Du="/ugc/v1/user_content/newest_list",Iu="/user/v1/library_nav/fetch",Ru="/user/v1/customize_nav/create",$u="/user/v1/customize_nav/edit",Bu="/user/v1/customize_nav/delete",Fu="/user/v1/customize_nav/sort",ju="/user/v1/upload",fo="/user/v1/customize_nav/all",Uu="/ugc/v1/user_content/list",Hu="/toolbox/v1/exchange_rate",Wu="/search/v1/search_bar/associate";var ho={exports:{}};function Yu(e){throw new Error('Could not dynamically require "'+e+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var qn={exports:{}};const Vu=ii(Wi);var ia;function Ju(){return ia||(ia=1,function(e,t){(function(n,r){e.exports=r()})(kt,function(){var n=n||function(r,a){var o;if(typeof window<"u"&&window.crypto&&(o=window.crypto),typeof self<"u"&&self.crypto&&(o=self.crypto),typeof globalThis<"u"&&globalThis.crypto&&(o=globalThis.crypto),!o&&typeof window<"u"&&window.msCrypto&&(o=window.msCrypto),!o&&typeof kt<"u"&&kt.crypto&&(o=kt.crypto),!o&&typeof Yu=="function")try{o=Vu}catch{}var s=function(){if(o){if(typeof o.getRandomValues=="function")try{return o.getRandomValues(new Uint32Array(1))[0]}catch{}if(typeof o.randomBytes=="function")try{return o.randomBytes(4).readInt32LE()}catch{}}throw new Error("Native crypto module could not be used to get secure random number.")},c=Object.create||function(){function m(){}return function(g){var w;return m.prototype=g,w=new m,m.prototype=null,w}}(),l={},i=l.lib={},d=i.Base=function(){return{extend:function(m){var g=c(this);return m&&g.mixIn(m),(!g.hasOwnProperty("init")||this.init===g.init)&&(g.init=function(){g.$super.init.apply(this,arguments)}),g.init.prototype=g,g.$super=this,g},create:function(){var m=this.extend();return m.init.apply(m,arguments),m},init:function(){},mixIn:function(m){for(var g in m)m.hasOwnProperty(g)&&(this[g]=m[g]);m.hasOwnProperty("toString")&&(this.toString=m.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),p=i.WordArray=d.extend({init:function(m,g){m=this.words=m||[],g!=a?this.sigBytes=g:this.sigBytes=m.length*4},toString:function(m){return(m||f).stringify(this)},concat:function(m){var g=this.words,w=m.words,E=this.sigBytes,z=m.sigBytes;if(this.clamp(),E%4)for(var T=0;T<z;T++){var O=w[T>>>2]>>>24-T%4*8&255;g[E+T>>>2]|=O<<24-(E+T)%4*8}else for(var q=0;q<z;q+=4)g[E+q>>>2]=w[q>>>2];return this.sigBytes+=z,this},clamp:function(){var m=this.words,g=this.sigBytes;m[g>>>2]&=4294967295<<32-g%4*8,m.length=r.ceil(g/4)},clone:function(){var m=d.clone.call(this);return m.words=this.words.slice(0),m},random:function(m){for(var g=[],w=0;w<m;w+=4)g.push(s());return new p.init(g,m)}}),u=l.enc={},f=u.Hex={stringify:function(m){for(var g=m.words,w=m.sigBytes,E=[],z=0;z<w;z++){var T=g[z>>>2]>>>24-z%4*8&255;E.push((T>>>4).toString(16)),E.push((T&15).toString(16))}return E.join("")},parse:function(m){for(var g=m.length,w=[],E=0;E<g;E+=2)w[E>>>3]|=parseInt(m.substr(E,2),16)<<24-E%8*4;return new p.init(w,g/2)}},h=u.Latin1={stringify:function(m){for(var g=m.words,w=m.sigBytes,E=[],z=0;z<w;z++){var T=g[z>>>2]>>>24-z%4*8&255;E.push(String.fromCharCode(T))}return E.join("")},parse:function(m){for(var g=m.length,w=[],E=0;E<g;E++)w[E>>>2]|=(m.charCodeAt(E)&255)<<24-E%4*8;return new p.init(w,g)}},v=u.Utf8={stringify:function(m){try{return decodeURIComponent(escape(h.stringify(m)))}catch{throw new Error("Malformed UTF-8 data")}},parse:function(m){return h.parse(unescape(encodeURIComponent(m)))}},y=i.BufferedBlockAlgorithm=d.extend({reset:function(){this._data=new p.init,this._nDataBytes=0},_append:function(m){typeof m=="string"&&(m=v.parse(m)),this._data.concat(m),this._nDataBytes+=m.sigBytes},_process:function(m){var g,w=this._data,E=w.words,z=w.sigBytes,T=this.blockSize,O=T*4,q=z/O;m?q=r.ceil(q):q=r.max((q|0)-this._minBufferSize,0);var D=q*T,A=r.min(D*4,z);if(D){for(var P=0;P<D;P+=T)this._doProcessBlock(E,P);g=E.splice(0,D),w.sigBytes-=A}return new p.init(g,A)},clone:function(){var m=d.clone.call(this);return m._data=this._data.clone(),m},_minBufferSize:0});i.Hasher=y.extend({cfg:d.extend(),init:function(m){this.cfg=this.cfg.extend(m),this.reset()},reset:function(){y.reset.call(this),this._doReset()},update:function(m){return this._append(m),this._process(),this},finalize:function(m){m&&this._append(m);var g=this._doFinalize();return g},blockSize:16,_createHelper:function(m){return function(g,w){return new m.init(w).finalize(g)}},_createHmacHelper:function(m){return function(g,w){return new k.HMAC.init(m,w).finalize(g)}}});var k=l.algo={};return l}(Math);return n})}(qn)),qn.exports}(function(e,t){(function(n,r){e.exports=r(Ju())})(kt,function(n){return function(r){var a=n,o=a.lib,s=o.WordArray,c=o.Hasher,l=a.algo,i=[];(function(){for(var v=0;v<64;v++)i[v]=r.abs(r.sin(v+1))*4294967296|0})();var d=l.MD5=c.extend({_doReset:function(){this._hash=new s.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(v,y){for(var k=0;k<16;k++){var m=y+k,g=v[m];v[m]=(g<<8|g>>>24)&16711935|(g<<24|g>>>8)&4278255360}var w=this._hash.words,E=v[y+0],z=v[y+1],T=v[y+2],O=v[y+3],q=v[y+4],D=v[y+5],A=v[y+6],P=v[y+7],B=v[y+8],R=v[y+9],I=v[y+10],Y=v[y+11],U=v[y+12],G=v[y+13],oe=v[y+14],K=v[y+15],b=w[0],S=w[1],_=w[2],x=w[3];b=p(b,S,_,x,E,7,i[0]),x=p(x,b,S,_,z,12,i[1]),_=p(_,x,b,S,T,17,i[2]),S=p(S,_,x,b,O,22,i[3]),b=p(b,S,_,x,q,7,i[4]),x=p(x,b,S,_,D,12,i[5]),_=p(_,x,b,S,A,17,i[6]),S=p(S,_,x,b,P,22,i[7]),b=p(b,S,_,x,B,7,i[8]),x=p(x,b,S,_,R,12,i[9]),_=p(_,x,b,S,I,17,i[10]),S=p(S,_,x,b,Y,22,i[11]),b=p(b,S,_,x,U,7,i[12]),x=p(x,b,S,_,G,12,i[13]),_=p(_,x,b,S,oe,17,i[14]),S=p(S,_,x,b,K,22,i[15]),b=u(b,S,_,x,z,5,i[16]),x=u(x,b,S,_,A,9,i[17]),_=u(_,x,b,S,Y,14,i[18]),S=u(S,_,x,b,E,20,i[19]),b=u(b,S,_,x,D,5,i[20]),x=u(x,b,S,_,I,9,i[21]),_=u(_,x,b,S,K,14,i[22]),S=u(S,_,x,b,q,20,i[23]),b=u(b,S,_,x,R,5,i[24]),x=u(x,b,S,_,oe,9,i[25]),_=u(_,x,b,S,O,14,i[26]),S=u(S,_,x,b,B,20,i[27]),b=u(b,S,_,x,G,5,i[28]),x=u(x,b,S,_,T,9,i[29]),_=u(_,x,b,S,P,14,i[30]),S=u(S,_,x,b,U,20,i[31]),b=f(b,S,_,x,D,4,i[32]),x=f(x,b,S,_,B,11,i[33]),_=f(_,x,b,S,Y,16,i[34]),S=f(S,_,x,b,oe,23,i[35]),b=f(b,S,_,x,z,4,i[36]),x=f(x,b,S,_,q,11,i[37]),_=f(_,x,b,S,P,16,i[38]),S=f(S,_,x,b,I,23,i[39]),b=f(b,S,_,x,G,4,i[40]),x=f(x,b,S,_,E,11,i[41]),_=f(_,x,b,S,O,16,i[42]),S=f(S,_,x,b,A,23,i[43]),b=f(b,S,_,x,R,4,i[44]),x=f(x,b,S,_,U,11,i[45]),_=f(_,x,b,S,K,16,i[46]),S=f(S,_,x,b,T,23,i[47]),b=h(b,S,_,x,E,6,i[48]),x=h(x,b,S,_,P,10,i[49]),_=h(_,x,b,S,oe,15,i[50]),S=h(S,_,x,b,D,21,i[51]),b=h(b,S,_,x,U,6,i[52]),x=h(x,b,S,_,O,10,i[53]),_=h(_,x,b,S,I,15,i[54]),S=h(S,_,x,b,z,21,i[55]),b=h(b,S,_,x,B,6,i[56]),x=h(x,b,S,_,K,10,i[57]),_=h(_,x,b,S,A,15,i[58]),S=h(S,_,x,b,G,21,i[59]),b=h(b,S,_,x,q,6,i[60]),x=h(x,b,S,_,Y,10,i[61]),_=h(_,x,b,S,T,15,i[62]),S=h(S,_,x,b,R,21,i[63]),w[0]=w[0]+b|0,w[1]=w[1]+S|0,w[2]=w[2]+_|0,w[3]=w[3]+x|0},_doFinalize:function(){var v=this._data,y=v.words,k=this._nDataBytes*8,m=v.sigBytes*8;y[m>>>5]|=128<<24-m%32;var g=r.floor(k/4294967296),w=k;y[(m+64>>>9<<4)+15]=(g<<8|g>>>24)&16711935|(g<<24|g>>>8)&4278255360,y[(m+64>>>9<<4)+14]=(w<<8|w>>>24)&16711935|(w<<24|w>>>8)&4278255360,v.sigBytes=(y.length+1)*4,this._process();for(var E=this._hash,z=E.words,T=0;T<4;T++){var O=z[T];z[T]=(O<<8|O>>>24)&16711935|(O<<24|O>>>8)&4278255360}return E},clone:function(){var v=c.clone.call(this);return v._hash=this._hash.clone(),v}});function p(v,y,k,m,g,w,E){var z=v+(y&k|~y&m)+g+E;return(z<<w|z>>>32-w)+y}function u(v,y,k,m,g,w,E){var z=v+(y&m|k&~m)+g+E;return(z<<w|z>>>32-w)+y}function f(v,y,k,m,g,w,E){var z=v+(y^k^m)+g+E;return(z<<w|z>>>32-w)+y}function h(v,y,k,m,g,w,E){var z=v+(k^(y|~m))+g+E;return(z<<w|z>>>32-w)+y}a.MD5=c._createHelper(d),a.HmacMD5=c._createHmacHelper(d)}(Math),n.MD5})})(ho);const vo=ho.exports,go=()=>document.cookie.replace(/(?:(?:^|.*;\s*)bbs_token\s*\=\s*([^;]*).*$)|^.*$/,"$1"),fn=()=>{const e=location.href;return e.includes("brandark.com")?2:e.includes("tt123")?5:e.includes("vn.amz123")?7:e.includes("dny123")?8:3};Nt.defaults.headers.post["Access-Control-Allow-Origin"]="*";const mr=Nt.create({timeout:1e3*10,withCredentials:!1,headers:{"Content-Type":"application/json"}}),Gu=fn(),yo=Nt.create({withCredentials:!0,headers:{"Content-Type":"application/json","App-Id":Gu,Authorization:Oa()}}),Xu=e=>{const{data:t}=e;return(t==null?void 0:t.status)!==0?(t.info&&N(t.info,"info"),Promise.reject()):Promise.resolve(t.data)};yo.interceptors.response.use(e=>{const{data:t}=e;return(t==null?void 0:t.status)!==0?Promise.reject():Promise.resolve(t.data)},async e=>(N("response error:",e),Promise.reject()));mr.interceptors.request.use(e=>(e.headers.timestamp||(e.headers.timestamp=(new Date().valueOf()/1e3).toFixed()),e.headers.Authorization=go(),e.method==="post"&&Object.keys(e.data).length>0&&(e.data={...e.data},e.data.sign=e.headers.sign||Zu(e.data)),e));mr.interceptors.response.use(Xu,async e=>{var t;return N("response error:",e),e instanceof Ca&&e.response.status===401&&(lt(),window&&((t=window==null?void 0:window.location)==null||t.reload())),Promise.reject()});const be={post:(e,t={},n={})=>mr.post(e,t,n),rawPost:(e,t={},n={})=>(location.href.includes("https://www.amz123.com/")?e="https://api.amz123.com"+e:e="/api"+e,yo.post(e,t,n))},Zu=e=>{if(typeof e!="object")throw new Error("\u65E0\u6548\u7684payload\u6570\u636E");return vo(JSON.stringify(e)).toString()},Qu=async()=>({customize_nav:(await be.rawPost(fo)).rows}),Ku=async()=>be.rawPost(fo),em=async e=>{try{return be.rawPost(ju,{file:e,type:2},{headers:{"Content-Type":"multipart/form-data"}})}catch{showDebug("\u56FE\u7247\u4E0A\u4F20\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5","info")}},tm=async e=>{try{return be.rawPost(Iu,{url:e})}catch{showDebug("\u65E0\u6548\u7684\u5730\u5740\uFF0C\u8BF7\u68C0\u67E5","info")}},nm=e=>be.rawPost(Ru,e),rm=e=>be.rawPost($u,e),am=e=>be.rawPost(Bu,{id:e}),om=(e,t)=>be.rawPost(Fu,{id_list:e,category_id:t});let Ct;function wo(e){this.style.opacity=.7,this.style.background="#fffbec",Ct=this,e.dataTransfer.effectAllowed="move",e.dataTransfer.setData("text/html",this.innerHTML)}function bo(){this.style.opacity=1,this.style.background=null,this.style.border="2px dashed transparent"}function _o(e){this.style.border="2px dashed blue",e.preventDefault()}function xo(){this.style.border="2px dashed transparent"}function So(e){if(e.stopPropagation(),Ct!==this)try{const t=document.querySelector("#bookmark_vue .site_tab_items"),n=t.innerHTML,r={},a=$globalStore.customize_nav[$globalStore.activeTabIdx];Ct.innerHTML=this.innerHTML;const o=Ct.dataset.navId,s=this.dataset.navId;Ct.dataset.navId=s,this.dataset.navId=o,this.innerHTML=e.dataTransfer.getData("text/html"),this.style.border="2px dashed transparent";const c=document.querySelectorAll(".site_tab_items li[data-nav-id]");c&&(c.forEach((l,i)=>{r[l.dataset.navId]=i+1}),om([...c].map(l=>+l.dataset.navId),+a.cate_id).then(()=>{N("\u6392\u5E8F\u6210\u529F"),To()}).catch(l=>{N("\u6392\u5E8F\u5931\u8D25",l),t.innerHTML=n}))}catch(t){N("\u6392\u5E8F\uFF1A",t)}return!1}const zo=".site_tab_items li",im=()=>{document.querySelectorAll(zo).forEach(e=>{e.setAttribute("draggable",!0),e.style.cursor="move",e.classList.add("_drag_"),e.addEventListener("dragstart",wo),e.addEventListener("dragover",_o),e.addEventListener("dragleave",xo),e.addEventListener("dragend",bo),e.addEventListener("drop",So)})},sm=()=>{document.querySelectorAll(zo).forEach(e=>{e.removeAttribute("draggable"),e.style.cursor=null,e.classList.remove("_drag_"),e.removeEventListener("dragstart",wo),e.removeEventListener("dragover",_o),e.removeEventListener("dragleave",xo),e.removeEventListener("dragend",bo),e.removeEventListener("drop",So)})},Eo=()=>{document.documentElement.scrollTop=0},cm=()=>{document.addEventListener("scroll",Eo)},lm=()=>{document.removeEventListener("scroll",Eo)},ko=(e,t={width:360})=>()=>{const n=document.createElement("div"),r=()=>{lm(),n.remove()};n.id="_amz_tip_modal_";const a=document.createElement("div");a.style.width=`${t.width}px`,a.className="box",n.appendChild(a),document.body.appendChild(n),cm(),n.addEventListener("click",o=>{o.stopPropagation(),document.body.style.overflow="unset",document.body.style.paddingRight="0",r()},{once:!0}),a.addEventListener("click",o=>{o.stopPropagation()}),e(a,r,t)},Co=ko((e,t)=>{(()=>{var c,l,i,d,p,u,f;e.innerHTML=`<div style="display: flex;justify-content: space-between;align-items: center;font-size: 1.1rem">
      <span>\u6DFB\u52A0\u7F51\u5740</span>
      <img style="width: 1.3rem;height: 1.3rem;cursor: pointer" class="_close_" src="https://img.amz123.com/upload/index/material-symbols_close.svg" alt="" />
    </div>
    <div class="_add_input_box">
      <input type="text" placeholder="\u8BF7\u8F93\u5165http\u6216https\u5F00\u5934\u7684\u7F51\u5740" id="_web_url_">
      <button>\u6293\u53D6\u6807\u9898</button>
    </div>
    <div class="_site_name_ico">
      <div class="_upload_wrapper _flex_center_">
        <input type="file" id="_upload_web_ico_" title="\u70B9\u51FB\u4E0A\u4F20\u65B0\u7684logo">
        <img id="_web_ico_" src="${$globalStore.navFormProxy.web_ico}" style="width: 1.5rem"/>
      </div>
      <input placeholder="\u7F51\u7AD9\u540D\u79F0" type="text" id="_web_name_"/>
    </div>
    <div class="_option_">
      <select>
      ${$globalStore.customize_nav.map(h=>`<option data-cate-id="${h.cate_id}" value=${h.cate_id}>${h.cate_name}</option>`).join()}
      </select>
    </div>
    <button class="_add_now_">${$globalStore.isEditNavItem?"\u7ACB\u5373\u66F4\u65B0":"\u7ACB\u5373\u6DFB\u52A0"}</button>
    `,(c=document.querySelector("._close_"))==null||c.addEventListener("click",t,{once:!0});const r=async()=>{var v,y;const h=document.querySelector("._add_input_box button");try{const k=(y=(v=document.querySelector("._add_input_box input"))==null?void 0:v.value)!=null?y:"";if(k.length===0)N("\u8BF7\u8F93\u5165\u7F51\u5740\u540E\u518D\u5C1D\u8BD5\u6293\u53D6\u6807\u9898","info");else{h.innerHTML="\u89E3\u6790\u4E2D...";const{title:m,icon_url:g}=await tm(k.trim()),{web_ico:w,web_name:E,web_url:z}={web_ico:g,web_name:m,web_url:k.trim()};if((w==null?void 0:w.length)>0)if(w.startsWith("http"))$globalStore.navFormProxy.web_ico=w;else{const{origin:T}=new URL(z!=null?z:$globalStore.navFormProxy.web_url);$globalStore.navFormProxy.web_ico=w}else N("\u6B64\u7F51\u5740\u65E0\u6CD5\u6293\u53D6logo\uFF0C\u5EFA\u8BAE\u624B\u52A8\u4E0A\u4F20","info");(E==null?void 0:E.length)>0&&($globalStore.navFormProxy.web_name=E)}}catch(k){N("\u89E3\u6790\u5931\u8D25\uFF0C\u5EFA\u8BAE\u624B\u52A8\u7F16\u8F91","info"),N("\u89E3\u6790\u5931\u8D25\uFF1A",k)}finally{h.innerHTML="\u6293\u53D6\u6807\u9898"}};(l=document.querySelector("#_web_url_"))==null||l.addEventListener("change",h=>{$globalStore.navFormProxy.web_url=h.target.value}),(i=document.querySelector("#_web_name_"))==null||i.addEventListener("change",h=>{$globalStore.navFormProxy.web_name=h.target.value},{once:!0}),(d=document.querySelector("._option_ select"))==null||d.addEventListener("change",h=>{$globalStore.navFormProxy.cate_id=h.target.value}),(p=document.querySelector("#_upload_web_ico_"))==null||p.addEventListener("change",async h=>{var k;const v=h.target,y=(k=v==null?void 0:v.files)==null?void 0:k[0];if(y)if(y.size>=102400)N("\u4EC5\u652F\u6301\u5C0F\u4E8E100kb\u7684\u56FE\u6807","info");else{const{url:m}=await em(y);$globalStore.navFormProxy.web_ico=m}else N("\u8BF7\u4E0A\u4F20\u7AD9\u70B9\u56FE\u6807\uFF0C\u6216\u5C1D\u8BD5\u70B9\u51FB\u6293\u53D6\u6807\u9898\u6309\u94AE","info")}),(u=document.querySelector("button._add_now_"))==null||u.addEventListener("click",async()=>{const h={...$globalStore.navFormProxy};if(Object.keys(h).some(v=>{var y;return v!=="id"&&((y=h[v])==null?void 0:y.length)===0}))N("\u8BF7\u586B\u5199\u5B8C\u6574\u4FE1\u606F\u518D\u64CD\u4F5C","info");else{const{web_url:v,web_ico:y,web_name:k,id:m}=h;$globalStore.isEditNavItem?await rm({category_id:+h.cate_id,name:k,url:v,icon_url:y,id:m}):await nm({category_id:+h.cate_id,name:k,url:v,icon_url:y}),N($globalStore.isEditNavItem?"\u66F4\u65B0\u6210\u529F":"\u6DFB\u52A0\u6210\u529F"),await window.$refreshNavList(),$globalStore==null||$globalStore.resetNavFormProxy(),$globalStore.onActiveTabIdxChange(),t()}}),(f=document.querySelector("._add_input_box button"))==null||f.addEventListener("click",r),$globalStore.isEditNavItem;const a=document.querySelector("#_web_name_"),o=document.querySelector("#_web_url_"),s=document.querySelector("._option_ select");a&&(a.value=$globalStore.navFormProxy.web_name),o&&(o.value=$globalStore.navFormProxy.web_url),s&&(s.value=$globalStore.navFormProxy.cate_id)})()}),Lo=ko((e,t)=>{var n;e.innerHTML=`<div style="display: flex;justify-content: space-between;align-items: center">
  <div style="display: flex;align-items: center;gap: 3px">
  <img src="https://img.amz123.com/upload/index/carbon_information.svg" alt="" />\u63D0\u793A
  </div>
  </div>
  <div class="_tip_">
  \u8BF7\u767B\u5F55\u540E\u518D\u64CD\u4F5C\uFF0C<a href="user-login.htm">[\u767B\u5F55]</a> <a href="user-create.htm">[\u6CE8\u518C]</a>
  </div>
  <button class="_close_">\u5173\u95ED</button>
  `,(n=document.querySelector("button._close_"))==null||n.addEventListener("click",t,{once:!0})}),dm=()=>{var e;(e=document.querySelector(".tab_container span.item_dots._flex_center_"))==null||e.addEventListener("click",t=>{t.stopPropagation(),t.preventDefault();const n=document.getElementById("_dialog_wrapper_");if(n){n.remove();return}const r=po(t.target);r.innerHTML=`<a target="blank" href="https://www.amz123.com/my/custom_nav" class="_setting_item_">
       <img src="https://img.amz123.com/upload/index/uit_layer-group.svg"/>\u6279\u5904\u7406\u7F51\u5740
     </a>
     <a target="blank" href="https://www.amz123.com/my/custom_nav" class="_setting_item_">
       <img src="https://img.amz123.com/upload/index/healthicons_ui-menu-grid-outline.svg"/>\u5206\u7C7B\u7BA1\u7406
     </a>
     <a target="blank" href="https://www.amz123.com/my/custom_nav" class="_setting_item_">
       <img src="https://img.amz123.com/upload/index/icons8_shutdown.svg"/>\u5173\u95ED\u81EA\u5B9A\u4E49
     </a>`})},um=()=>{document.querySelectorAll("button.site_tab_item").forEach(e=>{e.addEventListener("click",t=>{$globalStore.activeTabIdx=+t.target.getAttribute("data-idx")},{once:!0})})},mm=()=>{document.querySelector(".site_collection_container ._add_").addEventListener("click",()=>{$globalStore.token&&$globalStore.token.length>0?($globalStore.resetNavFormProxy(),$globalStore.isEditNavItem=!1,Co()):Lo()})},pm=()=>{let e=!1;const t=document.querySelector("._lock_");t==null||t.addEventListener("click",()=>{if(!($globalStore.token&&$globalStore.token.length>0)){Lo();return}e=!e,t.classList.toggle("focus"),t.classList.toggle("hover"),e?im():sm()})},To=()=>{document.querySelectorAll("._item_setting_").forEach((t,n)=>{t.addEventListener("click",r=>{var o,s,c;r.stopPropagation();const a=po(t);a.innerHTML=`<div class="_setting_item_copy_">
      <img src="https://img.amz123.com/upload/index/mdi-light_link-variant.svg"/>\u590D\u5236\u94FE\u63A5
    </div>
    <div class="_setting_item_edit_">
      <img src="https://img.amz123.com/upload/index/ep_edit.svg">\u7F16\u8F91
    </div>
    <div class="_setting_item_delete_">
      <img src="https://img.amz123.com/upload/index/mdi-light_delete.svg"/>\u5220\u9664
    </div>`,(o=document.querySelector("._setting_item_copy_"))==null||o.addEventListener("click",()=>{const l=t.parentNode.querySelector("a");l&&(navigator.clipboard.writeText(l.href),N())}),(s=document.querySelector("._setting_item_edit_"))==null||s.addEventListener("click",()=>{var u;const l=$globalStore.customize_nav[$globalStore.activeTabIdx];$globalStore.isEditNavItem=!0;const{cate_id:i}=l,d=l.nav_list[n],p={...Mu(d,["web_url","web_name","web_ico","id"]),cate_id:i};(u=$globalStore==null?void 0:$globalStore.resetNavFormProxy)==null||u.call($globalStore,p),Co()}),(c=document.querySelector("._setting_item_delete_"))==null||c.addEventListener("click",async()=>{var l,i,d;try{const p=(i=(l=$globalStore.customize_nav[$globalStore.activeTabIdx])==null?void 0:l.nav_list)==null?void 0:i[n].id;await am(+p),(d=document.querySelector(`li[data-nav-id="${p}"]`))==null||d.remove(),N("\u5220\u9664\u6210\u529F"),$globalStore.customize_nav[$globalStore.activeTabIdx].nav_list=$globalStore.customize_nav[$globalStore.activeTabIdx].nav_list.filter(u=>u.id!==p)}catch(p){N("delete item:",p)}})})})};function fm(){try{const e=document.querySelector("#bookmark_vue");if(N("$globalStore.customize_nav:",$globalStore.customize_nav),e&&$globalStore.customize_nav.length>0){const t=()=>{var i,d;const n=$globalStore.activeTabIdx,r=$globalStore.customize_nav,a=($globalStore==null?void 0:$globalStore.token)&&$globalStore.token.length>0,o=r.map((p,u)=>`<button class="site_tab_item${u===n?" active":""}" data-idx="${u}" data-sdk-report="1" data-sdk-position="9" data-sdk-index="${r[0].cate_name+"-"+u}">${p.cate_name}</button>`).join(""),s=(d=(i=r[n])==null?void 0:i.nav_list)!=null?d:[],c=`<ul class="site_tab_items" data-sdk-position="\u7F51\u5740\u6536\u85CF-${r[n].cate_name}">${s.map((p,u)=>`<li data-nav-id="${p.id}" data-sdk-index="${u}">
            <a href="${p.web_url}" target="blank" draggable="false" data-sdk-report="1">
              <img src="${p.web_ico}" style="width: 24px;height:24px" draggable="false"/>
              <span class="_text_over_hidden_">${p.web_name}</span>
            </a>
            ${a?'<img src="https://img.amz123.com/upload/index/majesticons_more-menu.svg" class="_item_setting_"/>':""}
            </li>`).join("")}</ul>`,l=`<div class="site_collection_container">
        <div class="tab_container">
          ${o}
          <div class="_settings_">
            <span class="_lock_ hover">
              <svg width="12" height="12" viewBox="0 0 24 24"><path fill="currentColor" d="M4 22V8h3V6q0-2.075 1.463-3.538Q9.925 1 12 1t3.538 1.462Q17 3.925 17 6v2h3v14ZM9 8h6V6q0-1.25-.875-2.125T12 3q-1.25 0-2.125.875T9 6ZM6 20h12V10H6Zm6-3q.825 0 1.413-.587Q14 15.825 14 15q0-.825-.587-1.413Q12.825 13 12 13q-.825 0-1.412.587Q10 14.175 10 15q0 .825.588 1.413Q11.175 17 12 17Zm-6 3V10v10Z"/></svg>
            </span>
            <span class="_flex_center_ _add_">
            <span style="position: relative;bottom: 1px;right:4px;color: #a1a7b7">+</span>
            <span style="color:#a1a7b7">\u6DFB\u52A0</span>
            </span>
            <span class="item_dots _flex_center_">
                <i></i>
                <i></i>
                <i></i>
            </span>
          </div>
        </div>
        ${c}
      </div>`;e.innerHTML=l,dm(),um(),mm(),pm(),To()};t(),$globalStore.onActiveTabIdxChange=t}}catch(e){N("\u81EA\u5B9A\u4E49\u5BFC\u822A\u5F02\u5E38\uFF1A",e)}}const hm=()=>{window.$globalStore=new Proxy({hot:[],ads:[],activities:[],exchange:[],customize_nav:[],user_data:{},activeTabIdx:0,token:go(),onActiveTabIdxChange:()=>{},isEditNavItem:!1},{set:(e,t,n)=>(e[t]=n,t==="activeTabIdx"&&e.onActiveTabIdxChange(),!0),get:(e,t)=>(t==="emitUpdateCustomNav"&&typeof e.updateCustomizeNavCallback=="function"&&e.updateCustomizeNavCallback(),e[t])})},vm=async()=>{const e=await Qu();hm(),Object.assign(window.$globalStore,e);const t=()=>{var n,r,a;return{web_ico:"https://img.amz123.com/upload/index/carbon_camera.svg",web_url:"",web_name:"",cate_id:(a=(r=$globalStore.customize_nav)==null?void 0:r[(n=$globalStore.activeTabIdx)!=null?n:0])==null?void 0:a.cate_id,id:""}};window.$globalStore.resetNavFormProxy=(n={})=>{$globalStore.navFormProxy=void 0,$globalStore.navFormProxy=new Proxy({...t(),...n},{set:(r,a,o)=>{var s,c;if(!Object.keys(r).includes(a))return!1;if(r[a]=o,a!=="cate_id")if(a==="web_ico"){const l=document.getElementById(`_${a}_`);l==null||l.setAttribute("src",o)}else{const l=document.getElementById(`_${a}_`);l&&(l.value=o)}return Object.keys(r).every(l=>r[l].length>0)?(s=document.querySelector("button._add_now_"))==null||s.classList.add("_ready_"):(c=document.querySelector("button._add_now_"))==null||c.classList.remove("_ready_"),!0}})},window.$globalStore.resetNavFormProxy(),window.$refreshNavList=async()=>{try{const{rows:n}=await Ku();$globalStore.customize_nav=n}catch(n){N("\u521D\u59CB\u5316\u5168\u5C40\u4EE3\u7406\u5BF9\u8C61\u5931\u8D25\uFF1A",n),N()}}},gm=async()=>{try{Xs({callback:()=>{window.location.reload()}}),setTimeout(async()=>{await Ms();const e=Kn();if(e){let t={};e&&(t.username=e.username,t.avatar=e.avatar,t.role_id_list=e.role_id_list,t.unread_notices=e.unread_msg_count,t.is_vip=e.is_vip,t.uid=e.app_uid,t.sign_info=e.sign_info),N("user_info",t),Is(t)}})}catch(e){N("\u521D\u59CB\u5316\u7528\u6237\u4FE1\u606F\u5931\u8D25\uFF1A",e)}},ym=async()=>{var e,t,n;try{const r=document.querySelector('.amz-card-style[data-style="12"] .amz-tab-wrapper');if(r===null)return;const a=JSON.parse((e=r.dataset)==null?void 0:e.extra);if(r){N("\u53C2\u6570:",a);const o=document.createElement("div");o.className="temp-skeleton",o.innerHTML=Array(6).fill("").map(l=>`
          <div class="temp_skeleton_box">
            <div class="temp_img"></div>
            <div class="temp_intro">
              <div class="temp_title"></div>
              <div class="temp_description temp_width"></div>
              <div class="temp_description"></div>
              <div class="temp_author_info_and_date"></div>
            </div>
          </div>
        `).join(""),r.appendChild(o);const{data:{data:{rows:s}}}=await request.rawPost(Uu,a);if(await fl(.5),s.length===0){r.parentNode.remove();return}r.querySelectorAll(".amz-news-item").forEach((l,i)=>{var d;s[i]&&l?((d=l.querySelector("img"))==null||d.setAttribute("src",s[i].thumb),l.querySelector(".amz-news-title a").textContent=s[i].title,l.querySelector(".amz-news-content").textContent=s[i].description,l.querySelector(".amz-news-content").textContent=s[i].description,l.querySelector(".amz-news-info .amz-news-author img").src=s[i].author.avatar,l.querySelector(".amz-news-info .amz-news-author span").textContent=s[i].author.username,l.querySelector(".amz-news-info .amz-news-date").textContent=ro(s[i].published_at*1e3,"yyyy-mm-dd")):l.remove()}),o.remove(),(t=r.querySelector(".common-style.amz-news"))==null||t.classList.remove("amz-relative-tem-hidden")}}catch{N(),(n=document.querySelector('.amz-card-style[data-style="12"]'))==null||n.remove()}},sa=mo((e,t,n,r)=>{var d,p;(d=document.querySelector("#temp-box"))==null||d.remove();const a=document.createElement("div"),o=document.querySelector(".amz-search-tips"),s=document.querySelector(".amz-search-box .amz-search-w");o?s.insertBefore(a,o):s.appendChild(a),a.outerHTML=`<div id="temp-box" style="width: ${n}px;height: ${r}px;opacity: 0"></div>`,e.style.position="fixed";const{x:c,width:l}=document.querySelector(".amz-search-box .amz-search-w").getBoundingClientRect(),{y:i}=document.querySelector("#temp-box").getBoundingClientRect();N(),e.style.left=`${c}px`,e.style.top=`${i}px`,e.style.width=`${l}px`,(p=t==null?void 0:t.classList)==null||p.add("active")},200),wm=()=>{try{const e=document.querySelector(".amz-search-w .amz-input"),{width:t,height:n}=e.getBoundingClientRect(),r=e.querySelector("input"),a=e.querySelector(".amz123-search-suggestion-items"),o=e.querySelector(".amz123-search-auto-main");globalThis.__isSynchronize=!1;let s="",c={};setInterval(()=>c={},1e3*60);const l=()=>{var u;a.classList.remove("active"),a.innerHTML="",e.style.position="unset",(u=document.querySelector("#temp-box"))==null||u.remove();const{x:p}=document.querySelector(".amz-search-box .amz-search-w").getBoundingClientRect();e.style.left=`${p}px`},i=()=>{var u,f;(u=o==null?void 0:o.classList)==null||u.remove("active"),e.style.position="unset",(f=document.querySelector("#temp-box"))==null||f.remove();const{x:p}=document.querySelector(".amz-search-box .amz-search-w").getBoundingClientRect();e.style.left=`${p}px`,document.querySelector(".amz-search-tips").style.display="flex"},d=p=>{var v,y;const u=Array.from(a.querySelectorAll(".suggestion-item"));if(globalThis.__isSynchronize=!0,u.length===0)return;const f=u.findIndex(k=>k.classList.contains("active"));let h;p?f===-1?h=u.length-1:f===0||(h=f-1):f===u.length-1?h=-1:h=f+1,u.forEach((k,m)=>{m===h?k.classList.add("active"):k.classList.remove("active")}),r.value=(y=(v=u[h])==null?void 0:v.dataset.name)!=null?y:s};if(globalThis.__resetActiveIndex=()=>{l(),i()},e){const p=mo(async u=>{var f,h,v,y,k;try{if(i(),globalThis.__isSynchronize)return;const m=Hn(!0),g=`${u}_${m}`,w=(y=c[g])!=null?y:await be.rawPost(Wu,{search_page_id:(f=globalThis==null?void 0:globalThis.__amz__)!=null&&f.__global_search_suggestion__?["global",(h=globalThis==null?void 0:globalThis.__amz__)==null?void 0:h.page_id]:[(v=globalThis==null?void 0:globalThis.__amz__)==null?void 0:v.page_id],search_value:u,search_bar:"nav",search_type:[+Hn(),0],path:location.pathname});if(c[g]=w,w.length===0){l();return}(k=document.querySelector("#temp-box"))==null||k.remove();const E=document.createElement("div"),z=document.querySelector(".amz-search-tips"),T=document.querySelector(".amz-search-box .amz-search-w");z?T.insertBefore(E,z):T.appendChild(E),E.outerHTML=`<div id="temp-box" style="width: ${t}px;height: ${n}px;opacity: 0"></div>`,e.style.position="fixed";const{x:O,width:q}=document.querySelector(".amz-search-box .amz-search-w").getBoundingClientRect(),{y:D}=document.querySelector("#temp-box").getBoundingClientRect();N(D),e.style.left=`${O}px`,e.style.top=`${D}px`,e.style.width=`${q}px`,a.classList.add("active"),a.innerHTML=w.slice(0,5).map(P=>{const{meta_data:{description:B,description_color:R,image:I,name:Y,name_color:U,url:G}}=P;return`
        <div onclick="javascript:globalThis.__isSynchronize = true;globalThis.__searchHotWordStatistic('${Y.trim()}');window.open('${G}', '_blank');" style="cursor: pointer;max-width:${t}px" data-name="${G}">
          <div class="suggestion-item" data-name="${Y}">        
          <img src="${I}"/>
            <section style="max-width: calc(100% - 106px)">
              <div style="color: ${U.length>0?U:"black"}">${Y}</div>
              <div style="color: ${R.length>0?R:"rgba(134, 142, 150, 1)"};overflow:hidden;white-space: nowrap;text-overflow: ellipsis;">${B}</div>
            </section>
            <span>\u7ACB\u5373\u8BBF\u95EE</span>
          </div>
        </div>
      `}).join("");const A=Array.from(a.querySelectorAll(".suggestion-item"));if(A.length>0){const P=A[0].dataset.name;u===P&&A[0].classList.add("active")}}catch(m){N(`\u641C\u7D22\u5EFA\u8BAE\u5F02\u5E38:${m}`)}},200);r.addEventListener("keyup",u=>{var h;u.stopPropagation();const f=(h=u.target.value)==null?void 0:h.trim();u.key==="ArrowUp"||u.key==="ArrowDown"?d(u.key==="ArrowUp"):f!==""&&globalThis.__amz__.__cancel__!==!0&&(s=f,globalThis.__isSynchronize=u.key==="Enter",p(u.target.value.trim()))}),r.addEventListener("compositionstart",()=>{globalThis.__amz__.__cancel__=!0}),r.addEventListener("compositionend",u=>{var h;const f=(h=u.target.value)==null?void 0:h.trim();f!==""&&(s=f,globalThis.__isSynchronize=u.key==="Enter",p(u.target.value.trim())),globalThis.__amz__.__cancel__=!1}),r.addEventListener("focus",u=>{var f;N("focus!!!",u.target.value),(f=u.target.value)!=null&&f.trim()?p(u.target.value.trim()):sa(e,o,t,n)}),r.addEventListener("keydown",u=>{u.key==="ArrowUp"&&u.preventDefault()}),r.addEventListener("click",u=>{u.stopPropagation(),a.classList.add("active")}),a.addEventListener("click",u=>u.stopPropagation()),document.addEventListener("click",()=>{var u;a.classList.remove("active"),(u=o==null?void 0:o.classList)==null||u.remove("active")}),r.addEventListener("input",u=>{u.target.value.trim()===""&&(l(),sa(e,o,t,n))}),document.querySelectorAll(".amz-search-text .amz-tag-name").forEach(u=>{u.addEventListener("click",f=>{f.stopPropagation(),r.focus(),N("class:",f.target.classList),!u.classList.toString().includes("amz-active_search_tag")&&l()})})}window.addEventListener("scroll",()=>{globalThis.__resetActiveIndex(),r.blur()})}catch(e){N("\u521D\u59CB\u5316\u641C\u7D22\u5EFA\u8BAE\u5931\u8D25\uFF1A",e)}finally{window.addEventListener("resize",()=>{globalThis.__resetActiveIndex()})}},bm=async e=>{try{let t=Zi(__surpriseData["1-4"]),n=null,r=[];if(t&&r.push(t),window.innerWidth>992){N("init text ad");const a=e?__surpriseData["1-2"]:__surpriseData["0-9"];n=Qi(a),n&&r.push(n)}r.length>0&&(globalThis==null?void 0:globalThis.__batchReport__)&&globalThis.__batchReport__(r)}catch(t){N("\u{1F916} \u521D\u59CB\u5316\u5E7F\u544A\u529F\u80FD\u5931\u8D25",t)}},_m=()=>{const e=document.querySelector("#bookmark_vue");e&&(e.style.marginTop=0);const t=document.querySelector(".site_collection_container");t&&(t.style.marginTop=0)};async function xm(){var e,t,n,r,a,o;try{const s=document.querySelector(".amz-exchange-rate");if(s===null){(e=document.querySelector(".amz-exchange-rate"))==null||e.remove();return}const c=s.dataset.currency,l=(t=s.dataset)==null?void 0:t.currencyReverse,i=(n=s.dataset)==null?void 0:n.currencyCode;let d;l&&(d=l.split("_"));const p=c.split("_");let u;i?(code_list=i.split("_"),u={code_list}):u={currency_list:p};const h=Math.floor(new Date().getTime()/1e3),v="amz123-toolbox",{updated_at:y,rows:k=[]}=await be.rawPost(Hu,u,{headers:{timestamp:h+"",sign:vo(v+h+JSON.stringify({})).toString().toLowerCase()}});if(k.length===0){(r=document.querySelector(".amz-exchange-rate"))==null||r.remove();return}const m=fn(),g={3:"https://www.amz123.com",5:"https://www.tt123.com",8:"https://www.dny123.com"},w=g[m]||g[3];if(c){const E=`
      <div class='amz-exchange-time amz-hover-orange'><a href="${w}/tools-rmbquot" target="_blank">\u5B9E\u65F6\u6C47\u7387&nbsp; (\u66F4\u65B0\u65F6\u95F4:<span class="amz-update-time"></span>)</a></div>
      <div class="amz-exchange-items">
      </div>
      `;s.innerHTML=E;const z=s.querySelector(".amz-update-time");z&&(z.innerText=y);const T=s.querySelector(".amz-exchange-items"),O=(a=s.dataset)==null?void 0:a.currencyAlisa;let q;if(O&&(q=O.split("_")),N(k),T){p.forEach((A,P)=>{const B=k.find(R=>R.currency===A);B.reverse=d&&d[P]||"0",B.currency=q&&q[P]!=="\u672A\u8BBE\u7F6E"&&q[P]||B.currency,p[P]=B});const D=p.reduce((A,P)=>{const B=(P==null?void 0:P.reverse)=="0"?`1${P.currency}=<span>${P.rate.toFixed(4)}</span>\u4EBA\u6C11\u5E01`:`1\u4EBA\u6C11\u5E01=<span>${(1/P.rate).toFixed(2)}</span>${P.currency}`;return`${A}<span class="amz-hover-orange"><a href="${(P==null?void 0:P.link)||"#"}" target="_blank">${B}</a></span>`},"");T.innerHTML=D}en(s,!0)}}catch(s){N("\u8BBE\u7F6E\u6C47\u7387\u5931\u8D25",s),(o=document.querySelector(".amz-exchange-rate"))==null||o.remove(),_m()}}async function Sm(){try{const e={3:"https://www.amz123.com",5:"https://www.tt123.com",8:"https://www.dny123.com"},t=fn(),n=e[t]||e[3],{rows:r}=await be.rawPost(Du),a=document.getElementById("amz-hot-info-flow");if(a){en(a);const o=r.reduce((s,c,l)=>`${s}<li><a data-sdk-report="1" data-sdk-index="${l}" href="${n}/t/${c.tid}" target="_blank">${c.subject}</a></li>`,"");a.innerHTML=`<ul class="amz-info-flow-ul" data-sdk-position="\u70ED\u70B9\u4FE1\u606F\u6D41">${o}</ul>`}}catch(e){N("\u8BBE\u7F6E\u70ED\u70B9\u4FE1\u606F\u6D41\u5931\u8D25",e)}}const zm=async()=>{try{const t=fn();globalThis.__navApp__={appId:t,projectId:"index"},ym(),Sm(),gm(),xm(),document.querySelector("#bookmark_vue")&&(await vm(),fm())}catch(t){N("\u5F02\u6B65\u4EFB\u52A1\u5F02\u5E38:",t)}finally{let r=function(a){t&&clearTimeout(t),a.target.className.includes(n)||(a.target.className+=n),t=setTimeout(()=>{a.target.className=a.target.className.replace(n,"")},800)};var e=r;let t;const n="toggle-scroll-visible";document.body.addEventListener("scroll",r)}},Em=async()=>{var e;try{const t=(e=globalThis.__amz__)==null?void 0:e.search_page_name;await Hs(t),bm(t==="/")}catch(t){console.error(t)}};window.addEventListener("DOMContentLoaded",()=>{zm(),Em(),wm()});window.addEventListener("DOMContentLoaded",()=>{try{ys()}catch(e){N("\u52A0\u8F7D\u4FA7\u8FB9\u5DE5\u5177\u680F\u5931\u8D25",e)}});window.addEventListener("DOMContentLoaded",()=>{try{document.body.clientWidth<992&&Vs()}catch(e){N("\u79FB\u52A8\u7AEF\u5934\u90E8\u8BB0\u8F7D\u51FA\u9519",e)}});function km(){const e=/(Android|webOS|iPhone|iPod|tablet|BlackBerry|Mobile)/i.test(navigator.userAgent),t=!/(Android|webOS|iPhone|iPod|tablet|BlackBerry|Mobile)/i.test(navigator.userAgent),n=window.location.hostname,r=window.location.pathname;e&&n==="amz123.cc"&&(window.location.href=r==="/"?"http://m.amz123.cc":"http://m.amz123.cc"+r),t&&n==="m.amz123.cc"&&(window.location.href=r==="/"?"http://amz123.cc":"http://amz123.cc"+r),e&&n==="www.amz123.com"&&(window.location.href=r==="/"?"https://m.amz123.com":"https://m.amz123.com"+r),t&&n==="m.amz123.com"&&(window.location.href=r==="/"?"https://www.amz123.com":"https://www.amz123.com"+r)}window.addEventListener("DOMContentLoaded",()=>{try{km()}catch(e){showDebug("\u79FB\u52A8\u7AEF\u94FE\u63A5\u66FF\u6362\u9519\u8BEF",e)}});const Cm="sdk",Lm="1.1.0",Tm="module",Om="amz123",qm={dev:"vite",build:"vite build",preview:"vite preview",upload:"yarn install && yarn build && bash upload.sh",forceUpload:"rm -rf node_modules && yarn && yarn upload",test:"mocha"},Pm={mocha:"^10.1.0",vite:"^3.0.7","vite-plugin-banner":"^0.7.0","vite-plugin-html":"^3.2.0","vite-plugin-remove-console":"^1.1.0"},Am={axios:"^1.2.2","crypto-js":"^4.1.1","date-fns":"^2.29.2","date-fns-tz":"^1.3.6","include-media":"^1.4.10",ismobilejs:"^1.1.1","lodash-es":"^4.17.21","nav-public-utils":"2.4.91",sass:"^1.54.5"},Nm={name:Cm,private:!0,version:Lm,type:Tm,author:Om,scripts:qm,devDependencies:Pm,dependencies:Am};window.addEventListener("DOMContentLoaded",()=>{const e=document.querySelector('meta[charset="utf-8"]'),t=Nm.dependencies["nav-public-utils"],n=document.createElement("meta");n.setAttribute("name","public-utils"),t.includes("^")?n.setAttribute("content",t.split("^")[1]):n.setAttribute("content",t),e.insertAdjacentElement("afterend",n)});const Mm=()=>{try{const e=navigator.userAgent,t=document.getElementsByClassName("amz123-collection-button");if(t.length>0){const n=t[0];e.indexOf("Windows")!==-1&&(n.textContent="Ctrl"),e.indexOf("Mac")!==-1&&(n.textContent="Command")}}catch{}};window.addEventListener("DOMContentLoaded",Mm);const Dm=()=>{const e=document.createElement("style");e.textContent=`
.shop-modal-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    will-change: opacity;
}

.shop-qrcode-box {

}
.shop-modal-all {
    background-color: #fff;
    border-radius: 8px;
}
.shop-modal-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(180deg, #afc3ff, hsla(0, 0%, 100%, 0));
    padding: 20px;
    border-radius: 8px;
    position: relative;
    width: 242px;
    height: 242px;
    overflow: auto;
    opacity: 0;
}
.shop-modal-mask.active {
    opacity: 1;
}
.shop-modal-mask.active .shop-modal-content {
    transform: translateY(0);
    opacity: 1;
}

.shop-close-btn {
    position: absolute;
    right: 10px;
    top: 10px;
    cursor: pointer;
    font-size: 20px;
}

.shop-qrcode-img {
    max-width: 128px;
    max-height: 128px;
    display: block;
    opacity: 0;
    border-radius: 4px;
    transition: opacity 0.2s 0.2s;
}
.shop-tip-text {
    font-size: 14px;
    font-weight: 500;
    color: #111827;
    text-align: center;
    height: auto;
    line-height: 22px;
    border-bottom: 0;
    margin-top: 10px;
    padding: 0;
}
.shop-qrcode-img.loaded {
    opacity: 1;
}
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #ddd;
    border-radius: 50%;
    border-top-color: #666;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}
@keyframes spin {
    to { transform: rotate(360deg); }
}
`,document.head.appendChild(e);const t=document.querySelectorAll('[id="new-open-shop"]');function n(a,o){const s=document.createElement("div");s.className="shop-modal-mask";const c=document.createElement("div");c.className="shop-modal-all";const l=document.createElement("div");l.className="shop-modal-content";const i=document.createElement("span");i.className="shop-close-btn",i.innerHTML="\xD7",i.onclick=()=>r(s);const d=document.createElement("div");d.className="loading-spinner";const p=document.createElement("div");p.className="shop-qrcode-box";const u=document.createElement("img");u.className="shop-qrcode-img",u.onload=()=>{u.classList.add("loaded"),d.remove()},u.onerror=()=>{d.textContent="\u56FE\u7247\u52A0\u8F7D\u5931\u8D25",d.style.animation="none"},u.src=a;const f=document.createElement("div");return f.textContent=o,f.className="shop-tip-text",p.appendChild(d),p.appendChild(u),l.appendChild(i),l.appendChild(p),l.appendChild(f),c.appendChild(l),s.appendChild(c),setTimeout(()=>{s.classList.add("active")},10),s}function r(a){a.classList.remove("active"),a.addEventListener("transitionend",()=>{a.remove()},{once:!0})}t&&t.forEach(a=>{a.addEventListener("click",function(o){o.preventDefault(),o.stopPropagation();const s=this.dataset.shopUrl,c=this.dataset.shopTip,l=n(s,c);document.body.appendChild(l),l.addEventListener("click",function(i){i.target===this&&document.body.removeChild(l)})})}),document.addEventListener("error",function(a){a.target.tagName==="IMG"&&a.target.classList.contains("shop-qrcode-img")&&(a.target.parentNode.innerHTML='<div style="color:red">\u56FE\u7247\u52A0\u8F7D\u5931\u8D25</div>')},!0),document.addEventListener("keydown",function(a){a.key==="Escape"&&document.querySelector(".shop-modal-mask")&&document.querySelectorAll(".shop-modal-mask").forEach(o=>o.remove())})};window.addEventListener("DOMContentLoaded",Dm);function Im(e){let t=e.filter(a=>Number(a.getAttribute("data-sdk-pinned"))!==1);for(let a=t.length-1;a>0;a--){const o=Math.floor(Math.random()*(a+1));[t[a],t[o]]=[t[o],t[a]]}let n=0;return e.map(a=>Number(a.getAttribute("data-sdk-pinned"))!==1?t[n++]:a)}window.addEventListener("DOMContentLoaded",()=>{try{const e=document.querySelectorAll("#random-top");if(!e||(e==null?void 0:e.length)===0)return;e.forEach(t=>{const n=t.children,r=Array.prototype.slice.call(n),a=Im(r);t.innerHTML="",a.forEach(o=>{t.appendChild(o)})})}catch(e){console.error("\u5931\u8D25",e)}});const Rm=()=>{const e=document.querySelector(".amz-activity-content-wrapper"),t=e&&e.querySelectorAll(".activity-recent-item");t&&Array.from(t).forEach(r=>{const a=r.querySelector(".activity-recent-day");if(a){const o=r.getAttribute("data-start-time"),s=r.getAttribute("data-end-time"),c=$m(o,s);a.style.background=c.color,a.innerHTML=c.content}})},$m=(e,t)=>{const n=e*1e3,r=t*1e3,a=Date.now(),o=(n-a)/(1e3*3600*24),s=(r-a)/(1e3*3600*24),c=Bm(n,a);let l="",i="";return c&&o>0?(l="\u5373\u5C06\u5F00\u59CB",i="#FCBD00"):o<=0&&s>=0?(l="\u6D3B\u52A8\u8FDB\u884C\u4E2D",i="#FF4470"):o>0&&!c?(l=`\u5012\u8BA1\u65F6${Math.ceil(o)}\u5929`,i="#2ECC75"):s<0&&(l="\u6D3B\u52A8\u5DF2\u7ED3\u675F",i="#989DA2"),{color:i,content:l}},Bm=(e,t)=>{const n=new Date(e),r=new Date(t);return n.getFullYear()===r.getFullYear()&&n.getMonth()===r.getMonth()&&n.getDate()===r.getDate()};window.addEventListener("DOMContentLoaded",Rm);