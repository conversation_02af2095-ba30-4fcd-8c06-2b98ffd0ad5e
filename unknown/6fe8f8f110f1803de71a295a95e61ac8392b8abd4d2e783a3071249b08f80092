import { defineStore } from 'pinia'

interface Activity {
  id: number
  title: string
  banner: string
  description: string
  startDate: string
  endDate: string
}

export const useActivityStore = defineStore('activity', {
  state: () => ({
    activityList: [] as Activity[],
    moreActivities: [] as Activity[],
    loading: false
  }),
  
  actions: {
    // SSG阶段获取首屏活动数据
    async fetchActivityList(): Promise<Activity[]> {
      try {
        this.loading = true
        // 模拟API请求
        const data = await new Promise<Activity[]>(resolve => {
          setTimeout(() => {
            resolve([
              { 
                id: 1, 
                title: '双11促销活动', 
                banner: 'https://placeholder.pics/svg/800x300/DEDEDE/555555/双11活动',
                description: '年度最大促销活动',
                startDate: '2024-11-01',
                endDate: '2024-11-15'
              },
              { 
                id: 2, 
                title: '新品发布会', 
                banner: 'https://placeholder.pics/svg/800x300/DEDEDE/555555/新品发布',
                description: '了解最新产品动态',
                startDate: '2024-11-20',
                endDate: '2024-11-20'
              }
            ])
          }, 300)
        })
        this.activityList = data
        return data
      } catch (error) {
        console.error('获取活动数据失败', error)
        return []
      } finally {
        this.loading = false
      }
    },
    
    // 客户端动态加载更多活动
    async fetchMoreActivities(): Promise<Activity[]> {
      try {
        this.loading = true
        // 模拟API请求
        const data = await new Promise<Activity[]>(resolve => {
          setTimeout(() => {
            resolve([
              { 
                id: 3, 
                title: '会员专享日', 
                banner: 'https://placeholder.pics/svg/800x300/DEDEDE/555555/会员专享',
                description: '会员独享特别优惠',
                startDate: '2024-12-01',
                endDate: '2024-12-03'
              },
              { 
                id: 4, 
                title: '年终盛典', 
                banner: 'https://placeholder.pics/svg/800x300/DEDEDE/555555/年终盛典',
                description: '年度最后一次大促',
                startDate: '2024-12-20',
                endDate: '2024-12-31'
              }
            ])
          }, 500)
        })
        this.moreActivities = data
        return data
      } catch (error) {
        console.error('获取更多活动失败', error)
        return []
      } finally {
        this.loading = false
      }
    }
  }
}) 