<template>
  <section class="bg-gray-100 rounded-lg border border-gray-200 p-2 -mt-1">
    <div class="flex items-center justify-between w-full overflow-x-auto">
      <!-- 左侧：实时汇率标题和更新时间 -->
      <div class="text-xs text-gray-600 flex-shrink-0 mr-2">
        实时汇率 (更新时间:<span class="text-orange-600">2025-06-18 13:25:31</span>)
      </div>
      <!-- 右侧：汇率信息 -->
      <div class="flex items-center gap-x-4 flex-1 justify-end whitespace-nowrap">
        <span class="text-xs text-gray-600">
          1人民币=<span class="text-orange-600">4.11</span>新台币
        </span>
        <span class="text-xs text-gray-600">
          1人民币=<span class="text-orange-600">2272.73</span>印尼盾
        </span>
        <span class="text-xs text-gray-600">
          1人民币=<span class="text-orange-600">4.53</span>泰铢
        </span>
        <span class="text-xs text-gray-600">
          1人民币=<span class="text-orange-600">7.91</span>菲律宾比索
        </span>
        <span class="text-xs text-gray-600">
          1人民币=<span class="text-orange-600">3623.19</span>越南盾
        </span>
        <span class="text-xs text-gray-600">
          1马来西亚林吉特=<span class="text-orange-600">1.6925</span>人民币
        </span>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// 可以在这里添加汇率数据的获取和更新逻辑
</script>

<style scoped>
.flex-1 {
  flex: 1 1 0%;
}
</style> 