# Nuxt3 SSG 示例项目

这是一个使用Nuxt3框架构建的静态站点生成(SSG)示例项目，结合了Vue3、Pinia和TailwindCSS。

## 项目特点

- 使用Nuxt3框架进行SSG静态站点生成
- Vue3作为前端框架
- Pinia用于状态管理
- TailwindCSS用于样式设计
- 多页面应用（首页和活动页）
- 首屏数据SSG预渲染，后续内容客户端动态加载

## 项目结构

```
├── assets            # 静态资源
│   └── css           # CSS文件
├── components        # 组件
├── layouts           # 布局组件
├── pages             # 页面组件
├── public            # 公共资源
├── stores            # Pinia状态管理
└── nuxt.config.ts    # Nuxt配置
```

## 页面说明

1. 首页 (`/`): 
   - 导航入口展示
   - 首屏内容由SSG生成
   - 更多内容通过客户端动态请求

2. 活动页 (`/activity`): 
   - 展示活动列表
   - 首屏活动由SSG生成
   - 更多活动通过客户端动态加载

## 开发命令

```bash
# 开发模式
npm run dev

# 构建静态站点
npm run generate

# 预览静态站点
npm run preview
```

## 技术实现

- 通过Pinia构建数据获取逻辑，实现数据与UI分离
- 使用`useAsyncData`进行SSG数据预获取
- 客户端使用Pinia actions获取动态数据
- 组件化设计，复用UI元素
- 响应式布局适配不同设备
